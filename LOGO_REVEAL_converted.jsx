// Logo 1 - Converted from Lottie JSON
// Original Bodymovin version: 5.12.1
// Generated by <PERSON><PERSON> to JSX Converter

// Disable undo for performance
app.beginUndoGroup('Create Animation');

// Helper Functions
function setKeyframes(property, keyframes, frameRate) {
    if (!keyframes || !keyframes.length) return;
    
    for (var i = 0; i < keyframes.length; i++) {
        var kf = keyframes[i];
        var time = kf.t / frameRate;
        
        property.setValueAtTime(time, kf.s);
        
        // Set easing if available
        if (kf.i && kf.o) {
            var keyIndex = property.nearestKeyIndex(time);
            try {
                // Convert Lottie easing to After Effects easing
                var inEase = convertLottieEasing(kf.i);
                var outEase = convertLottieEasing(kf.o);
                property.setTemporalEaseAtKey(keyIndex, inEase, outEase);
            } catch(e) {
                // Ignore easing errors
            }
        }
    }
}

function convertLottieEasing(easing) {
    // Convert Lottie easing values to After Effects KeyframeEase
    if (!easing || !easing.length) return new KeyframeEase(0, 33.33);
    
    var influence = 33.33; // Default influence
    var speed = 0;
    
    if (easing.length >= 2) {
        // Lottie uses normalized values, AE uses different ranges
        speed = (easing[0] || 0) * 100; // Convert to percentage
        influence = Math.max(0.1, Math.min(100, (easing[1] || 0.33) * 100));
    }
    
    return new KeyframeEase(speed, influence);
}

function setKeyframeWithEasing(property, time, value, inEasing, outEasing, frameRate) {
    // Set keyframe value
    property.setValueAtTime(time / frameRate, value);
    
    // Apply easing if provided
    if (inEasing || outEasing) {
        try {
            var keyIndex = property.nearestKeyIndex(time / frameRate);
            if (inEasing && outEasing) {
                var inEase = convertLottieEasing(inEasing);
                var outEase = convertLottieEasing(outEasing);
                property.setTemporalEaseAtKey(keyIndex, [inEase], [outEase]);
            }
        } catch(e) {
            // Ignore easing errors
        }
    }
}

function createSolidLayer(comp, name, color, width, height) {
    return comp.layers.addSolid(color, name, width, height, 1.0);
}

function createShapeLayer(comp, name) {
    var layer = comp.layers.addShape();
    layer.name = name;
    return layer;
}

function createNullLayer(comp, name) {
    var layer = comp.layers.addNull();
    layer.name = name;
    return layer;
}

function createTextLayer(comp, name) {
    var layer = comp.layers.addText(name);
    return layer;
}

function createPrecomp(comp, name, refId) {
    // This would reference a precomp - simplified for now
    return createNullLayer(comp, name + ' (Precomp)');
}

function rgbToAE(r, g, b, a) {
    a = a || 1;
    return [r, g, b, a];
}

function bezierToAE(bezierData) {
    var shape = new Shape();
    if (bezierData.v && bezierData.v.length > 0) {
        shape.vertices = bezierData.v;
        if (bezierData.i) shape.inTangents = bezierData.i;
        if (bezierData.o) shape.outTangents = bezierData.o;
        shape.closed = bezierData.c || false;
    }
    return shape;
}

// === INTELLIGENT ASSET IMPORT WITH CONTEXT-AWARE DIALOGS ===
function importAssetWithDialog(comp, assetId, originalPath, assetType, assetName, dimensions, description) {
    // Try original path first
    if (originalPath && originalPath !== '') {
        var originalFile = new File(originalPath);
        if (originalFile.exists) {
            return importAssetFile(comp, assetId, originalFile, assetName);
        }
    }
    
    // Build hyper-intelligent dialog with detailed context
    var message = '🚨 MISSING ASSET REQUIRED\n';
    message += '════════════════════════════════════════\n\n';
    
    // Asset identification section
    message += '📋 WHAT FILE IS NEEDED:\n';
    message += '• Asset Name: "' + (assetName || assetId) + '"\n';
    message += '• File Type: ' + assetType.toUpperCase() + '\n';
    message += '• Asset ID: ' + assetId + '\n';
    
    if (dimensions && dimensions !== '') {
        message += '• Expected Size: ' + dimensions + ' pixels\n';
    }
    
    message += '• Original Path: ' + (originalPath || 'Not specified') + '\n';
    
    // Context and purpose section
    if (description && description !== '') {
        message += '\n🎯 PURPOSE & CONTEXT:\n';
        message += '• ' + description + '\n';
    }
    
    // Smart guidance based on asset type and name
    message += '\n💡 WHAT TO LOOK FOR:\n';
    if (assetType === 'image') {
        message += '• Look for PNG, JPG, or other image files\n';
        if (assetName && assetName.toLowerCase().indexOf('logo') !== -1) {
            message += '• This should be your company/brand logo\n';
            message += '• Usually a PNG with transparent background\n';
        } else if (assetName && assetName.toLowerCase().indexOf('background') !== -1) {
            message += '• This should be a background image\n';
            message += '• Usually covers the full composition size\n';
        } else if (assetName && assetName.toLowerCase().indexOf('placeholder') !== -1) {
            message += '• This is a placeholder - replace with your image\n';
            message += '• Can be any image that fits your design\n';
        }
    } else if (assetType === 'audio') {
        message += '• Look for MP3, WAV, or other audio files\n';
        if (assetName && assetName.toLowerCase().indexOf('music') !== -1) {
            message += '• This should be background music\n';
            message += '• Usually a longer audio track\n';
        } else {
            message += '• This should be a sound effect or audio clip\n';
            message += '• Usually a shorter audio file\n';
        }
    } else if (assetType === 'video') {
        message += '• Look for MP4, MOV, or other video files\n';
        message += '• Should match the expected dimensions if specified\n';
    }
    
    message += '\n📂 NEXT STEPS:\n';
    message += '• Click OK to open file browser and locate the file\n';
    message += '• Click Cancel to create a placeholder instead\n';
    message += '\n⚠️  If you can\'t find the exact file, choose a similar one or cancel for placeholder.';
    
    var userChoice = confirm(message);
    if (!userChoice) {
        return createPlaceholderLayer(comp, assetId, assetName, assetType, dimensions);
    }
    
    // Show file dialog with smart filters and detailed title
    var fileFilter = getSmartFileFilter(assetType);
    var dialogTitle = 'SELECT ' + assetType.toUpperCase() + ': ' + (assetName || assetId);
    if (description) {
        dialogTitle += ' (' + description + ')';
    }
    
    var selectedFile = File.openDialog(dialogTitle, fileFilter);
    
    if (selectedFile && selectedFile.exists) {
        return importAssetFile(comp, assetId, selectedFile, assetName);
    } else {
        alert('❌ No file selected. Creating placeholder layer instead.');
        return createPlaceholderLayer(comp, assetId, assetName, assetType, dimensions);
    }
}

function importAssetFile(comp, assetId, file, assetName) {
    try {
        var importOptions = new ImportOptions(file);
        var footage = app.project.importFile(importOptions);
        var layer = comp.layers.add(footage);
        layer.name = assetName || assetId;
        return layer;
    } catch (e) {
        alert('Error importing file: ' + file.fsName + '\nError: ' + e.toString() + '\nCreating placeholder layer.');
        return createNullLayer(comp, (assetName || assetId) + ' (Import Error)');
    }
}

function createPlaceholderLayer(comp, assetId, assetName, assetType, dimensions) {
    var placeholderName = (assetName || assetId) + ' [MISSING ' + assetType.toUpperCase() + ']';
    
    // Create different placeholder types based on asset type
    if (assetType === 'image') {
        // Create a colored solid as image placeholder
        var width = 500, height = 500;
        if (dimensions) {
            var parts = dimensions.split('x');
            if (parts.length === 2) {
                width = parseInt(parts[0]) || 500;
                height = parseInt(parts[1]) || 500;
            }
        }
        var placeholder = comp.layers.addSolid([0.8, 0.2, 0.2], placeholderName, width, height, 1.0);
        return placeholder;
    } else if (assetType === 'audio') {
        // Create null layer for audio placeholder
        var placeholder = comp.layers.addNull();
        placeholder.name = placeholderName;
        return placeholder;
    } else if (assetType === 'video') {
        // Create solid for video placeholder
        var placeholder = comp.layers.addSolid([0.2, 0.2, 0.8], placeholderName, 1920, 1080, 1.0);
        return placeholder;
    } else {
        // Generic null layer placeholder
        var placeholder = comp.layers.addNull();
        placeholder.name = placeholderName;
        return placeholder;
    }
}

function getSmartFileFilter(assetType) {
    switch (assetType.toLowerCase()) {
        case 'image':
            return 'Image Files:*.png;*.jpg;*.jpeg;*.gif;*.bmp;*.tiff;*.tga;*.psd;*.ai;*.eps';
        case 'video':
            return 'Video Files:*.mp4;*.mov;*.avi;*.wmv;*.flv;*.webm;*.mkv;*.m4v;*.3gp';
        case 'audio':
            return 'Audio Files:*.mp3;*.wav;*.aac;*.ogg;*.flac;*.m4a;*.wma;*.aiff';
        default:
            return 'All Files:*.*';
    }
}

function getFileTypesForAsset(assetType) {
    switch (assetType.toLowerCase()) {
        case 'image':
            return 'Images:*.png;*.jpg;*.jpeg;*.gif;*.bmp;*.tiff;*.psd';
        case 'video':
            return 'Videos:*.mp4;*.mov;*.avi;*.wmv;*.flv;*.webm;*.mkv';
        case 'audio':
            return 'Audio:*.mp3;*.wav;*.aac;*.ogg;*.flac;*.m4a';
        default:
            return 'All Files:*.*';
    }
}

// Create main composition: Logo 1
var comp = app.project.items.addComp('Logo 1', 1920, 1080, 1.0, 5.000, 24);
var frameRate = 24;

// Create Color Control adjustment layer
var colorControlLayer = comp.layers.addSolid([1, 1, 1], 'Color Control', comp.width, comp.height, 1);
colorControlLayer.adjustmentLayer = true;
colorControlLayer.enabled = false; // Hidden layer for controls only

// Add Circle effect
var circleEffect = colorControlLayer.property('Effects').addProperty('ADBE Color Control');
circleEffect.name = 'Circle';
circleEffect.property('Color').setValue([1.0, 1.0, 1.0, 1.0]);

// Add Tagline effect
var taglineEffect = colorControlLayer.property('Effects').addProperty('ADBE Color Control');
taglineEffect.name = 'Tagline';
taglineEffect.property('Color').setValue([1.0, 1.0, 1.0, 1.0]);

// Add Line Burst  effect
var line_burst_Effect = colorControlLayer.property('Effects').addProperty('ADBE Color Control');
line_burst_Effect.name = 'Line Burst ';
line_burst_Effect.property('Color').setValue([0.662745118141, 0.909803926945, 0.270588248968, 1]);

// Add Logo effect
var logoEffect = colorControlLayer.property('Effects').addProperty('ADBE Color Control');
logoEffect.name = 'Logo';
logoEffect.property('Color').setValue([1.0, 1.0, 1.0, 1.0]);

// Add Tagline Scale effect
var tagline_scaleEffect = colorControlLayer.property('Effects').addProperty('ADBE Slider Control');
tagline_scaleEffect.name = 'Tagline Scale';
tagline_scaleEffect.property('Slider').setValue(100);

// Add Bg effect
var bgEffect = colorControlLayer.property('Effects').addProperty('ADBE Color Control');
bgEffect.name = 'Bg';
bgEffect.property('Color').setValue([0.12156862745098039, 0.11372549019607843, 0.08627450980392157, 1.0]);

// Add Center Position effect
var center_positionEffect = colorControlLayer.property('Effects').addProperty('ADBE Point Control');
center_positionEffect.name = 'Center Position';
center_positionEffect.property('Point').setValue([960, 540]);

// Add Lines effect
var linesEffect = colorControlLayer.property('Effects').addProperty('ADBE Color Control');
linesEffect.name = 'Lines';
linesEffect.property('Color').setValue([1.0, 1.0, 1.0, 1.0]);

// Add Tagline Position effect
var tagline_positionEffect = colorControlLayer.property('Effects').addProperty('ADBE Point Control');
tagline_positionEffect.name = 'Tagline Position';
tagline_positionEffect.property('Point').setValue([475, 5]);


// Asset References and Precomps
// Asset: image_0 (image)
// Asset: audio_0 (image)
// Create precomp: Audio 1
var comp_0 = app.project.items.addComp('Audio 1', 1920, 1080, 1.0, 5.000, 24);

// Processing 2 layers in Audio 1
// Reversing JSON layer order for correct After Effects stacking
// Audio Logo 1 (index 2)
// Audio Logo 1 (index 1)
// Layer 2: Audio Logo 1
var layer_2 = createNullLayer(comp_0, 'Audio Logo 1 (Missing Precomp: comp_1)');
// Warning: Precomp comp_1 not found
layer_2.inPoint = 0.291667;
layer_2.outPoint = 2.000000;
layer_2.startTime = 0.291667;
layer_2.property('Transform').property('Opacity').setValue(100);
layer_2.property('Transform').property('Position').setValue([960, 540, 0]);
layer_2.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_2.property('Transform').property('Rotation').setValue(0);
layer_2.property('Transform').property('Anchor Point').setValue([960, 540, 0]);

// Layer 1: Audio Logo 1
var layer_1 = createNullLayer(comp_0, 'Audio Logo 1 (Missing Precomp: comp_1)');
// Warning: Precomp comp_1 not found
layer_1.inPoint = 2.000000;
layer_1.outPoint = 5.000000;
layer_1.startTime = -1.458333;
layer_1.property('Transform').property('Opacity').setValue(100);
layer_1.property('Transform').property('Position').setValue([960, 540, 0]);
layer_1.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_1.property('Transform').property('Rotation').setValue(0);
layer_1.property('Transform').property('Anchor Point').setValue([960, 540, 0]);


// Create precomp: Audio Logo 1
var comp_1 = app.project.items.addComp('Audio Logo 1', 1920, 1080, 1.0, 6.478, 24);

// Processing 1 layers in Audio Logo 1
// Reversing JSON layer order for correct After Effects stacking
// Audio L1.mp3 (index 1)
// Layer 1: Audio L1.mp3
// Audio Layer: Audio L1.mp3 (Asset: audio_0)
var layer_1 = importAssetWithDialog(comp_1, 'audio_0', 'images/aud_0.mp3', 'audio', 'Audio L1.mp3', '', 'Media asset for the animation');
layer_1.inPoint = 0.000000;
layer_1.outPoint = 6.477982;
layer_1.startTime = 0.000000;


// Create precomp: Logo
var comp_2 = app.project.items.addComp('Logo', 1920, 1080, 1.0, 11.000, 24);

// Processing 1 layers in Logo
// Reversing JSON layer order for correct After Effects stacking
// Logo_Placeholder.png (index 1)
// Layer 1: Logo_Placeholder.png
// Image Layer: Logo_Placeholder.png (Asset: image_0)
var layer_1 = importAssetWithDialog(comp_2, 'image_0', 'images/img_0.png', 'image', 'Logo_Placeholder.png', '500x500', 'Company or brand logo image');
layer_1.inPoint = 0.000000;
layer_1.outPoint = 11.000000;
layer_1.startTime = 0.000000;
layer_1.property('Transform').property('Opacity').setValue(100);
layer_1.property('Transform').property('Position').setValue([250, 250, 0]);
layer_1.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_1.property('Transform').property('Rotation').setValue(0);
layer_1.property('Transform').property('Anchor Point').setValue([250, 250, 0]);


// Create precomp: Tagline
var comp_3 = app.project.items.addComp('Tagline', 1920, 1080, 1.0, 11.125, 24);

// Processing 1 layers in Tagline
// Reversing JSON layer order for correct After Effects stacking
// WWW.ENVATO.COM (index 1)
// Layer 1: WWW.ENVATO.COM
var layer_1 = createTextLayer(comp_3, 'WWW.ENVATO.COM');
var textDoc = layer_1.property('Source Text');
var textValue = textDoc.value;
textValue.text = 'WWW.ENVATO.COM';
textValue.font = 'Calibri';
textValue.fontSize = 50;
textValue.fillColor = [1.000000, 1.000000, 1.000000];
textValue.justification = ParagraphJustification.LEFT_JUSTIFY;
textDoc.setValue(textValue);
layer_1.inPoint = 0.000000;
layer_1.outPoint = 11.125000;
layer_1.startTime = -6.125000;
layer_1.property('Transform').property('Opacity').setValue(100);
layer_1.property('Transform').property('Position').setValue([23, 183, 0]);
layer_1.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_1.property('Transform').property('Rotation').setValue(0);
layer_1.property('Transform').property('Anchor Point').setValue([-2, -33, 0]);



// Font References
// Font: Calibri (Calibri)

// Creating 17 layers
// Reversing JSON layer order for correct After Effects stacking
// BG  (index 18)
// Tagline (index 17)
// Tagline Mask (index 16)
// Line Split (index 15)
// Tagline Transform Null (index 14)
// Line 1 (index 13)
// Line 2 (index 12)
// Line 3 (index 11)
// Line 4 (index 10)
// Circle 1 (index 9)
// Logo (index 8)
// Logo Mask (index 7)
// Line Burst 1 (index 6)
// Line Burst 2 (index 5)
// Master (index 4)
// Center Position (index 3)
// Audio 1 (index 1)
// Layer 18: BG 
var layer_18 = createSolidLayer(comp, 'BG ', [0.121569, 0.113725, 0.086275], 1920, 1080);
// Processing 1 effects for layer_18
// Effect 0: Fill (type: 21)
// Fill Effect: Fill
var effect_0 = layer_18.property('Effects').addProperty('ADBE Fill');
effect_0.name = 'Fill';
effect_0.property('Fill Mask').setValue(0);
effect_0.property('All Masks').setValue(0);
// Expression for Color
try {
    var effectProperty = effect_0.property('Color');
    if (effectProperty.canSetExpression) {
        effectProperty.expression = 'thisComp.layer(\'Color Control\').effect(\'Bg\')(\'Color\')';
        effectProperty.expressionEnabled = true;
    }
} catch (e) {
    // Effect property expression failed: thisComp.layer(\'Color Control\').effect(\'Bg\')(\'Color\')
}
effect_0.property('Invert').setValue(0);
effect_0.property('Horizontal Feather').setValue(0);
effect_0.property('Vertical Feather').setValue(0);
effect_0.property('Opacity').setValue(1);
layer_18.inPoint = 0.000000;
layer_18.outPoint = 5.000000;
layer_18.startTime = 0.000000;
layer_18.property('Transform').property('Opacity').setValue(100);
layer_18.property('Transform').property('Position').setValue([960, 540, 0]);
layer_18.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_18.property('Transform').property('Rotation').setValue(0);
layer_18.property('Transform').property('Anchor Point').setValue([960, 540, 0]);

// Layer 3: Center Position
var layer_3 = createNullLayer(comp, 'Center Position');
// Expression for Position on Center Position
try {
    var posProperty = layer_3.property('Transform').property('Position');
    if (posProperty.canSetExpression) {
        posProperty.expression = '[ thisComp.layer(\'Color Control\').effect(\'Center Position\')(\'Point\')[0], thisComp.layer(\'Color Control\').effect(\'Center Position\')(\'Point\')[1] ]';
        posProperty.expressionEnabled = true;
    }
} catch (e) {
    // Position expression failed: [ thisComp.layer(\'Color Control\').effect(\'Center Position\')(\'Point\')[0], thisComp.layer(\'Color Control\').effect(\'Center Position\')(\'Point\')[1] ]
}
layer_3.inPoint = 0.000000;
layer_3.outPoint = 5.000000;
layer_3.startTime = 0.000000;
layer_3.property('Transform').property('Opacity').setValue(0);
layer_3.property('Transform').property('Position').setValue([994, 540, 0]);
layer_3.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_3.property('Transform').property('Rotation').setValue(0);
layer_3.property('Transform').property('Anchor Point').setValue([0, 0, 0]);

// Layer 14: Tagline Transform Null
var layer_14 = createNullLayer(comp, 'Tagline Transform Null');
// Expression for Position on Tagline Transform Null
try {
    var posProperty = layer_14.property('Transform').property('Position');
    if (posProperty.canSetExpression) {
        posProperty.expression = '[ thisComp.layer(\'Color Control\').effect(\'Tagline Position\')(\'Point\')[0], thisComp.layer(\'Color Control\').effect(\'Tagline Position\')(\'Point\')[1] ]';
        posProperty.expressionEnabled = true;
    }
} catch (e) {
    // Position expression failed: [ thisComp.layer(\'Color Control\').effect(\'Tagline Position\')(\'Point\')[0], thisComp.layer(\'Color Control\').effect(\'Tagline Position\')(\'Point\')[1] ]
}
// Expression for Scale on Tagline Transform Null
try {
    var scaleProperty = layer_14.property('Transform').property('Scale');
    if (scaleProperty.canSetExpression) {
        scaleProperty.expression = '[ value, value ]';
        scaleProperty.expressionEnabled = true;
    }
} catch (e) {
    // Scale expression failed: [ value, value ]
}
layer_14.inPoint = 0.000000;
layer_14.outPoint = 5.000000;
layer_14.startTime = 0.000000;
layer_14.parent = layer_3;
layer_14.property('Transform').property('Opacity').setValue(0);
layer_14.property('Transform').property('Position').setValue([-184, 0, 0]);
layer_14.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_14.property('Transform').property('Rotation').setValue(0);
layer_14.property('Transform').property('Anchor Point').setValue([0, 0, 0]);

// Layer 4: Master
var layer_4 = createNullLayer(comp, 'Master');
layer_4.inPoint = 0.000000;
layer_4.outPoint = 5.000000;
layer_4.startTime = 0.000000;
layer_4.parent = layer_3;
layer_4.property('Transform').property('Opacity').setValue(0);
// Position has separate dimensions - combining for AE
setKeyframeWithEasing(layer_4.property('Transform').property('Position'), 45, [0, 0], {'x': [0.833], 'y': [0.833]}, {'x': [0.167], 'y': [0.167]}, frameRate);
layer_4.property('Transform').property('Position').setValueAtTime(52/frameRate, [-144, 0]);
layer_4.property('Transform').property('Scale').setValue([75, 75, 100]);
layer_4.property('Transform').property('Rotation').setValue(0);
layer_4.property('Transform').property('Anchor Point').setValue([0, 0, 0]);

// Layer 15: Line Split
var layer_15 = createNullLayer(comp, 'Line Split');
layer_15.inPoint = 2.000000;
layer_15.outPoint = 5.000000;
layer_15.startTime = 0.000000;
layer_15.parent = layer_4;
layer_15.property('Transform').property('Opacity').setValue(100);
// Position has separate dimensions - combining for AE
setKeyframeWithEasing(layer_15.property('Transform').property('Position'), 45, [246, -5], {'x': [0.667], 'y': [1]}, {'x': [0.333], 'y': [0]}, frameRate);
setKeyframeWithEasing(layer_15.property('Transform').property('Position'), 48, [256, -5], {'x': [0.461], 'y': [1]}, {'x': [0.333], 'y': [0]}, frameRate);
setKeyframeWithEasing(layer_15.property('Transform').property('Position'), 53, [-124, -5], {'x': [0.667], 'y': [1]}, {'x': [0.647], 'y': [0.304]}, frameRate);
layer_15.property('Transform').property('Position').setValueAtTime(58/frameRate, [-109, -5]);
layer_15.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_15.property('Transform').property('Rotation').setValue(0);
layer_15.property('Transform').property('Anchor Point').setValue([246, -5, 0]);

// Layer 16: Tagline Mask
var layer_16 = createShapeLayer(comp, 'Tagline Mask');
// Shape Group: Group 1
var layer_16_group_0 = layer_16.property('Contents').addProperty('ADBE Vector Group');
layer_16_group_0.name = 'Group 1';
var layer_16_group_0_rect_0 = layer_16_group_0.property('Contents').addProperty('ADBE Vector Shape - Rect');
layer_16_group_0_rect_0.property('Size').setValue([933, 1095]);
layer_16_group_0_rect_0.property('Position').setValue([-532, 0]);
var layer_16_group_0_fill_1 = layer_16_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_16_group_0_fill_1.property('Color').setValue([1.000000, 0.000000, 0.000000]);
layer_16_group_0_fill_1.property('Opacity').setValue(100);
var layer_16_group_0_transform = layer_16_group_0.property('Transform');
layer_16_group_0_transform.property('Position').setValue([0, 0]);
layer_16_group_0_transform.property('Scale').setValue([100, 100]);
layer_16_group_0_transform.property('Rotation').setValue(0);
layer_16_group_0_transform.property('Opacity').setValue(100);
// Processing 1 effects for layer_16
// Effect 0: Gaussian Blur (Legacy) (type: 5)
// Group effect: Gaussian Blur (Legacy) (groups are organizational only)
layer_16.inPoint = 0.000000;
layer_16.outPoint = 5.000000;
layer_16.startTime = 0.000000;
layer_16.parent = layer_15;
// layer_16 is a track matte
layer_16.enabled = false; // Hide matte layer
layer_16.property('Transform').property('Opacity').setValue(100);
layer_16.property('Transform').property('Position').setValue([306, 0, 0]);
layer_16.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_16.property('Transform').property('Rotation').setValue(0);
layer_16.property('Transform').property('Anchor Point').setValue([0, 0, 0]);

// Layer 17: Tagline
var layer_17 = comp.layers.add(comp_3);
layer_17.name = 'Tagline';
// Processing 1 effects for layer_17
// Effect 0: Fill (type: 21)
// Fill Effect: Fill
var effect_0 = layer_17.property('Effects').addProperty('ADBE Fill');
effect_0.name = 'Fill';
effect_0.property('Fill Mask').setValue(0);
effect_0.property('All Masks').setValue(0);
// Expression for Color
try {
    var effectProperty = effect_0.property('Color');
    if (effectProperty.canSetExpression) {
        effectProperty.expression = 'thisComp.layer(\'Color Control\').effect(\'Tagline\')(\'Color\')';
        effectProperty.expressionEnabled = true;
    }
} catch (e) {
    // Effect property expression failed: thisComp.layer(\'Color Control\').effect(\'Tagline\')(\'Color\')
}
effect_0.property('Invert').setValue(0);
effect_0.property('Horizontal Feather').setValue(0);
effect_0.property('Vertical Feather').setValue(0);
effect_0.property('Opacity').setValue(1);
layer_17.inPoint = 1.958333;
layer_17.outPoint = 10.000000;
layer_17.startTime = -1.000000;
layer_17.parent = layer_14;
// layer_17 uses track matte - will be configured later
layer_17.property('Transform').property('Opacity').setValue(100);
// Position has separate dimensions - combining for AE
setKeyframeWithEasing(layer_17.property('Transform').property('Position'), 46, [475, 5], {'x': [0.833], 'y': [1]}, {'x': [0.04], 'y': [0.232]}, frameRate);
setKeyframeWithEasing(layer_17.property('Transform').property('Position'), 53, [824, 5], {'x': [0.833], 'y': [1]}, {'x': [0.167], 'y': [0]}, frameRate);
setKeyframeWithEasing(layer_17.property('Transform').property('Position'), 57, [804, 5], {'x': [0.833], 'y': [1]}, {'x': [0.167], 'y': [0]}, frameRate);
setKeyframeWithEasing(layer_17.property('Transform').property('Position'), 59, [809, 5], {'x': [0.833], 'y': [1]}, {'x': [0.167], 'y': [0]}, frameRate);
layer_17.property('Transform').property('Position').setValueAtTime(61/frameRate, [804, 5]);
// Smart detection: 5 keyframes for layer_17.property('Transform').property('Scale')
setKeyframeWithEasing(layer_17.property('Transform').property('Scale'), 46, [100, 100, 100], {'x': [0.667, 0.667, 0.667], 'y': [1, 1, 1]}, {'x': [0.333, 0.333, 0.333], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_17.property('Transform').property('Scale'), 53, [110, 100, 100], {'x': [0.667, 0.667, 0.667], 'y': [1, 1, 1]}, {'x': [0.333, 0.333, 0.333], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_17.property('Transform').property('Scale'), 57, [100, 100, 100], {'x': [0.667, 0.667, 0.667], 'y': [1, 1, 1]}, {'x': [0.333, 0.333, 0.333], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_17.property('Transform').property('Scale'), 59, [101, 100, 100], {'x': [0.667, 0.667, 0.667], 'y': [1, 1, 1]}, {'x': [0.333, 0.333, 0.333], 'y': [0, 0, 0]}, frameRate);
layer_17.property('Transform').property('Scale').setValueAtTime(61/frameRate, [100, 100, 100]);
layer_17.property('Transform').property('Rotation').setValue(0);
layer_17.property('Transform').property('Anchor Point').setValue([830, 173, 0]);

// Layer 13: Line 1
var layer_13 = createShapeLayer(comp, 'Line 1');
// Shape Group: Shape 1
var layer_13_group_0 = layer_13.property('Contents').addProperty('ADBE Vector Group');
layer_13_group_0.name = 'Shape 1';
var layer_13_group_0_path_0 = layer_13_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-292, -41], [-3, -41]];
pathShape.inTangents = [[0, 0], [0, 0]];
pathShape.outTangents = [[0, 0], [0, 0]];
pathShape.closed = false;
layer_13_group_0_path_0.property('Path').setValue(pathShape);
var layer_13_group_0_transform = layer_13_group_0.property('Transform');
layer_13_group_0_transform.property('Position').setValue([0, 0]);
layer_13_group_0_transform.property('Scale').setValue([100, 100]);
layer_13_group_0_transform.property('Rotation').setValue(0);
layer_13_group_0_transform.property('Opacity').setValue(100);
var layer_13_trim_1 = layer_13.property('Contents').addProperty('ADBE Vector Filter - Trim');
// Smart detection: 2 keyframes for layer_13_trim_1.property('Start')
setKeyframeWithEasing(layer_13_trim_1.property('Start'), 48, 100, {'x': [0.833], 'y': [0.833]}, {'x': [0.167], 'y': [0.167]}, frameRate);
layer_13_trim_1.property('Start').setValueAtTime(52/frameRate, 0);
// Smart detection: 2 keyframes for layer_13_trim_1.property('End')
setKeyframeWithEasing(layer_13_trim_1.property('End'), 52, 100, {'x': [0.833], 'y': [0.833]}, {'x': [0.167], 'y': [0.167]}, frameRate);
layer_13_trim_1.property('End').setValueAtTime(55/frameRate, 0);
layer_13_trim_1.property('Offset').setValue(0);
var layer_13_stroke_2 = layer_13.property('Contents').addProperty('ADBE Vector Graphic - Stroke');
layer_13_stroke_2.property('Color').setValue([0.662745, 0.909804, 0.270588]);
layer_13_stroke_2.property('Stroke Width').setValue(2);
// Processing 1 effects for layer_13
// Effect 0: Fill (type: 21)
// Fill Effect: Fill
var effect_0 = layer_13.property('Effects').addProperty('ADBE Fill');
effect_0.name = 'Fill';
effect_0.property('Fill Mask').setValue(0);
effect_0.property('All Masks').setValue(0);
// Expression for Color
try {
    var effectProperty = effect_0.property('Color');
    if (effectProperty.canSetExpression) {
        effectProperty.expression = 'thisComp.layer(\'Color Control\').effect(\'Lines\')(\'Color\')';
        effectProperty.expressionEnabled = true;
    }
} catch (e) {
    // Effect property expression failed: thisComp.layer(\'Color Control\').effect(\'Lines\')(\'Color\')
}
effect_0.property('Invert').setValue(0);
effect_0.property('Horizontal Feather').setValue(0);
effect_0.property('Vertical Feather').setValue(0);
effect_0.property('Opacity').setValue(1);
layer_13.inPoint = 2.000000;
layer_13.outPoint = 5.000000;
layer_13.startTime = 0.000000;
layer_13.parent = layer_3;
layer_13.property('Transform').property('Opacity').setValue(100);
// Position has separate dimensions - combining for AE
layer_13.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_13.property('Transform').property('Rotation').setValue(0);
layer_13.property('Transform').property('Anchor Point').setValue([0, 0, 0]);

// Layer 12: Line 2
var layer_12 = createShapeLayer(comp, 'Line 2');
// Shape Group: Shape 1
var layer_12_group_0 = layer_12.property('Contents').addProperty('ADBE Vector Group');
layer_12_group_0.name = 'Shape 1';
var layer_12_group_0_path_0 = layer_12_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-292, -41], [-3, -41]];
pathShape.inTangents = [[0, 0], [0, 0]];
pathShape.outTangents = [[0, 0], [0, 0]];
pathShape.closed = false;
layer_12_group_0_path_0.property('Path').setValue(pathShape);
var layer_12_group_0_transform = layer_12_group_0.property('Transform');
layer_12_group_0_transform.property('Position').setValue([0, 0]);
layer_12_group_0_transform.property('Scale').setValue([100, 100]);
layer_12_group_0_transform.property('Rotation').setValue(0);
layer_12_group_0_transform.property('Opacity').setValue(100);
var layer_12_trim_1 = layer_12.property('Contents').addProperty('ADBE Vector Filter - Trim');
// Smart detection: 2 keyframes for layer_12_trim_1.property('Start')
setKeyframeWithEasing(layer_12_trim_1.property('Start'), 49, 100, {'x': [0.833], 'y': [0.833]}, {'x': [0.167], 'y': [0.167]}, frameRate);
layer_12_trim_1.property('Start').setValueAtTime(53/frameRate, 0);
// Smart detection: 2 keyframes for layer_12_trim_1.property('End')
setKeyframeWithEasing(layer_12_trim_1.property('End'), 53, 100, {'x': [0.833], 'y': [0.833]}, {'x': [0.167], 'y': [0.167]}, frameRate);
layer_12_trim_1.property('End').setValueAtTime(56/frameRate, 0);
layer_12_trim_1.property('Offset').setValue(0);
var layer_12_stroke_2 = layer_12.property('Contents').addProperty('ADBE Vector Graphic - Stroke');
layer_12_stroke_2.property('Color').setValue([0.662745, 0.909804, 0.270588]);
layer_12_stroke_2.property('Stroke Width').setValue(1);
// Processing 1 effects for layer_12
// Effect 0: Fill (type: 21)
// Fill Effect: Fill
var effect_0 = layer_12.property('Effects').addProperty('ADBE Fill');
effect_0.name = 'Fill';
effect_0.property('Fill Mask').setValue(0);
effect_0.property('All Masks').setValue(0);
// Expression for Color
try {
    var effectProperty = effect_0.property('Color');
    if (effectProperty.canSetExpression) {
        effectProperty.expression = 'thisComp.layer(\'Color Control\').effect(\'Lines\')(\'Color\')';
        effectProperty.expressionEnabled = true;
    }
} catch (e) {
    // Effect property expression failed: thisComp.layer(\'Color Control\').effect(\'Lines\')(\'Color\')
}
effect_0.property('Invert').setValue(0);
effect_0.property('Horizontal Feather').setValue(0);
effect_0.property('Vertical Feather').setValue(0);
effect_0.property('Opacity').setValue(1);
layer_12.inPoint = 2.041667;
layer_12.outPoint = 5.000000;
layer_12.startTime = 0.041667;
layer_12.parent = layer_3;
layer_12.property('Transform').property('Opacity').setValue(100);
// Position has separate dimensions - combining for AE
layer_12.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_12.property('Transform').property('Rotation').setValue(0);
layer_12.property('Transform').property('Anchor Point').setValue([0, 0, 0]);

// Layer 11: Line 3
var layer_11 = createShapeLayer(comp, 'Line 3');
// Shape Group: Shape 1
var layer_11_group_0 = layer_11.property('Contents').addProperty('ADBE Vector Group');
layer_11_group_0.name = 'Shape 1';
var layer_11_group_0_path_0 = layer_11_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-292, -41], [-3, -41]];
pathShape.inTangents = [[0, 0], [0, 0]];
pathShape.outTangents = [[0, 0], [0, 0]];
pathShape.closed = false;
layer_11_group_0_path_0.property('Path').setValue(pathShape);
var layer_11_group_0_transform = layer_11_group_0.property('Transform');
layer_11_group_0_transform.property('Position').setValue([0, 0]);
layer_11_group_0_transform.property('Scale').setValue([100, 100]);
layer_11_group_0_transform.property('Rotation').setValue(0);
layer_11_group_0_transform.property('Opacity').setValue(100);
var layer_11_trim_1 = layer_11.property('Contents').addProperty('ADBE Vector Filter - Trim');
// Smart detection: 2 keyframes for layer_11_trim_1.property('Start')
setKeyframeWithEasing(layer_11_trim_1.property('Start'), 50, 100, {'x': [0.833], 'y': [0.833]}, {'x': [0.167], 'y': [0.167]}, frameRate);
layer_11_trim_1.property('Start').setValueAtTime(54/frameRate, 0);
// Smart detection: 2 keyframes for layer_11_trim_1.property('End')
setKeyframeWithEasing(layer_11_trim_1.property('End'), 54, 100, {'x': [0.833], 'y': [0.833]}, {'x': [0.167], 'y': [0.167]}, frameRate);
layer_11_trim_1.property('End').setValueAtTime(57/frameRate, 0);
layer_11_trim_1.property('Offset').setValue(0);
var layer_11_stroke_2 = layer_11.property('Contents').addProperty('ADBE Vector Graphic - Stroke');
layer_11_stroke_2.property('Color').setValue([0.662745, 0.909804, 0.270588]);
layer_11_stroke_2.property('Stroke Width').setValue(2);
// Processing 1 effects for layer_11
// Effect 0: Fill (type: 21)
// Fill Effect: Fill
var effect_0 = layer_11.property('Effects').addProperty('ADBE Fill');
effect_0.name = 'Fill';
effect_0.property('Fill Mask').setValue(0);
effect_0.property('All Masks').setValue(0);
// Expression for Color
try {
    var effectProperty = effect_0.property('Color');
    if (effectProperty.canSetExpression) {
        effectProperty.expression = 'thisComp.layer(\'Color Control\').effect(\'Lines\')(\'Color\')';
        effectProperty.expressionEnabled = true;
    }
} catch (e) {
    // Effect property expression failed: thisComp.layer(\'Color Control\').effect(\'Lines\')(\'Color\')
}
effect_0.property('Invert').setValue(0);
effect_0.property('Horizontal Feather').setValue(0);
effect_0.property('Vertical Feather').setValue(0);
effect_0.property('Opacity').setValue(1);
layer_11.inPoint = 2.083333;
layer_11.outPoint = 5.000000;
layer_11.startTime = 0.083333;
layer_11.parent = layer_3;
layer_11.property('Transform').property('Opacity').setValue(100);
// Position has separate dimensions - combining for AE
layer_11.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_11.property('Transform').property('Rotation').setValue(0);
layer_11.property('Transform').property('Anchor Point').setValue([0, 0, 0]);

// Layer 10: Line 4
var layer_10 = createShapeLayer(comp, 'Line 4');
// Shape Group: Shape 1
var layer_10_group_0 = layer_10.property('Contents').addProperty('ADBE Vector Group');
layer_10_group_0.name = 'Shape 1';
var layer_10_group_0_path_0 = layer_10_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-292, -41], [-3, -41]];
pathShape.inTangents = [[0, 0], [0, 0]];
pathShape.outTangents = [[0, 0], [0, 0]];
pathShape.closed = false;
layer_10_group_0_path_0.property('Path').setValue(pathShape);
var layer_10_group_0_transform = layer_10_group_0.property('Transform');
layer_10_group_0_transform.property('Position').setValue([0, 0]);
layer_10_group_0_transform.property('Scale').setValue([100, 100]);
layer_10_group_0_transform.property('Rotation').setValue(0);
layer_10_group_0_transform.property('Opacity').setValue(100);
var layer_10_trim_1 = layer_10.property('Contents').addProperty('ADBE Vector Filter - Trim');
// Smart detection: 2 keyframes for layer_10_trim_1.property('Start')
setKeyframeWithEasing(layer_10_trim_1.property('Start'), 52, 100, {'x': [0.833], 'y': [0.833]}, {'x': [0.167], 'y': [0.167]}, frameRate);
layer_10_trim_1.property('Start').setValueAtTime(56/frameRate, 0);
// Smart detection: 2 keyframes for layer_10_trim_1.property('End')
setKeyframeWithEasing(layer_10_trim_1.property('End'), 56, 100, {'x': [0.833], 'y': [0.833]}, {'x': [0.167], 'y': [0.167]}, frameRate);
layer_10_trim_1.property('End').setValueAtTime(59/frameRate, 0);
layer_10_trim_1.property('Offset').setValue(0);
var layer_10_stroke_2 = layer_10.property('Contents').addProperty('ADBE Vector Graphic - Stroke');
layer_10_stroke_2.property('Color').setValue([0.662745, 0.909804, 0.270588]);
layer_10_stroke_2.property('Stroke Width').setValue(0.5);
// Processing 1 effects for layer_10
// Effect 0: Fill (type: 21)
// Fill Effect: Fill
var effect_0 = layer_10.property('Effects').addProperty('ADBE Fill');
effect_0.name = 'Fill';
effect_0.property('Fill Mask').setValue(0);
effect_0.property('All Masks').setValue(0);
// Expression for Color
try {
    var effectProperty = effect_0.property('Color');
    if (effectProperty.canSetExpression) {
        effectProperty.expression = 'thisComp.layer(\'Color Control\').effect(\'Lines\')(\'Color\')';
        effectProperty.expressionEnabled = true;
    }
} catch (e) {
    // Effect property expression failed: thisComp.layer(\'Color Control\').effect(\'Lines\')(\'Color\')
}
effect_0.property('Invert').setValue(0);
effect_0.property('Horizontal Feather').setValue(0);
effect_0.property('Vertical Feather').setValue(0);
effect_0.property('Opacity').setValue(1);
layer_10.inPoint = 2.166667;
layer_10.outPoint = 5.000000;
layer_10.startTime = 0.166667;
layer_10.parent = layer_3;
layer_10.property('Transform').property('Opacity').setValue(100);
// Position has separate dimensions - combining for AE
layer_10.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_10.property('Transform').property('Rotation').setValue(0);
layer_10.property('Transform').property('Anchor Point').setValue([0, 0, 0]);

// Layer 9: Circle 1
var layer_9 = createShapeLayer(comp, 'Circle 1');
// Shape Group: Group 1
var layer_9_group_0 = layer_9.property('Contents').addProperty('ADBE Vector Group');
layer_9_group_0.name = 'Group 1';
var layer_9_group_0_ellipse_0 = layer_9_group_0.property('Contents').addProperty('ADBE Vector Shape - Ellipse');
layer_9_group_0_ellipse_0.property('Size').setValue([300, 300]);
layer_9_group_0_ellipse_0.property('Position').setValue([0, 0]);
var layer_9_group_0_transform = layer_9_group_0.property('Transform');
layer_9_group_0_transform.property('Position').setValue([0, 0]);
layer_9_group_0_transform.property('Scale').setValue([100, 100]);
layer_9_group_0_transform.property('Rotation').setValue(0);
layer_9_group_0_transform.property('Opacity').setValue(100);
var layer_9_fill_1 = layer_9.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_9_fill_1.property('Color').setValue([1.000000, 0.000000, 0.000000]);
// Processing 1 effects for layer_9
// Effect 0: Fill (type: 21)
// Fill Effect: Fill
var effect_0 = layer_9.property('Effects').addProperty('ADBE Fill');
effect_0.name = 'Fill';
effect_0.property('Fill Mask').setValue(0);
effect_0.property('All Masks').setValue(0);
// Expression for Color
try {
    var effectProperty = effect_0.property('Color');
    if (effectProperty.canSetExpression) {
        effectProperty.expression = 'thisComp.layer(\'Color Control\').effect(\'Circle\')(\'Color\')';
        effectProperty.expressionEnabled = true;
    }
} catch (e) {
    // Effect property expression failed: thisComp.layer(\'Color Control\').effect(\'Circle\')(\'Color\')
}
effect_0.property('Invert').setValue(0);
effect_0.property('Horizontal Feather').setValue(0);
effect_0.property('Vertical Feather').setValue(0);
effect_0.property('Opacity').setValue(1);
layer_9.inPoint = 0.000000;
layer_9.outPoint = 5.000000;
layer_9.startTime = 0.000000;
layer_9.parent = layer_4;
layer_9.property('Transform').property('Opacity').setValue(100);
// Position has separate dimensions - combining for AE
setKeyframeWithEasing(layer_9.property('Transform').property('Position'), 18, [0, 0], {'x': [0.812], 'y': [0.935]}, {'x': [0.172], 'y': [0.357]}, frameRate);
setKeyframeWithEasing(layer_9.property('Transform').property('Position'), 23, [0, -190], {'x': [0.763], 'y': [-16.496]}, {'x': [0.201], 'y': [-7.919]}, frameRate);
setKeyframeWithEasing(layer_9.property('Transform').property('Position'), 26, [0, -190], {'x': [0.81], 'y': [0.676]}, {'x': [0.321], 'y': [0.166]}, frameRate);
setKeyframeWithEasing(layer_9.property('Transform').property('Position'), 30, [0, 0], {'x': [0.659], 'y': [0.907]}, {'x': [0.297], 'y': [0]}, frameRate);
setKeyframeWithEasing(layer_9.property('Transform').property('Position'), 31, [0, 19], {'x': [0.815], 'y': [1]}, {'x': [0.425], 'y': [-0.244]}, frameRate);
setKeyframeWithEasing(layer_9.property('Transform').property('Position'), 32, [0, 10], {'x': [0.833], 'y': [1]}, {'x': [0.167], 'y': [0]}, frameRate);
setKeyframeWithEasing(layer_9.property('Transform').property('Position'), 33, [0, -4], {'x': [0.833], 'y': [1]}, {'x': [0.167], 'y': [0]}, frameRate);
setKeyframeWithEasing(layer_9.property('Transform').property('Position'), 34, [0, -10.5], {'x': [0.583], 'y': [-0.875]}, {'x': [0.167], 'y': [0]}, frameRate);
setKeyframeWithEasing(layer_9.property('Transform').property('Position'), 35, [0, -8.5], {'x': [0.833], 'y': [1]}, {'x': [0.417], 'y': [0.682]}, frameRate);
setKeyframeWithEasing(layer_9.property('Transform').property('Position'), 36, [0, -3], {'x': [0.833], 'y': [1]}, {'x': [0.167], 'y': [0]}, frameRate);
layer_9.property('Transform').property('Position').setValueAtTime(37/frameRate, [0, 0]);
setKeyframeWithEasing(layer_9.property('Transform').property('Position'), 43, [0, 0], {'x': [0.667], 'y': [1]}, {'x': [0.333], 'y': [0]}, frameRate);
setKeyframeWithEasing(layer_9.property('Transform').property('Position'), 46, [35, 0], {'x': [0.667], 'y': [1]}, {'x': [0.333], 'y': [0]}, frameRate);
setKeyframeWithEasing(layer_9.property('Transform').property('Position'), 52, [-363, 0], {'x': [0.667], 'y': [1]}, {'x': [0.333], 'y': [0]}, frameRate);
setKeyframeWithEasing(layer_9.property('Transform').property('Position'), 57, [-330, 0], {'x': [0.833], 'y': [1]}, {'x': [0.167], 'y': [0]}, frameRate);
layer_9.property('Transform').property('Position').setValueAtTime(60/frameRate, [-335, 0]);
// Smart detection: 20 keyframes for layer_9.property('Transform').property('Scale')
setKeyframeWithEasing(layer_9.property('Transform').property('Scale'), 12, [0, 0, 100], {'x': [0.833, 0.833, 0.833], 'y': [0.921, 0.84, 1]}, {'x': [0.333, 0.333, 0.333], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_9.property('Transform').property('Scale'), 18, [120, 80, 100], {'x': [0.667, 0.667, 0.667], 'y': [1, 1, 1]}, {'x': [0.167, 0.167, 0.167], 'y': [-0.135, 0.183, 0]}, frameRate);
setKeyframeWithEasing(layer_9.property('Transform').property('Scale'), 21, [85, 115, 100], {'x': [0.667, 0.667, 0.667], 'y': [1, 1, 1]}, {'x': [0.333, 0.333, 0.333], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_9.property('Transform').property('Scale'), 23, [110, 90, 100], {'x': [0.833, 0.833, 0.833], 'y': [0.792, 0.792, 1]}, {'x': [0.333, 0.333, 0.333], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_9.property('Transform').property('Scale'), 26, [100, 100, 100], {'x': [0.667, 0.667, 0.667], 'y': [1, 1, 1]}, {'x': [0.167, 0.167, 0.167], 'y': [0.139, 0.139, 0]}, frameRate);
setKeyframeWithEasing(layer_9.property('Transform').property('Scale'), 29, [85, 115, 100], {'x': [0.667, 0.667, 0.667], 'y': [1, 1, 1]}, {'x': [0.333, 0.333, 0.333], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_9.property('Transform').property('Scale'), 31, [115, 85, 100], {'x': [0.667, 0.667, 0.667], 'y': [1, 1, 1]}, {'x': [0.333, 0.333, 0.333], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_9.property('Transform').property('Scale'), 34, [95, 105, 100], {'x': [0.833, 0.833, 0.833], 'y': [0.867, 0.867, 1]}, {'x': [0.333, 0.333, 0.333], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_9.property('Transform').property('Scale'), 36, [100, 100, 100], {'x': [0.667, 0.667, 0.667], 'y': [1, 1, 1]}, {'x': [0.167, 0.167, 0.167], 'y': [0.222, 0.222, 0]}, frameRate);
setKeyframeWithEasing(layer_9.property('Transform').property('Scale'), 38, [103, 97, 100], {'x': [0.667, 0.667, 0.667], 'y': [1, 1, 1]}, {'x': [0.333, 0.333, 0.333], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_9.property('Transform').property('Scale'), 40, [100, 100, 100], {'x': [0.833, 0.833, 0.833], 'y': [1, 1, 1]}, {'x': [0.333, 0.333, 0.333], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_9.property('Transform').property('Scale'), 43, [100, 100, 100], {'x': [0.833, 0.833, 0.833], 'y': [1, 1, 1]}, {'x': [0.167, 0.167, 0.167], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_9.property('Transform').property('Scale'), 46, [120, 80, 100], {'x': [0.833, 0.833, 0.833], 'y': [1, 1, 1]}, {'x': [0.167, 0.167, 0.167], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_9.property('Transform').property('Scale'), 49, [140, 60, 100], {'x': [0.833, 0.833, 0.833], 'y': [1, 1, 1]}, {'x': [0.167, 0.167, 0.167], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_9.property('Transform').property('Scale'), 52, [120, 80, 100], {'x': [0.833, 0.833, 0.833], 'y': [1, 1, 1]}, {'x': [0.167, 0.167, 0.167], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_9.property('Transform').property('Scale'), 55, [90, 110, 100], {'x': [0.833, 0.833, 0.833], 'y': [1, 1, 1]}, {'x': [0.167, 0.167, 0.167], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_9.property('Transform').property('Scale'), 58, [105, 95, 100], {'x': [0.833, 0.833, 0.833], 'y': [1, 1, 1]}, {'x': [0.167, 0.167, 0.167], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_9.property('Transform').property('Scale'), 60, [98, 102, 100], {'x': [0.833, 0.833, 0.833], 'y': [1, 1, 1]}, {'x': [0.167, 0.167, 0.167], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_9.property('Transform').property('Scale'), 62, [101, 99, 100], {'x': [0.833, 0.833, 0.833], 'y': [1, 1, 1]}, {'x': [0.167, 0.167, 0.167], 'y': [0, 0, 0]}, frameRate);
layer_9.property('Transform').property('Scale').setValueAtTime(64/frameRate, [100, 100, 100]);
layer_9.property('Transform').property('Rotation').setValue(0);
layer_9.property('Transform').property('Anchor Point').setValue([0, 0, 0]);

// Layer 7: Logo Mask
var layer_7 = createShapeLayer(comp, 'Logo Mask');
// Shape Group: Group 1
var layer_7_group_0 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_0.name = 'Group 1';
var layer_7_group_0_ellipse_0 = layer_7_group_0.property('Contents').addProperty('ADBE Vector Shape - Ellipse');
layer_7_group_0_ellipse_0.property('Size').setValue([300, 300]);
layer_7_group_0_ellipse_0.property('Position').setValue([0, 0]);
var layer_7_group_0_transform = layer_7_group_0.property('Transform');
layer_7_group_0_transform.property('Position').setValue([0, 0]);
layer_7_group_0_transform.property('Scale').setValue([100, 100]);
layer_7_group_0_transform.property('Rotation').setValue(0);
layer_7_group_0_transform.property('Opacity').setValue(100);
var layer_7_fill_1 = layer_7.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_fill_1.property('Color').setValue([1.000000, 0.000000, 0.000000]);
// Processing 1 effects for layer_7
// Effect 0: Fill (type: 21)
// Fill Effect: Fill
var effect_0 = layer_7.property('Effects').addProperty('ADBE Fill');
effect_0.name = 'Fill';
effect_0.property('Fill Mask').setValue(0);
effect_0.property('All Masks').setValue(0);
effect_0.property('Color').setValue([0.662745, 0.909804, 0.270588]);
effect_0.property('Invert').setValue(0);
effect_0.property('Horizontal Feather').setValue(0);
effect_0.property('Vertical Feather').setValue(0);
effect_0.property('Opacity').setValue(1);
layer_7.inPoint = 0.000000;
layer_7.outPoint = 5.000000;
layer_7.startTime = 0.000000;
layer_7.parent = layer_4;
// layer_7 is a track matte
layer_7.enabled = false; // Hide matte layer
layer_7.property('Transform').property('Opacity').setValue(100);
// Position has separate dimensions - combining for AE
setKeyframeWithEasing(layer_7.property('Transform').property('Position'), 18, [0, 0], {'x': [0.812], 'y': [0.935]}, {'x': [0.172], 'y': [0.357]}, frameRate);
setKeyframeWithEasing(layer_7.property('Transform').property('Position'), 23, [0, -190], {'x': [0.763], 'y': [-16.496]}, {'x': [0.201], 'y': [-7.919]}, frameRate);
setKeyframeWithEasing(layer_7.property('Transform').property('Position'), 26, [0, -190], {'x': [0.81], 'y': [0.676]}, {'x': [0.321], 'y': [0.166]}, frameRate);
setKeyframeWithEasing(layer_7.property('Transform').property('Position'), 30, [0, 0], {'x': [0.659], 'y': [0.907]}, {'x': [0.297], 'y': [0]}, frameRate);
setKeyframeWithEasing(layer_7.property('Transform').property('Position'), 31, [0, 19], {'x': [0.815], 'y': [1]}, {'x': [0.425], 'y': [-0.244]}, frameRate);
setKeyframeWithEasing(layer_7.property('Transform').property('Position'), 32, [0, 10], {'x': [0.833], 'y': [1]}, {'x': [0.167], 'y': [0]}, frameRate);
setKeyframeWithEasing(layer_7.property('Transform').property('Position'), 33, [0, -4], {'x': [0.833], 'y': [1]}, {'x': [0.167], 'y': [0]}, frameRate);
setKeyframeWithEasing(layer_7.property('Transform').property('Position'), 34, [0, -10.5], {'x': [0.583], 'y': [-0.875]}, {'x': [0.167], 'y': [0]}, frameRate);
setKeyframeWithEasing(layer_7.property('Transform').property('Position'), 35, [0, -8.5], {'x': [0.833], 'y': [1]}, {'x': [0.417], 'y': [0.682]}, frameRate);
setKeyframeWithEasing(layer_7.property('Transform').property('Position'), 36, [0, -3], {'x': [0.833], 'y': [1]}, {'x': [0.167], 'y': [0]}, frameRate);
layer_7.property('Transform').property('Position').setValueAtTime(37/frameRate, [0, 0]);
setKeyframeWithEasing(layer_7.property('Transform').property('Position'), 43, [0, 0], {'x': [0.667], 'y': [1]}, {'x': [0.333], 'y': [0]}, frameRate);
setKeyframeWithEasing(layer_7.property('Transform').property('Position'), 46, [35, 0], {'x': [0.667], 'y': [1]}, {'x': [0.333], 'y': [0]}, frameRate);
setKeyframeWithEasing(layer_7.property('Transform').property('Position'), 52, [-363, 0], {'x': [0.667], 'y': [1]}, {'x': [0.333], 'y': [0]}, frameRate);
setKeyframeWithEasing(layer_7.property('Transform').property('Position'), 57, [-330, 0], {'x': [0.833], 'y': [1]}, {'x': [0.167], 'y': [0]}, frameRate);
layer_7.property('Transform').property('Position').setValueAtTime(60/frameRate, [-335, 0]);
// Smart detection: 20 keyframes for layer_7.property('Transform').property('Scale')
setKeyframeWithEasing(layer_7.property('Transform').property('Scale'), 12, [0, 0, 100], {'x': [0.833, 0.833, 0.833], 'y': [0.921, 0.84, 1]}, {'x': [0.333, 0.333, 0.333], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_7.property('Transform').property('Scale'), 18, [120, 80, 100], {'x': [0.667, 0.667, 0.667], 'y': [1, 1, 1]}, {'x': [0.167, 0.167, 0.167], 'y': [-0.135, 0.183, 0]}, frameRate);
setKeyframeWithEasing(layer_7.property('Transform').property('Scale'), 21, [85, 115, 100], {'x': [0.667, 0.667, 0.667], 'y': [1, 1, 1]}, {'x': [0.333, 0.333, 0.333], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_7.property('Transform').property('Scale'), 23, [110, 90, 100], {'x': [0.833, 0.833, 0.833], 'y': [0.792, 0.792, 1]}, {'x': [0.333, 0.333, 0.333], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_7.property('Transform').property('Scale'), 26, [100, 100, 100], {'x': [0.667, 0.667, 0.667], 'y': [1, 1, 1]}, {'x': [0.167, 0.167, 0.167], 'y': [0.139, 0.139, 0]}, frameRate);
setKeyframeWithEasing(layer_7.property('Transform').property('Scale'), 29, [85, 115, 100], {'x': [0.667, 0.667, 0.667], 'y': [1, 1, 1]}, {'x': [0.333, 0.333, 0.333], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_7.property('Transform').property('Scale'), 31, [115, 85, 100], {'x': [0.667, 0.667, 0.667], 'y': [1, 1, 1]}, {'x': [0.333, 0.333, 0.333], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_7.property('Transform').property('Scale'), 34, [95, 105, 100], {'x': [0.833, 0.833, 0.833], 'y': [0.867, 0.867, 1]}, {'x': [0.333, 0.333, 0.333], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_7.property('Transform').property('Scale'), 36, [100, 100, 100], {'x': [0.667, 0.667, 0.667], 'y': [1, 1, 1]}, {'x': [0.167, 0.167, 0.167], 'y': [0.222, 0.222, 0]}, frameRate);
setKeyframeWithEasing(layer_7.property('Transform').property('Scale'), 38, [103, 97, 100], {'x': [0.667, 0.667, 0.667], 'y': [1, 1, 1]}, {'x': [0.333, 0.333, 0.333], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_7.property('Transform').property('Scale'), 40, [100, 100, 100], {'x': [0.833, 0.833, 0.833], 'y': [1, 1, 1]}, {'x': [0.333, 0.333, 0.333], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_7.property('Transform').property('Scale'), 43, [100, 100, 100], {'x': [0.833, 0.833, 0.833], 'y': [1, 1, 1]}, {'x': [0.167, 0.167, 0.167], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_7.property('Transform').property('Scale'), 46, [120, 80, 100], {'x': [0.833, 0.833, 0.833], 'y': [1, 1, 1]}, {'x': [0.167, 0.167, 0.167], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_7.property('Transform').property('Scale'), 49, [140, 60, 100], {'x': [0.833, 0.833, 0.833], 'y': [1, 1, 1]}, {'x': [0.167, 0.167, 0.167], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_7.property('Transform').property('Scale'), 52, [120, 80, 100], {'x': [0.833, 0.833, 0.833], 'y': [1, 1, 1]}, {'x': [0.167, 0.167, 0.167], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_7.property('Transform').property('Scale'), 55, [90, 110, 100], {'x': [0.833, 0.833, 0.833], 'y': [1, 1, 1]}, {'x': [0.167, 0.167, 0.167], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_7.property('Transform').property('Scale'), 58, [105, 95, 100], {'x': [0.833, 0.833, 0.833], 'y': [1, 1, 1]}, {'x': [0.167, 0.167, 0.167], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_7.property('Transform').property('Scale'), 60, [98, 102, 100], {'x': [0.833, 0.833, 0.833], 'y': [1, 1, 1]}, {'x': [0.167, 0.167, 0.167], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_7.property('Transform').property('Scale'), 62, [101, 99, 100], {'x': [0.833, 0.833, 0.833], 'y': [1, 1, 1]}, {'x': [0.167, 0.167, 0.167], 'y': [0, 0, 0]}, frameRate);
layer_7.property('Transform').property('Scale').setValueAtTime(64/frameRate, [100, 100, 100]);
layer_7.property('Transform').property('Rotation').setValue(0);
layer_7.property('Transform').property('Anchor Point').setValue([0, 0, 0]);

// Layer 8: Logo
var layer_8 = comp.layers.add(comp_2);
layer_8.name = 'Logo';
// Processing 1 effects for layer_8
// Effect 0: Fill (type: 21)
// Fill Effect: Fill
var effect_0 = layer_8.property('Effects').addProperty('ADBE Fill');
effect_0.name = 'Fill';
effect_0.property('Fill Mask').setValue(0);
effect_0.property('All Masks').setValue(0);
// Expression for Color
try {
    var effectProperty = effect_0.property('Color');
    if (effectProperty.canSetExpression) {
        effectProperty.expression = 'thisComp.layer(\'Color Control\').effect(\'Logo\')(\'Color\')';
        effectProperty.expressionEnabled = true;
    }
} catch (e) {
    // Effect property expression failed: thisComp.layer(\'Color Control\').effect(\'Logo\')(\'Color\')
}
effect_0.property('Invert').setValue(0);
effect_0.property('Horizontal Feather').setValue(0);
effect_0.property('Vertical Feather').setValue(0);
effect_0.property('Opacity').setValue(1);
layer_8.inPoint = 2.000000;
layer_8.outPoint = 9.125000;
layer_8.startTime = 0.375000;
layer_8.parent = layer_4;
// layer_8 uses track matte - will be configured later
layer_8.property('Transform').property('Opacity').setValue(100);
// Position has separate dimensions - combining for AE
setKeyframeWithEasing(layer_8.property('Transform').property('Position'), 49, [-332, 0], {'x': [0.833], 'y': [0.833]}, {'x': [0.167], 'y': [0.167]}, frameRate);
setKeyframeWithEasing(layer_8.property('Transform').property('Position'), 53, [-414, 0], {'x': [0.833], 'y': [0.833]}, {'x': [0.167], 'y': [0.167]}, frameRate);
setKeyframeWithEasing(layer_8.property('Transform').property('Position'), 58, [-317, 0], {'x': [0.833], 'y': [0.833]}, {'x': [0.167], 'y': [0.167]}, frameRate);
layer_8.property('Transform').property('Position').setValueAtTime(61/frameRate, [-340, 0]);
// Smart detection: 7 keyframes for layer_8.property('Transform').property('Scale')
setKeyframeWithEasing(layer_8.property('Transform').property('Scale'), 49, [80, 40, 100], {'x': [0.667, 0.667, 0.667], 'y': [1, 1, 1]}, {'x': [0.333, 0.333, 0.333], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_8.property('Transform').property('Scale'), 53, [60, 40, 100], {'x': [0.667, 0.667, 0.667], 'y': [1, 1, 1]}, {'x': [0.333, 0.333, 0.333], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_8.property('Transform').property('Scale'), 57, [60, 80, 100], {'x': [0.667, 0.667, 0.667], 'y': [1, 1, 1]}, {'x': [0.333, 0.333, 0.333], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_8.property('Transform').property('Scale'), 61, [65, 55, 100], {'x': [0.667, 0.667, 0.667], 'y': [1, 1, 1]}, {'x': [0.333, 0.333, 0.333], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_8.property('Transform').property('Scale'), 64, [58, 62, 100], {'x': [0.667, 0.667, 0.667], 'y': [1, 1, 1]}, {'x': [0.333, 0.333, 0.333], 'y': [0, 0, 0]}, frameRate);
setKeyframeWithEasing(layer_8.property('Transform').property('Scale'), 67, [61, 59, 100], {'x': [0.667, 0.667, 0.667], 'y': [1, 1, 1]}, {'x': [0.333, 0.333, 0.333], 'y': [0, 0, 0]}, frameRate);
layer_8.property('Transform').property('Scale').setValueAtTime(70/frameRate, [60, 60, 100]);
layer_8.property('Transform').property('Rotation').setValue(0);
layer_8.property('Transform').property('Anchor Point').setValue([250, 250, 0]);

// Layer 6: Line Burst 1
var layer_6 = createShapeLayer(comp, 'Line Burst 1');
// Shape Group: Shape 1
var layer_6_group_0 = layer_6.property('Contents').addProperty('ADBE Vector Group');
layer_6_group_0.name = 'Shape 1';
var layer_6_group_0_path_0 = layer_6_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[29, 152], [214.5, 152]];
pathShape.inTangents = [[0, 0], [0, 0]];
pathShape.outTangents = [[0, 0], [0, 0]];
pathShape.closed = false;
layer_6_group_0_path_0.property('Path').setValue(pathShape);
var layer_6_group_0_stroke_1 = layer_6_group_0.property('Contents').addProperty('ADBE Vector Graphic - Stroke');
layer_6_group_0_stroke_1.property('Color').setValue([0.662745, 0.909804, 0.270588]);
layer_6_group_0_stroke_1.property('Stroke Width').setValue(3);
layer_6_group_0_stroke_1.property('Opacity').setValue(100);
var layer_6_group_0_transform = layer_6_group_0.property('Transform');
layer_6_group_0_transform.property('Position').setValue([0, 0]);
layer_6_group_0_transform.property('Scale').setValue([100, 100]);
layer_6_group_0_transform.property('Rotation').setValue(0);
layer_6_group_0_transform.property('Opacity').setValue(100);
var layer_6_trim_1 = layer_6.property('Contents').addProperty('ADBE Vector Filter - Trim');
// Smart detection: 2 keyframes for layer_6_trim_1.property('Start')
setKeyframeWithEasing(layer_6_trim_1.property('Start'), 33, 0, {'x': [0.441], 'y': [0.794]}, {'x': [0.091], 'y': [0.678]}, frameRate);
layer_6_trim_1.property('Start').setValueAtTime(37/frameRate, 100);
// Smart detection: 2 keyframes for layer_6_trim_1.property('End')
setKeyframeWithEasing(layer_6_trim_1.property('End'), 29, 0, {'x': [0.667], 'y': [1]}, {'x': [0.08], 'y': [0.682]}, frameRate);
layer_6_trim_1.property('End').setValueAtTime(33/frameRate, 100);
layer_6_trim_1.property('Offset').setValue(0);
// Processing 1 effects for layer_6
// Effect 0: Fill (type: 21)
// Fill Effect: Fill
var effect_0 = layer_6.property('Effects').addProperty('ADBE Fill');
effect_0.name = 'Fill';
effect_0.property('Fill Mask').setValue(0);
effect_0.property('All Masks').setValue(0);
// Expression for Color
try {
    var effectProperty = effect_0.property('Color');
    if (effectProperty.canSetExpression) {
        effectProperty.expression = 'thisComp.layer(\'Color Control\').effect(\'Line Burst \')(\'Color\')';
        effectProperty.expressionEnabled = true;
    }
} catch (e) {
    // Effect property expression failed: thisComp.layer(\'Color Control\').effect(\'Line Burst \')(\'Color\')
}
effect_0.property('Invert').setValue(0);
effect_0.property('Horizontal Feather').setValue(0);
effect_0.property('Vertical Feather').setValue(0);
effect_0.property('Opacity').setValue(1);
layer_6.inPoint = 1.250000;
layer_6.outPoint = 1.541667;
layer_6.startTime = 0.000000;
layer_6.parent = layer_4;
layer_6.property('Transform').property('Opacity').setValue(100);
layer_6.property('Transform').property('Position').setValue([-19, -5, 0]);
layer_6.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_6.property('Transform').property('Rotation').setValue(0);
layer_6.property('Transform').property('Anchor Point').setValue([0, 0, 0]);

// Layer 5: Line Burst 2
var layer_5 = createShapeLayer(comp, 'Line Burst 2');
// Shape Group: Shape 1
var layer_5_group_0 = layer_5.property('Contents').addProperty('ADBE Vector Group');
layer_5_group_0.name = 'Shape 1';
var layer_5_group_0_path_0 = layer_5_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[29, 152], [214.5, 152]];
pathShape.inTangents = [[0, 0], [0, 0]];
pathShape.outTangents = [[0, 0], [0, 0]];
pathShape.closed = false;
layer_5_group_0_path_0.property('Path').setValue(pathShape);
var layer_5_group_0_stroke_1 = layer_5_group_0.property('Contents').addProperty('ADBE Vector Graphic - Stroke');
layer_5_group_0_stroke_1.property('Color').setValue([0.662745, 0.909804, 0.270588]);
layer_5_group_0_stroke_1.property('Stroke Width').setValue(3);
layer_5_group_0_stroke_1.property('Opacity').setValue(100);
var layer_5_group_0_transform = layer_5_group_0.property('Transform');
layer_5_group_0_transform.property('Position').setValue([0, 0]);
layer_5_group_0_transform.property('Scale').setValue([100, 100]);
layer_5_group_0_transform.property('Rotation').setValue(0);
layer_5_group_0_transform.property('Opacity').setValue(100);
var layer_5_trim_1 = layer_5.property('Contents').addProperty('ADBE Vector Filter - Trim');
// Smart detection: 2 keyframes for layer_5_trim_1.property('Start')
setKeyframeWithEasing(layer_5_trim_1.property('Start'), 33, 0, {'x': [0.441], 'y': [0.794]}, {'x': [0.091], 'y': [0.678]}, frameRate);
layer_5_trim_1.property('Start').setValueAtTime(37/frameRate, 100);
// Smart detection: 2 keyframes for layer_5_trim_1.property('End')
setKeyframeWithEasing(layer_5_trim_1.property('End'), 29, 0, {'x': [0.667], 'y': [1]}, {'x': [0.08], 'y': [0.682]}, frameRate);
layer_5_trim_1.property('End').setValueAtTime(33/frameRate, 100);
layer_5_trim_1.property('Offset').setValue(0);
// Processing 1 effects for layer_5
// Effect 0: Fill (type: 21)
// Fill Effect: Fill
var effect_0 = layer_5.property('Effects').addProperty('ADBE Fill');
effect_0.name = 'Fill';
effect_0.property('Fill Mask').setValue(0);
effect_0.property('All Masks').setValue(0);
// Expression for Color
try {
    var effectProperty = effect_0.property('Color');
    if (effectProperty.canSetExpression) {
        effectProperty.expression = 'thisComp.layer(\'Color Control\').effect(\'Line Burst \')(\'Color\')';
        effectProperty.expressionEnabled = true;
    }
} catch (e) {
    // Effect property expression failed: thisComp.layer(\'Color Control\').effect(\'Line Burst \')(\'Color\')
}
effect_0.property('Invert').setValue(0);
effect_0.property('Horizontal Feather').setValue(0);
effect_0.property('Vertical Feather').setValue(0);
effect_0.property('Opacity').setValue(1);
layer_5.inPoint = 1.250000;
layer_5.outPoint = 1.541667;
layer_5.startTime = 0.000000;
layer_5.parent = layer_6;
layer_5.property('Transform').property('Opacity').setValue(100);
layer_5.property('Transform').property('Position').setValue([56, 304, 0]);
layer_5.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_5.property('Transform').property('Rotation').setValue(180);
layer_5.property('Transform').property('Anchor Point').setValue([0, 0, 0]);

// Layer 1: Audio 1
var layer_1 = comp.layers.add(comp_0);
layer_1.name = 'Audio 1';
layer_1.inPoint = 0.000000;
layer_1.outPoint = 5.000000;
layer_1.startTime = 0.000000;
layer_1.property('Transform').property('Opacity').setValue(100);
layer_1.property('Transform').property('Position').setValue([960, 540, 0]);
layer_1.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_1.property('Transform').property('Rotation').setValue(0);
layer_1.property('Transform').property('Anchor Point').setValue([960, 540, 0]);


// Set up track matte relationships
// Set up track matte: layer_8 uses layer_7 as TrackMatteType.ALPHA
try {
    // Ensure track matte layer is directly above the layer that uses it
    layer_7.moveBefore(layer_8);
    // Set the track matte type
    layer_8.trackMatteType = TrackMatteType.ALPHA;
    // IMPORTANT: Hide the matte layer (turn off visibility)
    layer_7.enabled = false;
} catch(e) {
    // Track matte setup failed for layer_8
    alert('Track matte setup failed for layer_8: ' + e.toString());
}
// Set up track matte: layer_17 uses layer_16 as TrackMatteType.ALPHA_INVERTED
try {
    // Ensure track matte layer is directly above the layer that uses it
    layer_16.moveBefore(layer_17);
    // Set the track matte type
    layer_17.trackMatteType = TrackMatteType.ALPHA_INVERTED;
    // IMPORTANT: Hide the matte layer (turn off visibility)
    layer_16.enabled = false;
} catch(e) {
    // Track matte setup failed for layer_17
    alert('Track matte setup failed for layer_17: ' + e.toString());
}


// Animation creation complete
alert('Animation "Logo 1" created successfully!\n' +
      'Duration: 5.00 seconds\n' +
      'Layers: 17\n' +
      'Assets: 6');

app.endUndoGroup();