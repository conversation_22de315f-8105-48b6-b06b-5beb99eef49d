// Logo 1 - Converted from <PERSON><PERSON> JSON
// Original Bodymovin version: 5.12.1
// Generated by <PERSON><PERSON> to JSX Converter

// Disable undo for performance
app.beginUndoGroup('Create Animation');

// Helper Functions
function setKeyframes(property, keyframes, frameRate) {
    if (!keyframes || !keyframes.length) return;
    
    for (var i = 0; i < keyframes.length; i++) {
        var kf = keyframes[i];
        var time = kf.t / frameRate;
        
        if (i === 0) {
            property.setValueAtTime(time, kf.s);
        } else {
            property.setValueAtTime(time, kf.s);
            
            // Set easing if available
            if (kf.i && kf.o) {
                var keyIndex = property.nearestKeyIndex(time);
                try {
                    property.setTemporalEaseAtKey(keyIndex, kf.i, kf.o);
                } catch(e) {
                    // Ignore easing errors
                }
            }
        }
    }
}

function createSolidLayer(comp, name, color, width, height) {
    return comp.layers.addSolid(color, name, width, height, 1.0);
}

function createShapeLayer(comp, name) {
    var layer = comp.layers.addShape();
    layer.name = name;
    return layer;
}

function createNullLayer(comp, name) {
    var layer = comp.layers.addNull();
    layer.name = name;
    return layer;
}

function createTextLayer(comp, name) {
    var layer = comp.layers.addText(name);
    return layer;
}

function createPrecomp(comp, name, refId) {
    // This would reference a precomp - simplified for now
    return createNullLayer(comp, name + ' (Precomp)');
}

function rgbToAE(r, g, b, a) {
    a = a || 1;
    return [r, g, b, a];
}

function bezierToAE(bezierData) {
    var shape = new Shape();
    if (bezierData.v && bezierData.v.length > 0) {
        shape.vertices = bezierData.v;
        if (bezierData.i) shape.inTangents = bezierData.i;
        if (bezierData.o) shape.outTangents = bezierData.o;
        shape.closed = bezierData.c || false;
    }
    return shape;
}

// === INTELLIGENT ASSET IMPORT WITH CONTEXT-AWARE DIALOGS ===
function importAssetWithDialog(comp, assetId, originalPath, assetType, assetName, dimensions, description) {
    // Try original path first
    if (originalPath && originalPath !== '') {
        var originalFile = new File(originalPath);
        if (originalFile.exists) {
            return importAssetFile(comp, assetId, originalFile, assetName);
        }
    }
    
    // Build hyper-intelligent dialog with detailed context
    var message = '🚨 MISSING ASSET REQUIRED\n';
    message += '════════════════════════════════════════\n\n';
    
    // Asset identification section
    message += '📋 WHAT FILE IS NEEDED:\n';
    message += '• Asset Name: "' + (assetName || assetId) + '"\n';
    message += '• File Type: ' + assetType.toUpperCase() + '\n';
    message += '• Asset ID: ' + assetId + '\n';
    
    if (dimensions && dimensions !== '') {
        message += '• Expected Size: ' + dimensions + ' pixels\n';
    }
    
    message += '• Original Path: ' + (originalPath || 'Not specified') + '\n';
    
    // Context and purpose section
    if (description && description !== '') {
        message += '\n🎯 PURPOSE & CONTEXT:\n';
        message += '• ' + description + '\n';
    }
    
    // Smart guidance based on asset type and name
    message += '\n💡 WHAT TO LOOK FOR:\n';
    if (assetType === 'image') {
        message += '• Look for PNG, JPG, or other image files\n';
        if (assetName && assetName.toLowerCase().indexOf('logo') !== -1) {
            message += '• This should be your company/brand logo\n';
            message += '• Usually a PNG with transparent background\n';
        } else if (assetName && assetName.toLowerCase().indexOf('background') !== -1) {
            message += '• This should be a background image\n';
            message += '• Usually covers the full composition size\n';
        } else if (assetName && assetName.toLowerCase().indexOf('placeholder') !== -1) {
            message += '• This is a placeholder - replace with your image\n';
            message += '• Can be any image that fits your design\n';
        }
    } else if (assetType === 'audio') {
        message += '• Look for MP3, WAV, or other audio files\n';
        if (assetName && assetName.toLowerCase().indexOf('music') !== -1) {
            message += '• This should be background music\n';
            message += '• Usually a longer audio track\n';
        } else {
            message += '• This should be a sound effect or audio clip\n';
            message += '• Usually a shorter audio file\n';
        }
    } else if (assetType === 'video') {
        message += '• Look for MP4, MOV, or other video files\n';
        message += '• Should match the expected dimensions if specified\n';
    }
    
    message += '\n📂 NEXT STEPS:\n';
    message += '• Click OK to open file browser and locate the file\n';
    message += '• Click Cancel to create a placeholder instead\n';
    message += '\n⚠️  If you can\'t find the exact file, choose a similar one or cancel for placeholder.';
    
    var userChoice = confirm(message);
    if (!userChoice) {
        return createPlaceholderLayer(comp, assetId, assetName, assetType, dimensions);
    }
    
    // Show file dialog with smart filters and detailed title
    var fileFilter = getSmartFileFilter(assetType);
    var dialogTitle = 'SELECT ' + assetType.toUpperCase() + ': ' + (assetName || assetId);
    if (description) {
        dialogTitle += ' (' + description + ')';
    }
    
    var selectedFile = File.openDialog(dialogTitle, fileFilter);
    
    if (selectedFile && selectedFile.exists) {
        return importAssetFile(comp, assetId, selectedFile, assetName);
    } else {
        alert('❌ No file selected. Creating placeholder layer instead.');
        return createPlaceholderLayer(comp, assetId, assetName, assetType, dimensions);
    }
}

function importAssetFile(comp, assetId, file, assetName) {
    try {
        var importOptions = new ImportOptions(file);
        var footage = app.project.importFile(importOptions);
        var layer = comp.layers.add(footage);
        layer.name = assetName || assetId;
        return layer;
    } catch (e) {
        alert('Error importing file: ' + file.fsName + '\nError: ' + e.toString() + '\nCreating placeholder layer.');
        return createNullLayer(comp, (assetName || assetId) + ' (Import Error)');
    }
}

function createPlaceholderLayer(comp, assetId, assetName, assetType, dimensions) {
    var placeholderName = (assetName || assetId) + ' [MISSING ' + assetType.toUpperCase() + ']';
    
    // Create different placeholder types based on asset type
    if (assetType === 'image') {
        // Create a colored solid as image placeholder
        var width = 500, height = 500;
        if (dimensions) {
            var parts = dimensions.split('x');
            if (parts.length === 2) {
                width = parseInt(parts[0]) || 500;
                height = parseInt(parts[1]) || 500;
            }
        }
        var placeholder = comp.layers.addSolid([0.8, 0.2, 0.2], placeholderName, width, height, 1.0);
        return placeholder;
    } else if (assetType === 'audio') {
        // Create null layer for audio placeholder
        var placeholder = comp.layers.addNull();
        placeholder.name = placeholderName;
        return placeholder;
    } else if (assetType === 'video') {
        // Create solid for video placeholder
        var placeholder = comp.layers.addSolid([0.2, 0.2, 0.8], placeholderName, 1920, 1080, 1.0);
        return placeholder;
    } else {
        // Generic null layer placeholder
        var placeholder = comp.layers.addNull();
        placeholder.name = placeholderName;
        return placeholder;
    }
}

function getSmartFileFilter(assetType) {
    switch (assetType.toLowerCase()) {
        case 'image':
            return 'Image Files:*.png;*.jpg;*.jpeg;*.gif;*.bmp;*.tiff;*.tga;*.psd;*.ai;*.eps';
        case 'video':
            return 'Video Files:*.mp4;*.mov;*.avi;*.wmv;*.flv;*.webm;*.mkv;*.m4v;*.3gp';
        case 'audio':
            return 'Audio Files:*.mp3;*.wav;*.aac;*.ogg;*.flac;*.m4a;*.wma;*.aiff';
        default:
            return 'All Files:*.*';
    }
}

function getFileTypesForAsset(assetType) {
    switch (assetType.toLowerCase()) {
        case 'image':
            return 'Images:*.png;*.jpg;*.jpeg;*.gif;*.bmp;*.tiff;*.psd';
        case 'video':
            return 'Videos:*.mp4;*.mov;*.avi;*.wmv;*.flv;*.webm;*.mkv';
        case 'audio':
            return 'Audio:*.mp3;*.wav;*.aac;*.ogg;*.flac;*.m4a';
        default:
            return 'All Files:*.*';
    }
}

// Create main composition: Logo 1
var comp = app.project.items.addComp('Logo 1', 1920, 1080, 1.0, 5.000, 24);
var frameRate = 24;

// Asset References and Precomps
// Asset: image_0 (image)
// Asset: audio_0 (image)
// Create precomp: Audio 1
var comp_0 = app.project.items.addComp('Audio 1', 1920, 1080, 1.0, 5.000, 24);

// Processing 2 layers in Audio 1
// Preserving original JSON layer order for correct visual stacking
// Audio Logo 1 (index 1)
// Audio Logo 1 (index 2)
// Layer 1: Audio Logo 1
var layer_1 = createNullLayer(comp_0, 'Audio Logo 1 (Missing Precomp: comp_1)');
// Warning: Precomp comp_1 not found
layer_1.inPoint = 2.000000;
layer_1.outPoint = 5.000000;
layer_1.startTime = -1.458333;
layer_1.property('Transform').property('Opacity').setValue(100);
layer_1.property('Transform').property('Position').setValue([960, 540, 0]);
layer_1.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_1.property('Transform').property('Rotation').setValue(0);
layer_1.property('Transform').property('Anchor Point').setValue([960, 540, 0]);

// Layer 2: Audio Logo 1
var layer_2 = createNullLayer(comp_0, 'Audio Logo 1 (Missing Precomp: comp_1)');
// Warning: Precomp comp_1 not found
layer_2.inPoint = 0.291667;
layer_2.outPoint = 2.000000;
layer_2.startTime = 0.291667;
layer_2.property('Transform').property('Opacity').setValue(100);
layer_2.property('Transform').property('Position').setValue([960, 540, 0]);
layer_2.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_2.property('Transform').property('Rotation').setValue(0);
layer_2.property('Transform').property('Anchor Point').setValue([960, 540, 0]);


// Create precomp: Audio Logo 1
var comp_1 = app.project.items.addComp('Audio Logo 1', 1920, 1080, 1.0, 6.478, 24);

// Processing 1 layers in Audio Logo 1
// Preserving original JSON layer order for correct visual stacking
// Audio L1.mp3 (index 1)
// Layer 1: Audio L1.mp3
// Audio Layer: Audio L1.mp3 (Asset: audio_0)
var layer_1 = importAssetWithDialog(comp_1, 'audio_0', 'images/aud_0.mp3', 'audio', 'Audio L1.mp3', '', 'Media asset for the animation');
layer_1.inPoint = 0.000000;
layer_1.outPoint = 6.477982;
layer_1.startTime = 0.000000;


// Create precomp: Logo
var comp_2 = app.project.items.addComp('Logo', 1920, 1080, 1.0, 11.000, 24);

// Processing 1 layers in Logo
// Preserving original JSON layer order for correct visual stacking
// Logo_Placeholder.png (index 1)
// Layer 1: Logo_Placeholder.png
// Image Layer: Logo_Placeholder.png (Asset: image_0)
var layer_1 = importAssetWithDialog(comp_2, 'image_0', 'images/img_0.png', 'image', 'Logo_Placeholder.png', '500x500', 'Company or brand logo image');
layer_1.inPoint = 0.000000;
layer_1.outPoint = 11.000000;
layer_1.startTime = 0.000000;
layer_1.property('Transform').property('Opacity').setValue(100);
layer_1.property('Transform').property('Position').setValue([250, 250, 0]);
layer_1.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_1.property('Transform').property('Rotation').setValue(0);
layer_1.property('Transform').property('Anchor Point').setValue([250, 250, 0]);


// Create precomp: Tagline
var comp_3 = app.project.items.addComp('Tagline', 1920, 1080, 1.0, 11.125, 24);

// Processing 1 layers in Tagline
// Preserving original JSON layer order for correct visual stacking
// WWW.ENVATO.COM (index 1)
// Layer 1: WWW.ENVATO.COM
var layer_1 = createTextLayer(comp_3, 'WWW.ENVATO.COM');
var textDoc = layer_1.property('Source Text');
var textValue = textDoc.value;
textValue.text = 'WWW.ENVATO.COM';
textValue.font = 'Calibri';
textValue.fontSize = 50;
textValue.fillColor = [1.000000, 1.000000, 1.000000];
textValue.justification = ParagraphJustification.LEFT_JUSTIFY;
textDoc.setValue(textValue);
layer_1.inPoint = 0.000000;
layer_1.outPoint = 11.125000;
layer_1.startTime = -6.125000;
layer_1.property('Transform').property('Opacity').setValue(100);
layer_1.property('Transform').property('Position').setValue([23, 183, 0]);
layer_1.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_1.property('Transform').property('Rotation').setValue(0);
layer_1.property('Transform').property('Anchor Point').setValue([-2, -33, 0]);



// Font References
// Font: Calibri (Calibri)

// Creating 17 layers
// Preserving original JSON layer order for correct visual stacking
// Audio 1 (index 1)
// Center Position (index 3)
// Master (index 4)
// Line Burst 2 (index 5)
// Line Burst 1 (index 6)
// Logo Mask (index 7)
// Logo (index 8)
// Circle 1 (index 9)
// Line 4 (index 10)
// Line 3 (index 11)
// Line 2 (index 12)
// Line 1 (index 13)
// Tagline Transform Null (index 14)
// Line Split (index 15)
// Tagline Mask (index 16)
// Tagline (index 17)
// BG  (index 18)
// Layer 1: Audio 1
var layer_1 = comp.layers.add(comp_0);
layer_1.name = 'Audio 1';
layer_1.inPoint = 0.000000;
layer_1.outPoint = 5.000000;
layer_1.startTime = 0.000000;
layer_1.property('Transform').property('Opacity').setValue(100);
layer_1.property('Transform').property('Position').setValue([960, 540, 0]);
layer_1.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_1.property('Transform').property('Rotation').setValue(0);
layer_1.property('Transform').property('Anchor Point').setValue([960, 540, 0]);

// Layer 3: Center Position
var layer_3 = createNullLayer(comp, 'Center Position');
layer_3.inPoint = 0.000000;
layer_3.outPoint = 5.000000;
layer_3.startTime = 0.000000;
layer_3.property('Transform').property('Opacity').setValue(0);
layer_3.property('Transform').property('Position').setValue([994, 540, 0]);
layer_3.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_3.property('Transform').property('Rotation').setValue(0);
layer_3.property('Transform').property('Anchor Point').setValue([0, 0, 0]);

// Layer 4: Master
var layer_4 = createNullLayer(comp, 'Master');
layer_4.inPoint = 0.000000;
layer_4.outPoint = 5.000000;
layer_4.startTime = 0.000000;
layer_4.parent = layer_3;
layer_4.property('Transform').property('Opacity').setValue(0);
// Position has separate dimensions - combining for AE
layer_4.property('Transform').property('Position').setValueAtTime(0/frameRate, [0, 0]);
layer_4.property('Transform').property('Position').setValueAtTime(45/frameRate, [0, 0]);
layer_4.property('Transform').property('Position').setValueAtTime(52/frameRate, [-144, 0]);
layer_4.property('Transform').property('Scale').setValue([75, 75, 100]);
layer_4.property('Transform').property('Rotation').setValue(0);
layer_4.property('Transform').property('Anchor Point').setValue([0, 0, 0]);

// Layer 6: Line Burst 1
var layer_6 = createShapeLayer(comp, 'Line Burst 1');
// Shape Group: Shape 1
var layer_6_group_0 = layer_6.property('Contents').addProperty('ADBE Vector Group');
layer_6_group_0.name = 'Shape 1';
var layer_6_group_0_path_0 = layer_6_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[29, 152], [214.5, 152]];
pathShape.inTangents = [[0, 0], [0, 0]];
pathShape.outTangents = [[0, 0], [0, 0]];
pathShape.closed = false;
layer_6_group_0_path_0.property('Path').setValue(pathShape);
var layer_6_group_0_stroke_1 = layer_6_group_0.property('Contents').addProperty('ADBE Vector Graphic - Stroke');
layer_6_group_0_stroke_1.property('Color').setValue([0.662745, 0.909804, 0.270588]);
layer_6_group_0_stroke_1.property('Stroke Width').setValue(3);
layer_6_group_0_stroke_1.property('Opacity').setValue(100);
var layer_6_group_0_transform = layer_6_group_0.property('Transform');
layer_6_group_0_transform.property('Position').setValue([0, 0]);
layer_6_group_0_transform.property('Scale').setValue([100, 100]);
layer_6_group_0_transform.property('Rotation').setValue(0);
layer_6_group_0_transform.property('Opacity').setValue(100);
var layer_6_trim_1 = layer_6.property('Contents').addProperty('ADBE Vector Filter - Trim');
// Smart detection: 2 keyframes for layer_6_trim_1.property('Start')
layer_6_trim_1.property('Start').setValueAtTime(33/frameRate, 0);
layer_6_trim_1.property('Start').setValueAtTime(37/frameRate, 100);
// Smart detection: 2 keyframes for layer_6_trim_1.property('End')
layer_6_trim_1.property('End').setValueAtTime(29/frameRate, 0);
layer_6_trim_1.property('End').setValueAtTime(33/frameRate, 100);
layer_6_trim_1.property('Offset').setValue(0);
// Fill Effect: Fill
var effect_0 = layer_6.property('Effects').addProperty('ADBE Fill');
effect_0.name = 'Fill';
// Expression for Fill > Color
layer_6_effect_0.property('Color').expression = 'thisComp.layer(\'Color Control\').effect(\'Line Burst \')(\'ADBE Color Control-0001\')';
layer_6.inPoint = 1.250000;
layer_6.outPoint = 1.541667;
layer_6.startTime = 0.000000;
layer_6.parent = layer_4;
layer_6.property('Transform').property('Opacity').setValue(100);
layer_6.property('Transform').property('Position').setValue([-19, -5, 0]);
layer_6.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_6.property('Transform').property('Rotation').setValue(0);
layer_6.property('Transform').property('Anchor Point').setValue([0, 0, 0]);
// Fill Effect: Fill
var effect_0 = layer_6.property('Effects').addProperty('ADBE Fill');
effect_0.name = 'Fill';

// Layer 5: Line Burst 2
var layer_5 = createShapeLayer(comp, 'Line Burst 2');
// Shape Group: Shape 1
var layer_5_group_0 = layer_5.property('Contents').addProperty('ADBE Vector Group');
layer_5_group_0.name = 'Shape 1';
var layer_5_group_0_path_0 = layer_5_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[29, 152], [214.5, 152]];
pathShape.inTangents = [[0, 0], [0, 0]];
pathShape.outTangents = [[0, 0], [0, 0]];
pathShape.closed = false;
layer_5_group_0_path_0.property('Path').setValue(pathShape);
var layer_5_group_0_stroke_1 = layer_5_group_0.property('Contents').addProperty('ADBE Vector Graphic - Stroke');
layer_5_group_0_stroke_1.property('Color').setValue([0.662745, 0.909804, 0.270588]);
layer_5_group_0_stroke_1.property('Stroke Width').setValue(3);
layer_5_group_0_stroke_1.property('Opacity').setValue(100);
var layer_5_group_0_transform = layer_5_group_0.property('Transform');
layer_5_group_0_transform.property('Position').setValue([0, 0]);
layer_5_group_0_transform.property('Scale').setValue([100, 100]);
layer_5_group_0_transform.property('Rotation').setValue(0);
layer_5_group_0_transform.property('Opacity').setValue(100);
var layer_5_trim_1 = layer_5.property('Contents').addProperty('ADBE Vector Filter - Trim');
// Smart detection: 2 keyframes for layer_5_trim_1.property('Start')
layer_5_trim_1.property('Start').setValueAtTime(33/frameRate, 0);
layer_5_trim_1.property('Start').setValueAtTime(37/frameRate, 100);
// Smart detection: 2 keyframes for layer_5_trim_1.property('End')
layer_5_trim_1.property('End').setValueAtTime(29/frameRate, 0);
layer_5_trim_1.property('End').setValueAtTime(33/frameRate, 100);
layer_5_trim_1.property('Offset').setValue(0);
// Fill Effect: Fill
var effect_0 = layer_5.property('Effects').addProperty('ADBE Fill');
effect_0.name = 'Fill';
// Expression for Fill > Color
layer_5_effect_0.property('Color').expression = 'thisComp.layer(\'Color Control\').effect(\'Line Burst \')(\'ADBE Color Control-0001\')';
layer_5.inPoint = 1.250000;
layer_5.outPoint = 1.541667;
layer_5.startTime = 0.000000;
layer_5.parent = layer_6;
layer_5.property('Transform').property('Opacity').setValue(100);
layer_5.property('Transform').property('Position').setValue([56, 304, 0]);
layer_5.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_5.property('Transform').property('Rotation').setValue(180);
layer_5.property('Transform').property('Anchor Point').setValue([0, 0, 0]);
// Fill Effect: Fill
var effect_0 = layer_5.property('Effects').addProperty('ADBE Fill');
effect_0.name = 'Fill';

// Layer 7: Logo Mask
var layer_7 = createShapeLayer(comp, 'Logo Mask');
// Shape Group: Group 1
var layer_7_group_0 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_0.name = 'Group 1';
var layer_7_group_0_ellipse_0 = layer_7_group_0.property('Contents').addProperty('ADBE Vector Shape - Ellipse');
layer_7_group_0_ellipse_0.property('Size').setValue([300, 300]);
layer_7_group_0_ellipse_0.property('Position').setValue([0, 0]);
var layer_7_group_0_transform = layer_7_group_0.property('Transform');
layer_7_group_0_transform.property('Position').setValue([0, 0]);
layer_7_group_0_transform.property('Scale').setValue([100, 100]);
layer_7_group_0_transform.property('Rotation').setValue(0);
layer_7_group_0_transform.property('Opacity').setValue(100);
var layer_7_fill_1 = layer_7.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_fill_1.property('Color').setValue([1.000000, 0.000000, 0.000000]);
// Fill Effect: Fill
var effect_0 = layer_7.property('Effects').addProperty('ADBE Fill');
effect_0.name = 'Fill';
layer_7.inPoint = 0.000000;
layer_7.outPoint = 5.000000;
layer_7.startTime = 0.000000;
layer_7.parent = layer_4;
// layer_7 is a track matte
layer_7.enabled = false; // Hide matte layer
layer_7.property('Transform').property('Opacity').setValue(100);
// Position has separate dimensions - combining for AE
layer_7.property('Transform').property('Position').setValueAtTime(18/frameRate, [0, 0]);
layer_7.property('Transform').property('Position').setValueAtTime(23/frameRate, [0, -190]);
layer_7.property('Transform').property('Position').setValueAtTime(26/frameRate, [0, -190]);
layer_7.property('Transform').property('Position').setValueAtTime(30/frameRate, [0, 0]);
layer_7.property('Transform').property('Position').setValueAtTime(31/frameRate, [0, 19]);
layer_7.property('Transform').property('Position').setValueAtTime(32/frameRate, [0, 10]);
layer_7.property('Transform').property('Position').setValueAtTime(33/frameRate, [0, -4]);
layer_7.property('Transform').property('Position').setValueAtTime(34/frameRate, [0, -10.5]);
layer_7.property('Transform').property('Position').setValueAtTime(35/frameRate, [0, -8.5]);
layer_7.property('Transform').property('Position').setValueAtTime(36/frameRate, [0, -3]);
layer_7.property('Transform').property('Position').setValueAtTime(37/frameRate, [0, 0]);
layer_7.property('Transform').property('Position').setValueAtTime(43/frameRate, [0, 0]);
layer_7.property('Transform').property('Position').setValueAtTime(46/frameRate, [35, 0]);
layer_7.property('Transform').property('Position').setValueAtTime(52/frameRate, [-363, 0]);
layer_7.property('Transform').property('Position').setValueAtTime(57/frameRate, [-330, 0]);
layer_7.property('Transform').property('Position').setValueAtTime(60/frameRate, [-335, 0]);
// Smart detection: 20 keyframes for layer_7.property('Transform').property('Scale')
layer_7.property('Transform').property('Scale').setValueAtTime(12/frameRate, [0, 0, 100]);
layer_7.property('Transform').property('Scale').setValueAtTime(18/frameRate, [120, 80, 100]);
layer_7.property('Transform').property('Scale').setValueAtTime(21/frameRate, [85, 115, 100]);
layer_7.property('Transform').property('Scale').setValueAtTime(23/frameRate, [110, 90, 100]);
layer_7.property('Transform').property('Scale').setValueAtTime(26/frameRate, [100, 100, 100]);
layer_7.property('Transform').property('Scale').setValueAtTime(29/frameRate, [85, 115, 100]);
layer_7.property('Transform').property('Scale').setValueAtTime(31/frameRate, [115, 85, 100]);
layer_7.property('Transform').property('Scale').setValueAtTime(34/frameRate, [95, 105, 100]);
layer_7.property('Transform').property('Scale').setValueAtTime(36/frameRate, [100, 100, 100]);
layer_7.property('Transform').property('Scale').setValueAtTime(38/frameRate, [103, 97, 100]);
layer_7.property('Transform').property('Scale').setValueAtTime(40/frameRate, [100, 100, 100]);
layer_7.property('Transform').property('Scale').setValueAtTime(43/frameRate, [100, 100, 100]);
layer_7.property('Transform').property('Scale').setValueAtTime(46/frameRate, [120, 80, 100]);
layer_7.property('Transform').property('Scale').setValueAtTime(49/frameRate, [140, 60, 100]);
layer_7.property('Transform').property('Scale').setValueAtTime(52/frameRate, [120, 80, 100]);
layer_7.property('Transform').property('Scale').setValueAtTime(55/frameRate, [90, 110, 100]);
layer_7.property('Transform').property('Scale').setValueAtTime(58/frameRate, [105, 95, 100]);
layer_7.property('Transform').property('Scale').setValueAtTime(60/frameRate, [98, 102, 100]);
layer_7.property('Transform').property('Scale').setValueAtTime(62/frameRate, [101, 99, 100]);
layer_7.property('Transform').property('Scale').setValueAtTime(64/frameRate, [100, 100, 100]);
layer_7.property('Transform').property('Rotation').setValue(0);
layer_7.property('Transform').property('Anchor Point').setValue([0, 0, 0]);
// Fill Effect: Fill
var effect_0 = layer_7.property('Effects').addProperty('ADBE Fill');
effect_0.name = 'Fill';

// Layer 8: Logo
var layer_8 = comp.layers.add(comp_2);
layer_8.name = 'Logo';
layer_8.inPoint = 2.000000;
layer_8.outPoint = 9.125000;
layer_8.startTime = 0.375000;
layer_8.parent = layer_4;
// layer_8 uses track matte - will be configured later
layer_8.property('Transform').property('Opacity').setValue(100);
// Position has separate dimensions - combining for AE
layer_8.property('Transform').property('Position').setValueAtTime(0/frameRate, [0, 0]);
layer_8.property('Transform').property('Position').setValueAtTime(49/frameRate, [-332, 0]);
layer_8.property('Transform').property('Position').setValueAtTime(53/frameRate, [-414, 0]);
layer_8.property('Transform').property('Position').setValueAtTime(58/frameRate, [-317, 0]);
layer_8.property('Transform').property('Position').setValueAtTime(61/frameRate, [-340, 0]);
// Smart detection: 7 keyframes for layer_8.property('Transform').property('Scale')
layer_8.property('Transform').property('Scale').setValueAtTime(49/frameRate, [80, 40, 100]);
layer_8.property('Transform').property('Scale').setValueAtTime(53/frameRate, [60, 40, 100]);
layer_8.property('Transform').property('Scale').setValueAtTime(57/frameRate, [60, 80, 100]);
layer_8.property('Transform').property('Scale').setValueAtTime(61/frameRate, [65, 55, 100]);
layer_8.property('Transform').property('Scale').setValueAtTime(64/frameRate, [58, 62, 100]);
layer_8.property('Transform').property('Scale').setValueAtTime(67/frameRate, [61, 59, 100]);
layer_8.property('Transform').property('Scale').setValueAtTime(70/frameRate, [60, 60, 100]);
layer_8.property('Transform').property('Rotation').setValue(0);
layer_8.property('Transform').property('Anchor Point').setValue([250, 250, 0]);
// Fill Effect: Fill
var effect_0 = layer_8.property('Effects').addProperty('ADBE Fill');
effect_0.name = 'Fill';

// Layer 9: Circle 1
var layer_9 = createShapeLayer(comp, 'Circle 1');
// Shape Group: Group 1
var layer_9_group_0 = layer_9.property('Contents').addProperty('ADBE Vector Group');
layer_9_group_0.name = 'Group 1';
var layer_9_group_0_ellipse_0 = layer_9_group_0.property('Contents').addProperty('ADBE Vector Shape - Ellipse');
layer_9_group_0_ellipse_0.property('Size').setValue([300, 300]);
layer_9_group_0_ellipse_0.property('Position').setValue([0, 0]);
var layer_9_group_0_transform = layer_9_group_0.property('Transform');
layer_9_group_0_transform.property('Position').setValue([0, 0]);
layer_9_group_0_transform.property('Scale').setValue([100, 100]);
layer_9_group_0_transform.property('Rotation').setValue(0);
layer_9_group_0_transform.property('Opacity').setValue(100);
var layer_9_fill_1 = layer_9.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_9_fill_1.property('Color').setValue([1.000000, 0.000000, 0.000000]);
// Fill Effect: Fill
var effect_0 = layer_9.property('Effects').addProperty('ADBE Fill');
effect_0.name = 'Fill';
// Expression for Fill > Color
layer_9_effect_0.property('Color').expression = 'thisComp.layer(\'Color Control\').effect(\'Circle\')(\'ADBE Color Control-0001\')';
layer_9.inPoint = 0.000000;
layer_9.outPoint = 5.000000;
layer_9.startTime = 0.000000;
layer_9.parent = layer_4;
layer_9.property('Transform').property('Opacity').setValue(100);
// Position has separate dimensions - combining for AE
layer_9.property('Transform').property('Position').setValueAtTime(18/frameRate, [0, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(23/frameRate, [0, -190]);
layer_9.property('Transform').property('Position').setValueAtTime(26/frameRate, [0, -190]);
layer_9.property('Transform').property('Position').setValueAtTime(30/frameRate, [0, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(31/frameRate, [0, 19]);
layer_9.property('Transform').property('Position').setValueAtTime(32/frameRate, [0, 10]);
layer_9.property('Transform').property('Position').setValueAtTime(33/frameRate, [0, -4]);
layer_9.property('Transform').property('Position').setValueAtTime(34/frameRate, [0, -10.5]);
layer_9.property('Transform').property('Position').setValueAtTime(35/frameRate, [0, -8.5]);
layer_9.property('Transform').property('Position').setValueAtTime(36/frameRate, [0, -3]);
layer_9.property('Transform').property('Position').setValueAtTime(37/frameRate, [0, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(43/frameRate, [0, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(46/frameRate, [35, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(52/frameRate, [-363, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(57/frameRate, [-330, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(60/frameRate, [-335, 0]);
// Smart detection: 20 keyframes for layer_9.property('Transform').property('Scale')
layer_9.property('Transform').property('Scale').setValueAtTime(12/frameRate, [0, 0, 100]);
layer_9.property('Transform').property('Scale').setValueAtTime(18/frameRate, [120, 80, 100]);
layer_9.property('Transform').property('Scale').setValueAtTime(21/frameRate, [85, 115, 100]);
layer_9.property('Transform').property('Scale').setValueAtTime(23/frameRate, [110, 90, 100]);
layer_9.property('Transform').property('Scale').setValueAtTime(26/frameRate, [100, 100, 100]);
layer_9.property('Transform').property('Scale').setValueAtTime(29/frameRate, [85, 115, 100]);
layer_9.property('Transform').property('Scale').setValueAtTime(31/frameRate, [115, 85, 100]);
layer_9.property('Transform').property('Scale').setValueAtTime(34/frameRate, [95, 105, 100]);
layer_9.property('Transform').property('Scale').setValueAtTime(36/frameRate, [100, 100, 100]);
layer_9.property('Transform').property('Scale').setValueAtTime(38/frameRate, [103, 97, 100]);
layer_9.property('Transform').property('Scale').setValueAtTime(40/frameRate, [100, 100, 100]);
layer_9.property('Transform').property('Scale').setValueAtTime(43/frameRate, [100, 100, 100]);
layer_9.property('Transform').property('Scale').setValueAtTime(46/frameRate, [120, 80, 100]);
layer_9.property('Transform').property('Scale').setValueAtTime(49/frameRate, [140, 60, 100]);
layer_9.property('Transform').property('Scale').setValueAtTime(52/frameRate, [120, 80, 100]);
layer_9.property('Transform').property('Scale').setValueAtTime(55/frameRate, [90, 110, 100]);
layer_9.property('Transform').property('Scale').setValueAtTime(58/frameRate, [105, 95, 100]);
layer_9.property('Transform').property('Scale').setValueAtTime(60/frameRate, [98, 102, 100]);
layer_9.property('Transform').property('Scale').setValueAtTime(62/frameRate, [101, 99, 100]);
layer_9.property('Transform').property('Scale').setValueAtTime(64/frameRate, [100, 100, 100]);
layer_9.property('Transform').property('Rotation').setValue(0);
layer_9.property('Transform').property('Anchor Point').setValue([0, 0, 0]);
// Fill Effect: Fill
var effect_0 = layer_9.property('Effects').addProperty('ADBE Fill');
effect_0.name = 'Fill';

// Layer 10: Line 4
var layer_10 = createShapeLayer(comp, 'Line 4');
// Shape Group: Shape 1
var layer_10_group_0 = layer_10.property('Contents').addProperty('ADBE Vector Group');
layer_10_group_0.name = 'Shape 1';
var layer_10_group_0_path_0 = layer_10_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-292, -41], [-3, -41]];
pathShape.inTangents = [[0, 0], [0, 0]];
pathShape.outTangents = [[0, 0], [0, 0]];
pathShape.closed = false;
layer_10_group_0_path_0.property('Path').setValue(pathShape);
var layer_10_group_0_transform = layer_10_group_0.property('Transform');
layer_10_group_0_transform.property('Position').setValue([0, 0]);
layer_10_group_0_transform.property('Scale').setValue([100, 100]);
layer_10_group_0_transform.property('Rotation').setValue(0);
layer_10_group_0_transform.property('Opacity').setValue(100);
var layer_10_trim_1 = layer_10.property('Contents').addProperty('ADBE Vector Filter - Trim');
// Smart detection: 2 keyframes for layer_10_trim_1.property('Start')
layer_10_trim_1.property('Start').setValueAtTime(52/frameRate, 100);
layer_10_trim_1.property('Start').setValueAtTime(56/frameRate, 0);
// Smart detection: 2 keyframes for layer_10_trim_1.property('End')
layer_10_trim_1.property('End').setValueAtTime(56/frameRate, 100);
layer_10_trim_1.property('End').setValueAtTime(59/frameRate, 0);
layer_10_trim_1.property('Offset').setValue(0);
var layer_10_stroke_2 = layer_10.property('Contents').addProperty('ADBE Vector Graphic - Stroke');
layer_10_stroke_2.property('Color').setValue([0.662745, 0.909804, 0.270588]);
layer_10_stroke_2.property('Stroke Width').setValue(0.5);
// Fill Effect: Fill
var effect_0 = layer_10.property('Effects').addProperty('ADBE Fill');
effect_0.name = 'Fill';
// Expression for Fill > Color
layer_10_effect_0.property('Color').expression = 'thisComp.layer(\'Color Control\').effect(\'Lines\')(\'ADBE Color Control-0001\')';
layer_10.inPoint = 2.166667;
layer_10.outPoint = 5.000000;
layer_10.startTime = 0.166667;
layer_10.parent = layer_3;
layer_10.property('Transform').property('Opacity').setValue(100);
// Position has separate dimensions - combining for AE
layer_10.property('Transform').property('Position').setValueAtTime(0/frameRate, [-6, 82]);
layer_10.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_10.property('Transform').property('Rotation').setValue(0);
layer_10.property('Transform').property('Anchor Point').setValue([0, 0, 0]);
// Fill Effect: Fill
var effect_0 = layer_10.property('Effects').addProperty('ADBE Fill');
effect_0.name = 'Fill';

// Layer 11: Line 3
var layer_11 = createShapeLayer(comp, 'Line 3');
// Shape Group: Shape 1
var layer_11_group_0 = layer_11.property('Contents').addProperty('ADBE Vector Group');
layer_11_group_0.name = 'Shape 1';
var layer_11_group_0_path_0 = layer_11_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-292, -41], [-3, -41]];
pathShape.inTangents = [[0, 0], [0, 0]];
pathShape.outTangents = [[0, 0], [0, 0]];
pathShape.closed = false;
layer_11_group_0_path_0.property('Path').setValue(pathShape);
var layer_11_group_0_transform = layer_11_group_0.property('Transform');
layer_11_group_0_transform.property('Position').setValue([0, 0]);
layer_11_group_0_transform.property('Scale').setValue([100, 100]);
layer_11_group_0_transform.property('Rotation').setValue(0);
layer_11_group_0_transform.property('Opacity').setValue(100);
var layer_11_trim_1 = layer_11.property('Contents').addProperty('ADBE Vector Filter - Trim');
// Smart detection: 2 keyframes for layer_11_trim_1.property('Start')
layer_11_trim_1.property('Start').setValueAtTime(50/frameRate, 100);
layer_11_trim_1.property('Start').setValueAtTime(54/frameRate, 0);
// Smart detection: 2 keyframes for layer_11_trim_1.property('End')
layer_11_trim_1.property('End').setValueAtTime(54/frameRate, 100);
layer_11_trim_1.property('End').setValueAtTime(57/frameRate, 0);
layer_11_trim_1.property('Offset').setValue(0);
var layer_11_stroke_2 = layer_11.property('Contents').addProperty('ADBE Vector Graphic - Stroke');
layer_11_stroke_2.property('Color').setValue([0.662745, 0.909804, 0.270588]);
layer_11_stroke_2.property('Stroke Width').setValue(2);
// Fill Effect: Fill
var effect_0 = layer_11.property('Effects').addProperty('ADBE Fill');
effect_0.name = 'Fill';
// Expression for Fill > Color
layer_11_effect_0.property('Color').expression = 'thisComp.layer(\'Color Control\').effect(\'Lines\')(\'ADBE Color Control-0001\')';
layer_11.inPoint = 2.083333;
layer_11.outPoint = 5.000000;
layer_11.startTime = 0.083333;
layer_11.parent = layer_3;
layer_11.property('Transform').property('Opacity').setValue(100);
// Position has separate dimensions - combining for AE
layer_11.property('Transform').property('Position').setValueAtTime(0/frameRate, [-16, 104]);
layer_11.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_11.property('Transform').property('Rotation').setValue(0);
layer_11.property('Transform').property('Anchor Point').setValue([0, 0, 0]);
// Fill Effect: Fill
var effect_0 = layer_11.property('Effects').addProperty('ADBE Fill');
effect_0.name = 'Fill';

// Layer 12: Line 2
var layer_12 = createShapeLayer(comp, 'Line 2');
// Shape Group: Shape 1
var layer_12_group_0 = layer_12.property('Contents').addProperty('ADBE Vector Group');
layer_12_group_0.name = 'Shape 1';
var layer_12_group_0_path_0 = layer_12_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-292, -41], [-3, -41]];
pathShape.inTangents = [[0, 0], [0, 0]];
pathShape.outTangents = [[0, 0], [0, 0]];
pathShape.closed = false;
layer_12_group_0_path_0.property('Path').setValue(pathShape);
var layer_12_group_0_transform = layer_12_group_0.property('Transform');
layer_12_group_0_transform.property('Position').setValue([0, 0]);
layer_12_group_0_transform.property('Scale').setValue([100, 100]);
layer_12_group_0_transform.property('Rotation').setValue(0);
layer_12_group_0_transform.property('Opacity').setValue(100);
var layer_12_trim_1 = layer_12.property('Contents').addProperty('ADBE Vector Filter - Trim');
// Smart detection: 2 keyframes for layer_12_trim_1.property('Start')
layer_12_trim_1.property('Start').setValueAtTime(49/frameRate, 100);
layer_12_trim_1.property('Start').setValueAtTime(53/frameRate, 0);
// Smart detection: 2 keyframes for layer_12_trim_1.property('End')
layer_12_trim_1.property('End').setValueAtTime(53/frameRate, 100);
layer_12_trim_1.property('End').setValueAtTime(56/frameRate, 0);
layer_12_trim_1.property('Offset').setValue(0);
var layer_12_stroke_2 = layer_12.property('Contents').addProperty('ADBE Vector Graphic - Stroke');
layer_12_stroke_2.property('Color').setValue([0.662745, 0.909804, 0.270588]);
layer_12_stroke_2.property('Stroke Width').setValue(1);
// Fill Effect: Fill
var effect_0 = layer_12.property('Effects').addProperty('ADBE Fill');
effect_0.name = 'Fill';
// Expression for Fill > Color
layer_12_effect_0.property('Color').expression = 'thisComp.layer(\'Color Control\').effect(\'Lines\')(\'ADBE Color Control-0001\')';
layer_12.inPoint = 2.041667;
layer_12.outPoint = 5.000000;
layer_12.startTime = 0.041667;
layer_12.parent = layer_3;
layer_12.property('Transform').property('Opacity').setValue(100);
// Position has separate dimensions - combining for AE
layer_12.property('Transform').property('Position').setValueAtTime(0/frameRate, [-16, 14]);
layer_12.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_12.property('Transform').property('Rotation').setValue(0);
layer_12.property('Transform').property('Anchor Point').setValue([0, 0, 0]);
// Fill Effect: Fill
var effect_0 = layer_12.property('Effects').addProperty('ADBE Fill');
effect_0.name = 'Fill';

// Layer 13: Line 1
var layer_13 = createShapeLayer(comp, 'Line 1');
// Shape Group: Shape 1
var layer_13_group_0 = layer_13.property('Contents').addProperty('ADBE Vector Group');
layer_13_group_0.name = 'Shape 1';
var layer_13_group_0_path_0 = layer_13_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-292, -41], [-3, -41]];
pathShape.inTangents = [[0, 0], [0, 0]];
pathShape.outTangents = [[0, 0], [0, 0]];
pathShape.closed = false;
layer_13_group_0_path_0.property('Path').setValue(pathShape);
var layer_13_group_0_transform = layer_13_group_0.property('Transform');
layer_13_group_0_transform.property('Position').setValue([0, 0]);
layer_13_group_0_transform.property('Scale').setValue([100, 100]);
layer_13_group_0_transform.property('Rotation').setValue(0);
layer_13_group_0_transform.property('Opacity').setValue(100);
var layer_13_trim_1 = layer_13.property('Contents').addProperty('ADBE Vector Filter - Trim');
// Smart detection: 2 keyframes for layer_13_trim_1.property('Start')
layer_13_trim_1.property('Start').setValueAtTime(48/frameRate, 100);
layer_13_trim_1.property('Start').setValueAtTime(52/frameRate, 0);
// Smart detection: 2 keyframes for layer_13_trim_1.property('End')
layer_13_trim_1.property('End').setValueAtTime(52/frameRate, 100);
layer_13_trim_1.property('End').setValueAtTime(55/frameRate, 0);
layer_13_trim_1.property('Offset').setValue(0);
var layer_13_stroke_2 = layer_13.property('Contents').addProperty('ADBE Vector Graphic - Stroke');
layer_13_stroke_2.property('Color').setValue([0.662745, 0.909804, 0.270588]);
layer_13_stroke_2.property('Stroke Width').setValue(2);
// Fill Effect: Fill
var effect_0 = layer_13.property('Effects').addProperty('ADBE Fill');
effect_0.name = 'Fill';
// Expression for Fill > Color
layer_13_effect_0.property('Color').expression = 'thisComp.layer(\'Color Control\').effect(\'Lines\')(\'ADBE Color Control-0001\')';
layer_13.inPoint = 2.000000;
layer_13.outPoint = 5.000000;
layer_13.startTime = 0.000000;
layer_13.parent = layer_3;
layer_13.property('Transform').property('Opacity').setValue(100);
// Position has separate dimensions - combining for AE
layer_13.property('Transform').property('Position').setValueAtTime(0/frameRate, [-40, -12]);
layer_13.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_13.property('Transform').property('Rotation').setValue(0);
layer_13.property('Transform').property('Anchor Point').setValue([0, 0, 0]);
// Fill Effect: Fill
var effect_0 = layer_13.property('Effects').addProperty('ADBE Fill');
effect_0.name = 'Fill';

// Layer 14: Tagline Transform Null
var layer_14 = createNullLayer(comp, 'Tagline Transform Null');
layer_14.inPoint = 0.000000;
layer_14.outPoint = 5.000000;
layer_14.startTime = 0.000000;
layer_14.parent = layer_3;
layer_14.property('Transform').property('Opacity').setValue(0);
layer_14.property('Transform').property('Position').setValue([-184, 0, 0]);
layer_14.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_14.property('Transform').property('Rotation').setValue(0);
layer_14.property('Transform').property('Anchor Point').setValue([0, 0, 0]);

// Layer 15: Line Split
var layer_15 = createNullLayer(comp, 'Line Split');
layer_15.inPoint = 2.000000;
layer_15.outPoint = 5.000000;
layer_15.startTime = 0.000000;
layer_15.parent = layer_4;
layer_15.property('Transform').property('Opacity').setValue(100);
// Position has separate dimensions - combining for AE
layer_15.property('Transform').property('Position').setValueAtTime(0/frameRate, [0, -5]);
layer_15.property('Transform').property('Position').setValueAtTime(45/frameRate, [246, -5]);
layer_15.property('Transform').property('Position').setValueAtTime(48/frameRate, [256, -5]);
layer_15.property('Transform').property('Position').setValueAtTime(53/frameRate, [-124, -5]);
layer_15.property('Transform').property('Position').setValueAtTime(58/frameRate, [-109, -5]);
layer_15.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_15.property('Transform').property('Rotation').setValue(0);
layer_15.property('Transform').property('Anchor Point').setValue([246, -5, 0]);

// Layer 16: Tagline Mask
var layer_16 = createShapeLayer(comp, 'Tagline Mask');
// Shape Group: Group 1
var layer_16_group_0 = layer_16.property('Contents').addProperty('ADBE Vector Group');
layer_16_group_0.name = 'Group 1';
var layer_16_group_0_rect_0 = layer_16_group_0.property('Contents').addProperty('ADBE Vector Shape - Rect');
layer_16_group_0_rect_0.property('Size').setValue([933, 1095]);
layer_16_group_0_rect_0.property('Position').setValue([-532, 0]);
var layer_16_group_0_fill_1 = layer_16_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_16_group_0_fill_1.property('Color').setValue([1.000000, 0.000000, 0.000000]);
layer_16_group_0_fill_1.property('Opacity').setValue(100);
var layer_16_group_0_transform = layer_16_group_0.property('Transform');
layer_16_group_0_transform.property('Position').setValue([0, 0]);
layer_16_group_0_transform.property('Scale').setValue([100, 100]);
layer_16_group_0_transform.property('Rotation').setValue(0);
layer_16_group_0_transform.property('Opacity').setValue(100);
// Color Control Effect: Gaussian Blur (Legacy)
var effect_0 = layer_16.property('Effects').addProperty('ADBE Color Control');
effect_0.name = 'Gaussian Blur (Legacy)';
effect_0.property('Color').setValue([0.149412, 0.149412, 0.149412]);
layer_16.inPoint = 0.000000;
layer_16.outPoint = 5.000000;
layer_16.startTime = 0.000000;
layer_16.parent = layer_15;
// layer_16 is a track matte
layer_16.enabled = false; // Hide matte layer
layer_16.property('Transform').property('Opacity').setValue(100);
layer_16.property('Transform').property('Position').setValue([306, 0, 0]);
layer_16.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_16.property('Transform').property('Rotation').setValue(0);
layer_16.property('Transform').property('Anchor Point').setValue([0, 0, 0]);
// Color Control Effect: Gaussian Blur (Legacy)
var effect_0 = layer_16.property('Effects').addProperty('ADBE Color Control');
effect_0.name = 'Gaussian Blur (Legacy)';
effect_0.property('Color').setValue([0.149412, 0.149412, 0.149412]);

// Layer 17: Tagline
var layer_17 = comp.layers.add(comp_3);
layer_17.name = 'Tagline';
layer_17.inPoint = 1.958333;
layer_17.outPoint = 10.000000;
layer_17.startTime = -1.000000;
layer_17.parent = layer_14;
// layer_17 uses track matte - will be configured later
layer_17.property('Transform').property('Opacity').setValue(100);
// Position has separate dimensions - combining for AE
layer_17.property('Transform').property('Position').setValueAtTime(0/frameRate, [0, 5]);
layer_17.property('Transform').property('Position').setValueAtTime(46/frameRate, [475, 5]);
layer_17.property('Transform').property('Position').setValueAtTime(53/frameRate, [824, 5]);
layer_17.property('Transform').property('Position').setValueAtTime(57/frameRate, [804, 5]);
layer_17.property('Transform').property('Position').setValueAtTime(59/frameRate, [809, 5]);
layer_17.property('Transform').property('Position').setValueAtTime(61/frameRate, [804, 5]);
// Smart detection: 5 keyframes for layer_17.property('Transform').property('Scale')
layer_17.property('Transform').property('Scale').setValueAtTime(46/frameRate, [100, 100, 100]);
layer_17.property('Transform').property('Scale').setValueAtTime(53/frameRate, [110, 100, 100]);
layer_17.property('Transform').property('Scale').setValueAtTime(57/frameRate, [100, 100, 100]);
layer_17.property('Transform').property('Scale').setValueAtTime(59/frameRate, [101, 100, 100]);
layer_17.property('Transform').property('Scale').setValueAtTime(61/frameRate, [100, 100, 100]);
layer_17.property('Transform').property('Rotation').setValue(0);
layer_17.property('Transform').property('Anchor Point').setValue([830, 173, 0]);
// Fill Effect: Fill
var effect_0 = layer_17.property('Effects').addProperty('ADBE Fill');
effect_0.name = 'Fill';

// Layer 18: BG 
var layer_18 = createSolidLayer(comp, 'BG ', [0.121569, 0.113725, 0.086275], 1920, 1080);
layer_18.inPoint = 0.000000;
layer_18.outPoint = 5.000000;
layer_18.startTime = 0.000000;
layer_18.property('Transform').property('Opacity').setValue(100);
layer_18.property('Transform').property('Position').setValue([960, 540, 0]);
layer_18.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_18.property('Transform').property('Rotation').setValue(0);
layer_18.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
// Fill Effect: Fill
var effect_0 = layer_18.property('Effects').addProperty('ADBE Fill');
effect_0.name = 'Fill';


// Set up track matte relationships
// Set up track matte: layer_8 uses layer_7 as TrackMatteType.ALPHA
try {
    // Ensure track matte layer is directly above the layer that uses it
    layer_7.moveBefore(layer_8);
    // Set the track matte type
    layer_8.trackMatteType = TrackMatteType.ALPHA;
    // IMPORTANT: Hide the matte layer (turn off visibility)
    layer_7.enabled = false;
} catch(e) {
    // Track matte setup failed for layer_8
    alert('Track matte setup failed for layer_8: ' + e.toString());
}
// Set up track matte: layer_17 uses layer_16 as TrackMatteType.ALPHA_INVERTED
try {
    // Ensure track matte layer is directly above the layer that uses it
    layer_16.moveBefore(layer_17);
    // Set the track matte type
    layer_17.trackMatteType = TrackMatteType.ALPHA_INVERTED;
    // IMPORTANT: Hide the matte layer (turn off visibility)
    layer_16.enabled = false;
} catch(e) {
    // Track matte setup failed for layer_17
    alert('Track matte setup failed for layer_17: ' + e.toString());
}


// Animation creation complete
alert('Animation "Logo 1" created successfully!\n' +
      'Duration: 5.00 seconds\n' +
      'Layers: 17\n' +
      'Assets: 6');

app.endUndoGroup();