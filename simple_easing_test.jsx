// Simple easing test to verify basic functionality
app.beginUndoGroup("Simple Easing Test");

// Create test composition
var comp = app.project.items.addComp('Easing Test', 1920, 1080, 1.0, 3, 30);

// Create test layer
var layer = comp.layers.addSolid([1, 0, 0], 'Test Layer', 100, 100, 1.0);

// Get position property
var positionProp = layer.property('Transform').property('Position');

// Set keyframes
positionProp.setValueAtTime(0, [100, 100]);
positionProp.setValueAtTime(1, [500, 500]);
positionProp.setValueAtTime(2, [900, 100]);

// Apply simple easing to middle keyframe
try {
    var easeIn = new KeyframeEase(25, 50);
    var easeOut = new KeyframeEase(25, 50);
    
    // Apply to keyframe 2 (at time 1 second)
    positionProp.setTemporalEaseAtKey(2, [easeIn, easeIn], [easeOut, easeOut]);
    
    alert('SUCCESS: Basic easing applied to keyframe 2');
    
} catch(e) {
    alert('FAILED: ' + e.toString());
}

app.endUndoGroup();
