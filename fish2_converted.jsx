// 07 - Converted from <PERSON><PERSON> JSON
// Original Bodymovin version: 5.12.1
// Generated by <PERSON><PERSON> to JSX Converter

// Disable undo for performance
app.beginUndoGroup('Create Animation');

// Helper Functions
function setKeyframes(property, keyframes, frameRate) {
    if (!keyframes || !keyframes.length) return;
    
    for (var i = 0; i < keyframes.length; i++) {
        var kf = keyframes[i];
        var time = kf.t / frameRate;
        
        if (i === 0) {
            property.setValueAtTime(time, kf.s);
        } else {
            property.setValueAtTime(time, kf.s);
            
            // Set easing if available
            if (kf.i && kf.o) {
                var keyIndex = property.nearestKeyIndex(time);
                try {
                    property.setTemporalEaseAtKey(keyIndex, kf.i, kf.o);
                } catch(e) {
                    // Ignore easing errors
                }
            }
        }
    }
}

function createSolidLayer(comp, name, color, width, height) {
    return comp.layers.addSolid(color, name, width, height, 1.0);
}

function createShapeLayer(comp, name) {
    var layer = comp.layers.addShape();
    layer.name = name;
    return layer;
}

function createNullLayer(comp, name) {
    var layer = comp.layers.addNull();
    layer.name = name;
    return layer;
}

function createTextLayer(comp, name) {
    var layer = comp.layers.addText(name);
    return layer;
}

function createPrecomp(comp, name, refId) {
    // This would reference a precomp - simplified for now
    return createNullLayer(comp, name + ' (Precomp)');
}

function rgbToAE(r, g, b, a) {
    a = a || 1;
    return [r, g, b, a];
}

function bezierToAE(bezierData) {
    var shape = new Shape();
    if (bezierData.v && bezierData.v.length > 0) {
        shape.vertices = bezierData.v;
        if (bezierData.i) shape.inTangents = bezierData.i;
        if (bezierData.o) shape.outTangents = bezierData.o;
        shape.closed = bezierData.c || false;
    }
    return shape;
}

// === INTELLIGENT ASSET IMPORT WITH CONTEXT-AWARE DIALOGS ===
function importAssetWithDialog(comp, assetId, originalPath, assetType, assetName, dimensions, description) {
    // Try original path first
    if (originalPath && originalPath !== '') {
        var originalFile = new File(originalPath);
        if (originalFile.exists) {
            return importAssetFile(comp, assetId, originalFile, assetName);
        }
    }
    
    // Build hyper-intelligent dialog with detailed context
    var message = '🚨 MISSING ASSET REQUIRED\n';
    message += '════════════════════════════════════════\n\n';
    
    // Asset identification section
    message += '📋 WHAT FILE IS NEEDED:\n';
    message += '• Asset Name: "' + (assetName || assetId) + '"\n';
    message += '• File Type: ' + assetType.toUpperCase() + '\n';
    message += '• Asset ID: ' + assetId + '\n';
    
    if (dimensions && dimensions !== '') {
        message += '• Expected Size: ' + dimensions + ' pixels\n';
    }
    
    message += '• Original Path: ' + (originalPath || 'Not specified') + '\n';
    
    // Context and purpose section
    if (description && description !== '') {
        message += '\n🎯 PURPOSE & CONTEXT:\n';
        message += '• ' + description + '\n';
    }
    
    // Smart guidance based on asset type and name
    message += '\n💡 WHAT TO LOOK FOR:\n';
    if (assetType === 'image') {
        message += '• Look for PNG, JPG, or other image files\n';
        if (assetName && assetName.toLowerCase().indexOf('logo') !== -1) {
            message += '• This should be your company/brand logo\n';
            message += '• Usually a PNG with transparent background\n';
        } else if (assetName && assetName.toLowerCase().indexOf('background') !== -1) {
            message += '• This should be a background image\n';
            message += '• Usually covers the full composition size\n';
        } else if (assetName && assetName.toLowerCase().indexOf('placeholder') !== -1) {
            message += '• This is a placeholder - replace with your image\n';
            message += '• Can be any image that fits your design\n';
        }
    } else if (assetType === 'audio') {
        message += '• Look for MP3, WAV, or other audio files\n';
        if (assetName && assetName.toLowerCase().indexOf('music') !== -1) {
            message += '• This should be background music\n';
            message += '• Usually a longer audio track\n';
        } else {
            message += '• This should be a sound effect or audio clip\n';
            message += '• Usually a shorter audio file\n';
        }
    } else if (assetType === 'video') {
        message += '• Look for MP4, MOV, or other video files\n';
        message += '• Should match the expected dimensions if specified\n';
    }
    
    message += '\n📂 NEXT STEPS:\n';
    message += '• Click OK to open file browser and locate the file\n';
    message += '• Click Cancel to create a placeholder instead\n';
    message += '\n⚠️  If you can\'t find the exact file, choose a similar one or cancel for placeholder.';
    
    var userChoice = confirm(message);
    if (!userChoice) {
        return createPlaceholderLayer(comp, assetId, assetName, assetType, dimensions);
    }
    
    // Show file dialog with smart filters and detailed title
    var fileFilter = getSmartFileFilter(assetType);
    var dialogTitle = 'SELECT ' + assetType.toUpperCase() + ': ' + (assetName || assetId);
    if (description) {
        dialogTitle += ' (' + description + ')';
    }
    
    var selectedFile = File.openDialog(dialogTitle, fileFilter);
    
    if (selectedFile && selectedFile.exists) {
        return importAssetFile(comp, assetId, selectedFile, assetName);
    } else {
        alert('❌ No file selected. Creating placeholder layer instead.');
        return createPlaceholderLayer(comp, assetId, assetName, assetType, dimensions);
    }
}

function importAssetFile(comp, assetId, file, assetName) {
    try {
        var importOptions = new ImportOptions(file);
        var footage = app.project.importFile(importOptions);
        var layer = comp.layers.add(footage);
        layer.name = assetName || assetId;
        return layer;
    } catch (e) {
        alert('Error importing file: ' + file.fsName + '\nError: ' + e.toString() + '\nCreating placeholder layer.');
        return createNullLayer(comp, (assetName || assetId) + ' (Import Error)');
    }
}

function createPlaceholderLayer(comp, assetId, assetName, assetType, dimensions) {
    var placeholderName = (assetName || assetId) + ' [MISSING ' + assetType.toUpperCase() + ']';
    
    // Create different placeholder types based on asset type
    if (assetType === 'image') {
        // Create a colored solid as image placeholder
        var width = 500, height = 500;
        if (dimensions) {
            var parts = dimensions.split('x');
            if (parts.length === 2) {
                width = parseInt(parts[0]) || 500;
                height = parseInt(parts[1]) || 500;
            }
        }
        var placeholder = comp.layers.addSolid([0.8, 0.2, 0.2], placeholderName, width, height, 1.0);
        return placeholder;
    } else if (assetType === 'audio') {
        // Create null layer for audio placeholder
        var placeholder = comp.layers.addNull();
        placeholder.name = placeholderName;
        return placeholder;
    } else if (assetType === 'video') {
        // Create solid for video placeholder
        var placeholder = comp.layers.addSolid([0.2, 0.2, 0.8], placeholderName, 1920, 1080, 1.0);
        return placeholder;
    } else {
        // Generic null layer placeholder
        var placeholder = comp.layers.addNull();
        placeholder.name = placeholderName;
        return placeholder;
    }
}

function getSmartFileFilter(assetType) {
    switch (assetType.toLowerCase()) {
        case 'image':
            return 'Image Files:*.png;*.jpg;*.jpeg;*.gif;*.bmp;*.tiff;*.tga;*.psd;*.ai;*.eps';
        case 'video':
            return 'Video Files:*.mp4;*.mov;*.avi;*.wmv;*.flv;*.webm;*.mkv;*.m4v;*.3gp';
        case 'audio':
            return 'Audio Files:*.mp3;*.wav;*.aac;*.ogg;*.flac;*.m4a;*.wma;*.aiff';
        default:
            return 'All Files:*.*';
    }
}

function getFileTypesForAsset(assetType) {
    switch (assetType.toLowerCase()) {
        case 'image':
            return 'Images:*.png;*.jpg;*.jpeg;*.gif;*.bmp;*.tiff;*.psd';
        case 'video':
            return 'Videos:*.mp4;*.mov;*.avi;*.wmv;*.flv;*.webm;*.mkv';
        case 'audio':
            return 'Audio:*.mp3;*.wav;*.aac;*.ogg;*.flac;*.m4a';
        default:
            return 'All Files:*.*';
    }
}

// Create main composition: 07
var comp = app.project.items.addComp('07', 1080, 1080, 1.0, 6.000, 25);
var frameRate = 25;

// Asset References and Precomps

// Creating 10 layers
// Preserving original JSON layer order for correct visual stacking
// Body (index 1)
// Eyes (index 2)
// Mouth (index 3)
// Body 2 (index 4)
// Fether (index 5)
// Fether (index 6)
// Fether (index 7)
// fether (index 8)
// Fether (index 9)
// Bg (index 10)
// Layer 4: Body 2
var layer_4 = createShapeLayer(comp, 'Body 2');
// Shape Group: Group 1
var layer_4_group_0 = layer_4.property('Contents').addProperty('ADBE Vector Group');
layer_4_group_0.name = 'Group 1';
var layer_4_group_0_path_0 = layer_4_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-99.754, -7.973], [-95.065, 23.889], [21.657, 24.332], [83.508, 28.647], [104.009, 18.973]];
pathShape.inTangents = [[104.418, -62.511], [-28.979, -10.511], [0, 0], [-11.507, -7.192], [16.187, 13.341]];
pathShape.outTangents = [[0, 0], [28.979, 10.512], [0, 0], [11.508, 7.192], [-16.185, -13.341]];
pathShape.closed = true;
layer_4_group_0_path_0.property('Path').setValue(pathShape);
var layer_4_group_0_fill_1 = layer_4_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_4_group_0_fill_1.property('Color').setValue([1.000000, 1.000000, 1.000000]);
layer_4_group_0_fill_1.property('Opacity').setValue(100);
var layer_4_group_0_transform = layer_4_group_0.property('Transform');
layer_4_group_0_transform.property('Position').setValue([197.95, 41.366]);
layer_4_group_0_transform.property('Scale').setValue([100, 100]);
layer_4_group_0_transform.property('Rotation').setValue(0);
layer_4_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 13
var layer_4_group_1 = layer_4.property('Contents').addProperty('ADBE Vector Group');
layer_4_group_1.name = 'Group 13';
var layer_4_group_1_path_0 = layer_4_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[78.57, 26.755], [9.983, -26.611], [-73.257, -4.781], [-55.056, 20.505], [-18.086, 33.689], [60.942, 31.324]];
pathShape.inTangents = [[-5.114, 1.612], [24.266, 65.914], [35.96, -34.522], [-23.514, -17.866], [-14.11, -2.44], [-25.962, 5.785]];
pathShape.outTangents = [[0, 0], [0, 0], [0, 0], [8.817, 6.699], [26.21, 4.53], [6.286, -1.4]];
pathShape.closed = true;
layer_4_group_1_path_0.property('Path').setValue(pathShape);
var layer_4_group_1_fill_1 = layer_4_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_4_group_1_fill_1.property('Color').setValue([1.000000, 1.000000, 1.000000]);
layer_4_group_1_fill_1.property('Opacity').setValue(100);
var layer_4_group_1_transform = layer_4_group_1.property('Transform');
layer_4_group_1_transform.property('Position').setValue([168.441, 190.586]);
layer_4_group_1_transform.property('Scale').setValue([100, 100]);
layer_4_group_1_transform.property('Rotation').setValue(0);
layer_4_group_1_transform.property('Opacity').setValue(100);
// Shape Group: Group 14
var layer_4_group_2 = layer_4.property('Contents').addProperty('ADBE Vector Group');
layer_4_group_2.name = 'Group 14';
var layer_4_group_2_path_0 = layer_4_group_2.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[32.203, 108.585], [-3.671, 3.095], [34.904, -102.889], [38.11, -108.585], [3.686, -102.889], [1.488, -98.784], [-36.612, -0.433], [-6.867, 97.884]];
pathShape.inTangents = [[0, 0], [-0.486, 38.31], [-24.253, 29.66], [0, 0], [0, 0], [0, 0], [1.498, -35.763], [-20.612, -29.265]];
pathShape.outTangents = [[-23.493, -30.266], [0.486, -38.311], [0, 0], [-10.296, 0.59], [0, 0], [-22.987, 27.439], [-1.498, 35.765], [0, 0]];
pathShape.closed = true;
layer_4_group_2_path_0.property('Path').setValue(pathShape);
var layer_4_group_2_fill_1 = layer_4_group_2.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_4_group_2_fill_1.property('Color').setValue([0.980392, 0.643137, 0.411765]);
layer_4_group_2_fill_1.property('Opacity').setValue(100);
var layer_4_group_2_transform = layer_4_group_2.property('Transform');
layer_4_group_2_transform.property('Position').setValue([127.329, 118.056]);
layer_4_group_2_transform.property('Scale').setValue([100, 100]);
layer_4_group_2_transform.property('Rotation').setValue(0);
layer_4_group_2_transform.property('Opacity').setValue(100);
// Shape Group: Group 15
var layer_4_group_3 = layer_4.property('Contents').addProperty('ADBE Vector Group');
layer_4_group_3.name = 'Group 15';
var layer_4_group_3_path_0 = layer_4_group_3.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[19.095, 104.911], [-13.324, -0.323], [33.841, -96.623], [39.454, -100.335], [7.643, -107.788], [4.098, -103.215], [-38.594, 12.392], [-2.481, 107.788]];
pathShape.inTangents = [[0, 0], [-4.505, 36.126], [-25.778, 25.707], [0, 0], [0, 0], [0, 0], [1.027, -42.213], [-25.984, -23.923]];
pathShape.outTangents = [[-18.674, -31.252], [4.506, -36.126], [0, 0], [0, 0], [0, 0], [-31.092, 28.572], [-0.86, 35.309], [0, 0]];
pathShape.closed = false;
layer_4_group_3_path_0.property('Path').setValue(pathShape);
var layer_4_group_3_fill_1 = layer_4_group_3.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_4_group_3_fill_1.property('Color').setValue([0.980392, 0.643137, 0.411765]);
layer_4_group_3_fill_1.property('Opacity').setValue(100);
var layer_4_group_3_transform = layer_4_group_3.property('Transform');
layer_4_group_3_transform.property('Position').setValue([196.916, 119.73]);
layer_4_group_3_transform.property('Scale').setValue([100, 100]);
layer_4_group_3_transform.property('Rotation').setValue(0);
layer_4_group_3_transform.property('Opacity').setValue(100);
// Shape Group: Group 16
var layer_4_group_4 = layer_4.property('Contents').addProperty('ADBE Vector Group');
layer_4_group_4.name = 'Group 16';
var layer_4_group_4_path_0 = layer_4_group_4.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[33.216, -68.829], [20.989, 68.829], [-11.867, 36.4], [-11.867, -23.924]];
pathShape.inTangents = [[0, 0], [-25.891, -83.428], [7.419, 3.531], [-21.349, 33.826]];
pathShape.outTangents = [[0, 0], [0, 0], [0, 0], [0, 0]];
pathShape.closed = true;
layer_4_group_4_path_0.property('Path').setValue(pathShape);
var layer_4_group_4_fill_1 = layer_4_group_4.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_4_group_4_fill_1.property('Color').setValue([1.000000, 1.000000, 1.000000]);
layer_4_group_4_fill_1.property('Opacity').setValue(100);
var layer_4_group_4_transform = layer_4_group_4.property('Transform');
layer_4_group_4_transform.property('Position').setValue([51.9, 106.187]);
layer_4_group_4_transform.property('Scale').setValue([100, 100]);
layer_4_group_4_transform.property('Rotation').setValue(0);
layer_4_group_4_transform.property('Opacity').setValue(100);
// Shape Group: Group 17
var layer_4_group_5 = layer_4.property('Contents').addProperty('ADBE Vector Group');
layer_4_group_5.name = 'Group 17';
var layer_4_group_5_path_0 = layer_4_group_5.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[146.201, -6.873], [143.842, -28.03], [135.942, -51.007], [38.319, -103.246], [-112.713, -55.778], [-145.797, -29.887], [-140.043, 23.333], [-101.206, 57.855], [-0.518, 111.076], [160.611, 54.984], [157.963, 37.848], [157.707, 37.717], [172.161, 23.538], [168.625, 17.425], [159.129, 9.964]];
pathShape.inTangents = [[2.831, 6.606], [-0.551, 14.37], [5.381, 6.407], [55.179, 10.567], [38.837, -40.275], [18.7, -1.438], [-33.083, -30.206], [-20.137, -30.207], [-74.797, -5.754], [-18.736, 18.873], [6.398, 3.318], [0.087, 0.044], [0.964, 9.775], [1.92, 1.508], [0, 0]];
pathShape.outTangents = [[0, 0], [0.32, -8.361], [-13.138, -15.642], [-67.604, -12.945], [0, 0], [0, 0], [0, 0], [0, 0], [66.498, 5.115], [5.078, -5.115], [-0.084, -0.044], [0, 0], [-0.239, -2.43], [0, 0], [-5.652, -4.441]];
pathShape.closed = true;
layer_4_group_5_path_0.property('Path').setValue(pathShape);
var layer_4_group_5_fill_1 = layer_4_group_5.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_4_group_5_fill_1.property('Color').setValue([0.976471, 0.552941, 0.262745]);
layer_4_group_5_fill_1.property('Opacity').setValue(100);
var layer_4_group_5_transform = layer_4_group_5.property('Transform');
layer_4_group_5_transform.property('Position').setValue([173.375, 116.442]);
layer_4_group_5_transform.property('Scale').setValue([100, 100]);
layer_4_group_5_transform.property('Rotation').setValue(0);
layer_4_group_5_transform.property('Opacity').setValue(100);
layer_4.inPoint = 0.000000;
layer_4.outPoint = 6.250000;
layer_4.startTime = 0.000000;
layer_4.property('Transform').property('Opacity').setValue(100);
// Smart detection: 3 keyframes for layer_4.property('Transform').property('Position')
layer_4.property('Transform').property('Position').setValueAtTime(0/frameRate, [319.997, 593.221, 0]);
layer_4.property('Transform').property('Position').setValueAtTime(75/frameRate, [572.482, 573.221, 0]);
layer_4.property('Transform').property('Position').setValueAtTime(149/frameRate, [821.601, 593.221, 0]);
layer_4.property('Transform').property('Scale').setValue([140, 140, 100]);
// Smart detection: 3 keyframes for layer_4.property('Transform').property('Rotation')
layer_4.property('Transform').property('Rotation').setValueAtTime(0/frameRate, 1);
layer_4.property('Transform').property('Rotation').setValueAtTime(75/frameRate, -11);
layer_4.property('Transform').property('Rotation').setValueAtTime(149/frameRate, 1);
layer_4.property('Transform').property('Anchor Point').setValue([173.375, 147.687, 0]);

// Layer 1: Body
var layer_1 = createShapeLayer(comp, 'Body');
// Shape Group: Group 2
var layer_1_group_0 = layer_1.property('Contents').addProperty('ADBE Vector Group');
layer_1_group_0.name = 'Group 2';
var layer_1_group_0_path_0 = layer_1_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-33.921, 15.147], [-8.679, -0.374], [18.128, -11.654], [34.32, -14.458], [34.32, -15.458], [6.052, -8.557], [-20.202, 5.185], [-34.426, 14.285]];
pathShape.inTangents = [[-0.541, 0.354], [-8.675, 4.735], [-9.379, 2.584], [-5.48, 0.373], [0.643, -0.043], [9.022, -3.689], [8.42, -5.177], [4.71, -3.082]];
pathShape.outTangents = [[8.265, -5.408], [8.521, -4.653], [5.293, -1.458], [0.638, -0.043], [-9.746, 0.662], [-9.161, 3.744], [-4.795, 2.948], [-0.536, 0.351]];
pathShape.closed = true;
layer_1_group_0_path_0.property('Path').setValue(pathShape);
var layer_1_group_0_fill_1 = layer_1_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_1_group_0_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_1_group_0_fill_1.property('Opacity').setValue(100);
var layer_1_group_0_transform = layer_1_group_0.property('Transform');
layer_1_group_0_transform.property('Position').setValue([128.525, 163.727]);
layer_1_group_0_transform.property('Scale').setValue([100, 100]);
layer_1_group_0_transform.property('Rotation').setValue(0);
layer_1_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 3
var layer_1_group_1 = layer_1.property('Contents').addProperty('ADBE Vector Group');
layer_1_group_1.name = 'Group 3';
var layer_1_group_1_path_0 = layer_1_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-41.177, 7.117], [-9.763, -2.396], [22.955, -6.037], [41.39, -5.435], [41.39, -6.435], [8.453, -6.173], [-23.909, 0.068], [-41.443, 6.152]];
pathShape.inTangents = [[-0.597, 0.242], [-10.734, 2.203], [-10.987, 0.211], [-6.129, -0.516], [0.638, 0.054], [10.943, -1.096], [10.568, -3.06], [5.735, -2.328]];
pathShape.outTangents = [[10.153, -4.123], [10.766, -2.211], [6.151, -0.117], [0.641, 0.054], [-10.957, -0.924], [-10.946, 1.096], [-5.946, 1.723], [-0.588, 0.239]];
pathShape.closed = true;
layer_1_group_1_path_0.property('Path').setValue(pathShape);
var layer_1_group_1_fill_1 = layer_1_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_1_group_1_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_1_group_1_fill_1.property('Opacity').setValue(100);
var layer_1_group_1_transform = layer_1_group_1.property('Transform');
layer_1_group_1_transform.property('Position').setValue([123.523, 154.908]);
layer_1_group_1_transform.property('Scale').setValue([100, 100]);
layer_1_group_1_transform.property('Rotation').setValue(0);
layer_1_group_1_transform.property('Opacity').setValue(100);
// Shape Group: Group 4
var layer_1_group_2 = layer_1.property('Contents').addProperty('ADBE Vector Group');
layer_1_group_2.name = 'Group 4';
var layer_1_group_2_path_0 = layer_1_group_2.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-40.66, -3.952], [-8.785, -4.108], [23.044, 1.571], [40.671, 5.9], [40.936, 4.935], [9.109, -2.416], [-22.865, -5.974], [-40.926, -4.916]];
pathShape.inTangents = [[-0.63, 0.085], [-10.587, -1.118], [-10.5, -2.437], [-5.867, -1.484], [0.624, 0.158], [10.701, 2.032], [10.742, 0.147], [5.986, -0.808]];
pathShape.outTangents = [[10.566, -1.427], [10.728, 1.132], [5.893, 1.37], [0.624, 0.157], [-10.556, -2.669], [-10.542, -2.002], [-6.038, -0.083], [-0.635, 0.085]];
pathShape.closed = true;
layer_1_group_2_path_0.property('Path').setValue(pathShape);
var layer_1_group_2_fill_1 = layer_1_group_2.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_1_group_2_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_1_group_2_fill_1.property('Opacity').setValue(100);
var layer_1_group_2_transform = layer_1_group_2.property('Transform');
layer_1_group_2_transform.property('Position').setValue([121.927, 144.222]);
layer_1_group_2_transform.property('Scale').setValue([100, 100]);
layer_1_group_2_transform.property('Rotation').setValue(0);
layer_1_group_2_transform.property('Opacity').setValue(100);
// Shape Group: Group 5
var layer_1_group_3 = layer_1.property('Contents').addProperty('ADBE Vector Group');
layer_1_group_3.name = 'Group 5';
var layer_1_group_3_path_0 = layer_1_group_3.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-36.607, -11.058], [-8.099, -4.797], [5.548, 2.411], [19.763, 8.474], [36.6, 13.685], [36.867, 12.72], [6.987, 1.997], [-6.793, -5.235], [-20.723, -11.396], [-36.874, -12.022]];
pathShape.inTangents = [[-0.622, 0.175], [-8.645, -4.568], [-4.634, -2.246], [-4.831, -1.792], [-5.702, -1.429], [0.625, 0.157], [9.571, 4.561], [4.579, 2.445], [4.866, 1.485], [5.338, -1.496]];
pathShape.outTangents = [[9.933, -2.783], [4.55, 2.404], [4.636, 2.247], [5.512, 2.046], [0.625, 0.156], [-10.284, -2.575], [-4.688, -2.236], [-4.476, -2.39], [-5.249, -1.602], [-0.619, 0.173]];
pathShape.closed = true;
layer_1_group_3_path_0.property('Path').setValue(pathShape);
var layer_1_group_3_fill_1 = layer_1_group_3.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_1_group_3_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_1_group_3_fill_1.property('Opacity').setValue(100);
var layer_1_group_3_transform = layer_1_group_3.property('Transform');
layer_1_group_3_transform.property('Position').setValue([124.683, 134.086]);
layer_1_group_3_transform.property('Scale').setValue([100, 100]);
layer_1_group_3_transform.property('Rotation').setValue(0);
layer_1_group_3_transform.property('Opacity').setValue(100);
// Shape Group: Group 6
var layer_1_group_4 = layer_1.property('Contents').addProperty('ADBE Vector Group');
layer_1_group_4.name = 'Group 6';
var layer_1_group_4_path_0 = layer_1_group_4.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[41.715, 20.138], [35.96, -4.314], [27.33, -4.314], [-37.397, -25.89], [-37.397, 30.206], [-15.822, 40.276], [20.07, 23.88]];
pathShape.inTangents = [[-7.429, -0.929], [12.945, 4.315], [0, 0], [8.63, -18.7], [-11.508, -21.576], [-12.945, 4.314], [-10.824, 5.449]];
pathShape.outTangents = [[0, 0], [0, 0], [0, 0], [-8.631, 18.699], [0, 0], [8.348, -2.784], [6.687, -3.366]];
pathShape.closed = true;
layer_1_group_4_path_0.property('Path').setValue(pathShape);
var layer_1_group_4_fill_1 = layer_1_group_4.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_1_group_4_fill_1.property('Color').setValue([0.972549, 0.305882, 0.192157]);
layer_1_group_4_fill_1.property('Opacity').setValue(100);
var layer_1_group_4_transform = layer_1_group_4.property('Transform');
layer_1_group_4_transform.property('Position').setValue([121.075, 141.933]);
layer_1_group_4_transform.property('Scale').setValue([100, 100]);
layer_1_group_4_transform.property('Rotation').setValue(0);
layer_1_group_4_transform.property('Opacity').setValue(100);
layer_1.inPoint = 0.000000;
layer_1.outPoint = 6.250000;
layer_1.startTime = 0.000000;
layer_1.parent = layer_4;
layer_1.property('Transform').property('Opacity').setValue(100);
layer_1.property('Transform').property('Position').setValue([164.74, 147.053, 0]);
layer_1.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 150 keyframes for layer_1.property('Transform').property('Rotation')
layer_1.property('Transform').property('Rotation').setValueAtTime(0/frameRate, 0);
layer_1.property('Transform').property('Rotation').setValueAtTime(1/frameRate, -0.145);
layer_1.property('Transform').property('Rotation').setValueAtTime(2/frameRate, -0.562);
layer_1.property('Transform').property('Rotation').setValueAtTime(3/frameRate, -1.229);
layer_1.property('Transform').property('Rotation').setValueAtTime(4/frameRate, -2.122);
layer_1.property('Transform').property('Rotation').setValueAtTime(5/frameRate, -3.217);
layer_1.property('Transform').property('Rotation').setValueAtTime(6/frameRate, -4.49);
layer_1.property('Transform').property('Rotation').setValueAtTime(7/frameRate, -5.917);
layer_1.property('Transform').property('Rotation').setValueAtTime(8/frameRate, -7.475);
layer_1.property('Transform').property('Rotation').setValueAtTime(9/frameRate, -9.14);
layer_1.property('Transform').property('Rotation').setValueAtTime(10/frameRate, -10.888);
layer_1.property('Transform').property('Rotation').setValueAtTime(11/frameRate, -12.696);
layer_1.property('Transform').property('Rotation').setValueAtTime(12/frameRate, -14.539);
layer_1.property('Transform').property('Rotation').setValueAtTime(13/frameRate, -16.394);
layer_1.property('Transform').property('Rotation').setValueAtTime(14/frameRate, -18.237);
layer_1.property('Transform').property('Rotation').setValueAtTime(15/frameRate, -20.044);
layer_1.property('Transform').property('Rotation').setValueAtTime(16/frameRate, -21.792);
layer_1.property('Transform').property('Rotation').setValueAtTime(17/frameRate, -23.457);
layer_1.property('Transform').property('Rotation').setValueAtTime(18/frameRate, -25.015);
layer_1.property('Transform').property('Rotation').setValueAtTime(19/frameRate, -26.443);
layer_1.property('Transform').property('Rotation').setValueAtTime(20/frameRate, -27.716);
layer_1.property('Transform').property('Rotation').setValueAtTime(21/frameRate, -28.81);
layer_1.property('Transform').property('Rotation').setValueAtTime(22/frameRate, -29.703);
layer_1.property('Transform').property('Rotation').setValueAtTime(23/frameRate, -30.37);
layer_1.property('Transform').property('Rotation').setValueAtTime(24/frameRate, -30.788);
layer_1.property('Transform').property('Rotation').setValueAtTime(25/frameRate, -30.933);
layer_1.property('Transform').property('Rotation').setValueAtTime(26/frameRate, -30.788);
layer_1.property('Transform').property('Rotation').setValueAtTime(27/frameRate, -30.37);
layer_1.property('Transform').property('Rotation').setValueAtTime(28/frameRate, -29.703);
layer_1.property('Transform').property('Rotation').setValueAtTime(29/frameRate, -28.81);
layer_1.property('Transform').property('Rotation').setValueAtTime(30/frameRate, -27.716);
layer_1.property('Transform').property('Rotation').setValueAtTime(31/frameRate, -26.443);
layer_1.property('Transform').property('Rotation').setValueAtTime(32/frameRate, -25.015);
layer_1.property('Transform').property('Rotation').setValueAtTime(33/frameRate, -23.457);
layer_1.property('Transform').property('Rotation').setValueAtTime(34/frameRate, -21.792);
layer_1.property('Transform').property('Rotation').setValueAtTime(35/frameRate, -20.044);
layer_1.property('Transform').property('Rotation').setValueAtTime(36/frameRate, -18.237);
layer_1.property('Transform').property('Rotation').setValueAtTime(37/frameRate, -16.394);
layer_1.property('Transform').property('Rotation').setValueAtTime(38/frameRate, -14.539);
layer_1.property('Transform').property('Rotation').setValueAtTime(39/frameRate, -12.696);
layer_1.property('Transform').property('Rotation').setValueAtTime(40/frameRate, -10.888);
layer_1.property('Transform').property('Rotation').setValueAtTime(41/frameRate, -9.14);
layer_1.property('Transform').property('Rotation').setValueAtTime(42/frameRate, -7.475);
layer_1.property('Transform').property('Rotation').setValueAtTime(43/frameRate, -5.917);
layer_1.property('Transform').property('Rotation').setValueAtTime(44/frameRate, -4.49);
layer_1.property('Transform').property('Rotation').setValueAtTime(45/frameRate, -3.217);
layer_1.property('Transform').property('Rotation').setValueAtTime(46/frameRate, -2.122);
layer_1.property('Transform').property('Rotation').setValueAtTime(47/frameRate, -1.229);
layer_1.property('Transform').property('Rotation').setValueAtTime(48/frameRate, -0.562);
layer_1.property('Transform').property('Rotation').setValueAtTime(49/frameRate, -0.145);
layer_1.property('Transform').property('Rotation').setValueAtTime(50/frameRate, 0);
layer_1.property('Transform').property('Rotation').setValueAtTime(51/frameRate, -0.145);
layer_1.property('Transform').property('Rotation').setValueAtTime(52/frameRate, -0.562);
layer_1.property('Transform').property('Rotation').setValueAtTime(53/frameRate, -1.229);
layer_1.property('Transform').property('Rotation').setValueAtTime(54/frameRate, -2.122);
layer_1.property('Transform').property('Rotation').setValueAtTime(55/frameRate, -3.217);
layer_1.property('Transform').property('Rotation').setValueAtTime(56/frameRate, -4.49);
layer_1.property('Transform').property('Rotation').setValueAtTime(57/frameRate, -5.917);
layer_1.property('Transform').property('Rotation').setValueAtTime(58/frameRate, -7.475);
layer_1.property('Transform').property('Rotation').setValueAtTime(59/frameRate, -9.14);
layer_1.property('Transform').property('Rotation').setValueAtTime(60/frameRate, -10.888);
layer_1.property('Transform').property('Rotation').setValueAtTime(61/frameRate, -12.696);
layer_1.property('Transform').property('Rotation').setValueAtTime(62/frameRate, -14.539);
layer_1.property('Transform').property('Rotation').setValueAtTime(63/frameRate, -16.394);
layer_1.property('Transform').property('Rotation').setValueAtTime(64/frameRate, -18.237);
layer_1.property('Transform').property('Rotation').setValueAtTime(65/frameRate, -20.044);
layer_1.property('Transform').property('Rotation').setValueAtTime(66/frameRate, -21.792);
layer_1.property('Transform').property('Rotation').setValueAtTime(67/frameRate, -23.457);
layer_1.property('Transform').property('Rotation').setValueAtTime(68/frameRate, -25.015);
layer_1.property('Transform').property('Rotation').setValueAtTime(69/frameRate, -26.443);
layer_1.property('Transform').property('Rotation').setValueAtTime(70/frameRate, -27.716);
layer_1.property('Transform').property('Rotation').setValueAtTime(71/frameRate, -28.81);
layer_1.property('Transform').property('Rotation').setValueAtTime(72/frameRate, -29.703);
layer_1.property('Transform').property('Rotation').setValueAtTime(73/frameRate, -30.37);
layer_1.property('Transform').property('Rotation').setValueAtTime(74/frameRate, -30.788);
layer_1.property('Transform').property('Rotation').setValueAtTime(75/frameRate, -30.933);
layer_1.property('Transform').property('Rotation').setValueAtTime(76/frameRate, -30.788);
layer_1.property('Transform').property('Rotation').setValueAtTime(77/frameRate, -30.37);
layer_1.property('Transform').property('Rotation').setValueAtTime(78/frameRate, -29.703);
layer_1.property('Transform').property('Rotation').setValueAtTime(79/frameRate, -28.81);
layer_1.property('Transform').property('Rotation').setValueAtTime(80/frameRate, -27.716);
layer_1.property('Transform').property('Rotation').setValueAtTime(81/frameRate, -26.443);
layer_1.property('Transform').property('Rotation').setValueAtTime(82/frameRate, -25.015);
layer_1.property('Transform').property('Rotation').setValueAtTime(83/frameRate, -23.457);
layer_1.property('Transform').property('Rotation').setValueAtTime(84/frameRate, -21.792);
layer_1.property('Transform').property('Rotation').setValueAtTime(85/frameRate, -20.044);
layer_1.property('Transform').property('Rotation').setValueAtTime(86/frameRate, -18.237);
layer_1.property('Transform').property('Rotation').setValueAtTime(87/frameRate, -16.394);
layer_1.property('Transform').property('Rotation').setValueAtTime(88/frameRate, -14.539);
layer_1.property('Transform').property('Rotation').setValueAtTime(89/frameRate, -12.696);
layer_1.property('Transform').property('Rotation').setValueAtTime(90/frameRate, -10.888);
layer_1.property('Transform').property('Rotation').setValueAtTime(91/frameRate, -9.14);
layer_1.property('Transform').property('Rotation').setValueAtTime(92/frameRate, -7.475);
layer_1.property('Transform').property('Rotation').setValueAtTime(93/frameRate, -5.917);
layer_1.property('Transform').property('Rotation').setValueAtTime(94/frameRate, -4.49);
layer_1.property('Transform').property('Rotation').setValueAtTime(95/frameRate, -3.217);
layer_1.property('Transform').property('Rotation').setValueAtTime(96/frameRate, -2.122);
layer_1.property('Transform').property('Rotation').setValueAtTime(97/frameRate, -1.229);
layer_1.property('Transform').property('Rotation').setValueAtTime(98/frameRate, -0.562);
layer_1.property('Transform').property('Rotation').setValueAtTime(99/frameRate, -0.145);
layer_1.property('Transform').property('Rotation').setValueAtTime(100/frameRate, 0);
layer_1.property('Transform').property('Rotation').setValueAtTime(101/frameRate, -0.145);
layer_1.property('Transform').property('Rotation').setValueAtTime(102/frameRate, -0.562);
layer_1.property('Transform').property('Rotation').setValueAtTime(103/frameRate, -1.229);
layer_1.property('Transform').property('Rotation').setValueAtTime(104/frameRate, -2.122);
layer_1.property('Transform').property('Rotation').setValueAtTime(105/frameRate, -3.217);
layer_1.property('Transform').property('Rotation').setValueAtTime(106/frameRate, -4.49);
layer_1.property('Transform').property('Rotation').setValueAtTime(107/frameRate, -5.917);
layer_1.property('Transform').property('Rotation').setValueAtTime(108/frameRate, -7.475);
layer_1.property('Transform').property('Rotation').setValueAtTime(109/frameRate, -9.14);
layer_1.property('Transform').property('Rotation').setValueAtTime(110/frameRate, -10.888);
layer_1.property('Transform').property('Rotation').setValueAtTime(111/frameRate, -12.696);
layer_1.property('Transform').property('Rotation').setValueAtTime(112/frameRate, -14.539);
layer_1.property('Transform').property('Rotation').setValueAtTime(113/frameRate, -16.394);
layer_1.property('Transform').property('Rotation').setValueAtTime(114/frameRate, -18.237);
layer_1.property('Transform').property('Rotation').setValueAtTime(115/frameRate, -20.044);
layer_1.property('Transform').property('Rotation').setValueAtTime(116/frameRate, -21.792);
layer_1.property('Transform').property('Rotation').setValueAtTime(117/frameRate, -23.457);
layer_1.property('Transform').property('Rotation').setValueAtTime(118/frameRate, -25.015);
layer_1.property('Transform').property('Rotation').setValueAtTime(119/frameRate, -26.443);
layer_1.property('Transform').property('Rotation').setValueAtTime(120/frameRate, -27.716);
layer_1.property('Transform').property('Rotation').setValueAtTime(121/frameRate, -28.81);
layer_1.property('Transform').property('Rotation').setValueAtTime(122/frameRate, -29.703);
layer_1.property('Transform').property('Rotation').setValueAtTime(123/frameRate, -30.37);
layer_1.property('Transform').property('Rotation').setValueAtTime(124/frameRate, -30.788);
layer_1.property('Transform').property('Rotation').setValueAtTime(125/frameRate, -30.933);
layer_1.property('Transform').property('Rotation').setValueAtTime(126/frameRate, -30.788);
layer_1.property('Transform').property('Rotation').setValueAtTime(127/frameRate, -30.37);
layer_1.property('Transform').property('Rotation').setValueAtTime(128/frameRate, -29.703);
layer_1.property('Transform').property('Rotation').setValueAtTime(129/frameRate, -28.81);
layer_1.property('Transform').property('Rotation').setValueAtTime(130/frameRate, -27.716);
layer_1.property('Transform').property('Rotation').setValueAtTime(131/frameRate, -26.443);
layer_1.property('Transform').property('Rotation').setValueAtTime(132/frameRate, -25.015);
layer_1.property('Transform').property('Rotation').setValueAtTime(133/frameRate, -23.457);
layer_1.property('Transform').property('Rotation').setValueAtTime(134/frameRate, -21.792);
layer_1.property('Transform').property('Rotation').setValueAtTime(135/frameRate, -20.044);
layer_1.property('Transform').property('Rotation').setValueAtTime(136/frameRate, -18.237);
layer_1.property('Transform').property('Rotation').setValueAtTime(137/frameRate, -16.394);
layer_1.property('Transform').property('Rotation').setValueAtTime(138/frameRate, -14.539);
layer_1.property('Transform').property('Rotation').setValueAtTime(139/frameRate, -12.696);
layer_1.property('Transform').property('Rotation').setValueAtTime(140/frameRate, -10.888);
layer_1.property('Transform').property('Rotation').setValueAtTime(141/frameRate, -9.14);
layer_1.property('Transform').property('Rotation').setValueAtTime(142/frameRate, -7.475);
layer_1.property('Transform').property('Rotation').setValueAtTime(143/frameRate, -5.917);
layer_1.property('Transform').property('Rotation').setValueAtTime(144/frameRate, -4.49);
layer_1.property('Transform').property('Rotation').setValueAtTime(145/frameRate, -3.217);
layer_1.property('Transform').property('Rotation').setValueAtTime(146/frameRate, -2.122);
layer_1.property('Transform').property('Rotation').setValueAtTime(147/frameRate, -1.229);
layer_1.property('Transform').property('Rotation').setValueAtTime(148/frameRate, -0.562);
layer_1.property('Transform').property('Rotation').setValueAtTime(149/frameRate, -0.145);
layer_1.property('Transform').property('Anchor Point').setValue([164.74, 147.053, 0]);

// Layer 2: Eyes
var layer_2 = createShapeLayer(comp, 'Eyes');
// Shape Group: Group 1
var layer_2_group_0 = layer_2.property('Contents').addProperty('ADBE Vector Group');
layer_2_group_0.name = 'Group 1';
var layer_2_group_0_path_0 = layer_2_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[7.192, 0], [0.001, 7.192], [-7.192, 0], [0.001, -7.192]];
pathShape.inTangents = [[0, -3.972], [3.971, 0], [0, 3.972], [-3.973, 0]];
pathShape.outTangents = [[0, 3.972], [-3.973, 0], [0, -3.972], [3.971, 0]];
pathShape.closed = true;
layer_2_group_0_path_0.property('Path').setValue(pathShape);
var layer_2_group_0_fill_1 = layer_2_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_2_group_0_fill_1.property('Color').setValue([1.000000, 1.000000, 1.000000]);
layer_2_group_0_fill_1.property('Opacity').setValue(100);
var layer_2_group_0_transform = layer_2_group_0.property('Transform');
layer_2_group_0_transform.property('Position').setValue([33.706, 15.978]);
layer_2_group_0_transform.property('Scale').setValue([100, 100]);
layer_2_group_0_transform.property('Rotation').setValue(0);
layer_2_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_2_group_1 = layer_2.property('Contents').addProperty('ADBE Vector Group');
layer_2_group_1.name = 'Group 2';
var layer_2_group_1_path_0 = layer_2_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[20.138, -0.001], [0, 20.137], [-20.138, -0.001], [0, -20.137]];
pathShape.inTangents = [[0, -11.122], [11.121, 0], [0, 11.122], [-11.122, 0]];
pathShape.outTangents = [[0, 11.122], [-11.122, 0], [0, -11.122], [11.121, 0]];
pathShape.closed = true;
layer_2_group_1_path_0.property('Path').setValue(pathShape);
var layer_2_group_1_fill_1 = layer_2_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_2_group_1_fill_1.property('Color').setValue([0.137255, 0.066667, 0.125490]);
layer_2_group_1_fill_1.property('Opacity').setValue(100);
var layer_2_group_1_transform = layer_2_group_1.property('Transform');
layer_2_group_1_transform.property('Position').setValue([27.234, 29.643]);
layer_2_group_1_transform.property('Scale').setValue([100, 100]);
layer_2_group_1_transform.property('Rotation').setValue(0);
layer_2_group_1_transform.property('Opacity').setValue(100);
// Shape Group: Group 3
var layer_2_group_2 = layer_2.property('Contents').addProperty('ADBE Vector Group');
layer_2_group_2.name = 'Group 3';
var layer_2_group_2_path_0 = layer_2_group_2.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[23.258, 4.728], [-5.445, 26.782], [-23.259, -4.728], [5.444, -26.782]];
pathShape.inTangents = [[3.007, -14.792], [12.846, 2.611], [-3.006, 14.791], [-12.845, -2.611]];
pathShape.outTangents = [[-3.007, 14.791], [-12.845, -2.612], [3.007, -14.792], [12.845, 2.611]];
pathShape.closed = true;
layer_2_group_2_path_0.property('Path').setValue(pathShape);
var layer_2_group_2_fill_1 = layer_2_group_2.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_2_group_2_fill_1.property('Color').setValue([1.000000, 1.000000, 1.000000]);
layer_2_group_2_fill_1.property('Opacity').setValue(100);
var layer_2_group_2_transform = layer_2_group_2.property('Transform');
layer_2_group_2_transform.property('Position').setValue([26.515, 29.643]);
layer_2_group_2_transform.property('Scale').setValue([100, 100]);
layer_2_group_2_transform.property('Rotation').setValue(0);
layer_2_group_2_transform.property('Opacity').setValue(100);
layer_2.inPoint = 0.000000;
layer_2.outPoint = 6.250000;
layer_2.startTime = 0.000000;
layer_2.parent = layer_4;
layer_2.property('Transform').property('Opacity').setValue(100);
layer_2.property('Transform').property('Position').setValue([261.321, 99.5, 0]);
// Smart detection: 45 keyframes for layer_2.property('Transform').property('Scale')
layer_2.property('Transform').property('Scale').setValueAtTime(30/frameRate, [100, 100, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(31/frameRate, [100, 94.461, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(32/frameRate, [100, 80.175, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(33/frameRate, [100, 60.641, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(34/frameRate, [100, 39.359, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(35/frameRate, [100, 19.825, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(36/frameRate, [100, 5.539, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(37/frameRate, [100, 0, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(38/frameRate, [100, 4.297, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(39/frameRate, [100, 15.625, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(40/frameRate, [100, 31.641, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(42/frameRate, [100, 68.359, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(43/frameRate, [100, 84.375, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(44/frameRate, [100, 95.703, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(45/frameRate, [100, 100, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(75/frameRate, [100, 100, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(76/frameRate, [100, 94.461, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(77/frameRate, [100, 80.175, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(78/frameRate, [100, 60.641, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(79/frameRate, [100, 39.359, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(80/frameRate, [100, 19.825, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(81/frameRate, [100, 5.539, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(82/frameRate, [100, 0, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(83/frameRate, [100, 4.297, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(84/frameRate, [100, 15.625, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(85/frameRate, [100, 31.641, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(87/frameRate, [100, 68.359, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(88/frameRate, [100, 84.375, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(89/frameRate, [100, 95.703, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(90/frameRate, [100, 100, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(120/frameRate, [100, 100, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(121/frameRate, [100, 94.461, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(122/frameRate, [100, 80.175, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(123/frameRate, [100, 60.641, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(124/frameRate, [100, 39.359, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(125/frameRate, [100, 19.825, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(126/frameRate, [100, 5.539, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(127/frameRate, [100, 0, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(128/frameRate, [100, 4.297, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(129/frameRate, [100, 15.625, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(130/frameRate, [100, 31.641, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(132/frameRate, [100, 68.359, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(133/frameRate, [100, 84.375, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(134/frameRate, [100, 95.703, 100]);
layer_2.property('Transform').property('Scale').setValueAtTime(135/frameRate, [100, 100, 100]);
layer_2.property('Transform').property('Rotation').setValue(0);
layer_2.property('Transform').property('Anchor Point').setValue([26.515, 29.643, 0]);

// Layer 3: Mouth
var layer_3 = createShapeLayer(comp, 'Mouth');
// Shape Group: Group 1
var layer_3_group_0 = layer_3.property('Contents').addProperty('ADBE Vector Group');
layer_3_group_0.name = 'Group 1';
var layer_3_group_0_path_0 = layer_3_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[2.158, -10.069], [2.158, 10.069]];
pathShape.inTangents = [[0, 0], [-4.315, -5.754]];
pathShape.outTangents = [[0, 0], [0, 0]];
pathShape.closed = false;
layer_3_group_0_path_0.property('Path').setValue(pathShape);
var layer_3_group_0_stroke_1 = layer_3_group_0.property('Contents').addProperty('ADBE Vector Graphic - Stroke');
layer_3_group_0_stroke_1.property('Color').setValue([0.937255, 0.435294, 0.094118]);
layer_3_group_0_stroke_1.property('Stroke Width').setValue(2);
layer_3_group_0_stroke_1.property('Opacity').setValue(100);
var layer_3_group_0_transform = layer_3_group_0.property('Transform');
layer_3_group_0_transform.property('Position').setValue([7.157, 15.069]);
layer_3_group_0_transform.property('Scale').setValue([100, 100]);
layer_3_group_0_transform.property('Rotation').setValue(0);
layer_3_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_3_group_1 = layer_3.property('Contents').addProperty('ADBE Vector Group');
layer_3_group_1.name = 'Group 2';
var layer_3_group_1_path_0 = layer_3_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-20.137, -4.316], [20.137, -4.316]];
pathShape.inTangents = [[0, 0], [-20.137, 8.631]];
pathShape.outTangents = [[0, 0], [0, 0]];
pathShape.closed = false;
layer_3_group_1_path_0.property('Path').setValue(pathShape);
var layer_3_group_1_stroke_1 = layer_3_group_1.property('Contents').addProperty('ADBE Vector Graphic - Stroke');
layer_3_group_1_stroke_1.property('Color').setValue([0.937255, 0.435294, 0.094118]);
layer_3_group_1_stroke_1.property('Stroke Width').setValue(2);
layer_3_group_1_stroke_1.property('Opacity').setValue(100);
var layer_3_group_1_transform = layer_3_group_1.property('Transform');
layer_3_group_1_transform.property('Position').setValue([28.014, 20.822]);
layer_3_group_1_transform.property('Scale').setValue([100, 100]);
layer_3_group_1_transform.property('Rotation').setValue(0);
layer_3_group_1_transform.property('Opacity').setValue(100);
layer_3.inPoint = 0.000000;
layer_3.outPoint = 6.250000;
layer_3.startTime = 0.000000;
layer_3.parent = layer_4;
layer_3.property('Transform').property('Opacity').setValue(100);
layer_3.property('Transform').property('Position').setValue([310.226, 152.002, 0]);
layer_3.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_3.property('Transform').property('Rotation').setValue(0);
layer_3.property('Transform').property('Anchor Point').setValue([26.576, 15.069, 0]);

// Layer 5: Fether
var layer_5 = createShapeLayer(comp, 'Fether');
// Shape Group: Group 1
var layer_5_group_0 = layer_5.property('Contents').addProperty('ADBE Vector Group');
layer_5_group_0.name = 'Group 1';
var layer_5_group_0_path_0 = layer_5_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-17.223, -26.01], [-5.245, -4.783], [8.273, 15.636], [16.423, 26.578], [17.131, 25.871], [2.899, 6.1], [-9.847, -14.539], [-16.36, -26.515]];
pathShape.inTangents = [[-0.297, -0.571], [-4.243, -6.932], [-4.747, -6.643], [-2.787, -3.595], [0.389, 0.503], [4.508, 6.756], [4.005, 7.027], [2.093, 4.034]];
pathShape.outTangents = [[3.742, 7.213], [4.261, 6.963], [2.644, 3.701], [0.394, 0.508], [-4.976, -6.419], [-4.489, -6.727], [-2.25, -3.949], [-0.297, -0.571]];
pathShape.closed = true;
layer_5_group_0_path_0.property('Path').setValue(pathShape);
var layer_5_group_0_fill_1 = layer_5_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_5_group_0_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_5_group_0_fill_1.property('Opacity').setValue(100);
var layer_5_group_0_transform = layer_5_group_0.property('Transform');
layer_5_group_0_transform.property('Position').setValue([61.851, 79.723]);
layer_5_group_0_transform.property('Scale').setValue([100, 100]);
layer_5_group_0_transform.property('Rotation').setValue(0);
layer_5_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_5_group_1 = layer_5.property('Contents').addProperty('ADBE Vector Group');
layer_5_group_1.name = 'Group 2';
var layer_5_group_1_path_0 = layer_5_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-18.701, -33.662], [-9.297, -3.933], [5.657, 22.325], [17.85, 34.162], [18.355, 33.299], [-1.107, 10.686], [-13.049, -17.35], [-17.737, -33.929]];
pathShape.inTangents = [[-0.16, -0.625], [-3.92, -9.64], [-6.376, -7.874], [-4.537, -3.429], [0.506, 0.384], [5.029, 8.661], [3.078, 9.689], [1.425, 5.565]];
pathShape.outTangents = [[2.579, 10.07], [3.805, 9.357], [3.579, 4.419], [0.512, 0.389], [-8.019, -6.062], [-5.115, -8.809], [-1.739, -5.474], [-0.16, -0.623]];
pathShape.closed = true;
layer_5_group_1_path_0.property('Path').setValue(pathShape);
var layer_5_group_1_fill_1 = layer_5_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_5_group_1_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_5_group_1_fill_1.property('Opacity').setValue(100);
var layer_5_group_1_transform = layer_5_group_1.property('Transform');
layer_5_group_1_transform.property('Position').setValue([57.911, 84.923]);
layer_5_group_1_transform.property('Scale').setValue([100, 100]);
layer_5_group_1_transform.property('Rotation').setValue(0);
layer_5_group_1_transform.property('Opacity').setValue(100);
// Shape Group: Group 3
var layer_5_group_2 = layer_5.property('Contents').addProperty('ADBE Vector Group');
layer_5_group_2.name = 'Group 3';
var layer_5_group_2_path_0 = layer_5_group_2.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-9.763, -32.072], [-7.564, -5.655], [1.245, 19.347], [8.944, 32.194], [9.807, 31.689], [-2.449, 8.362], [-8.384, -17.313], [-8.763, -32.072]];
pathShape.inTangents = [[0.03, -0.642], [-1.859, -8.665], [-3.981, -7.915], [-2.878, -4.087], [0.372, 0.527], [3.057, 8.263], [0.866, 8.763], [-0.23, 4.922]];
pathShape.outTangents = [[-0.416, 8.854], [1.86, 8.662], [2.246, 4.466], [0.368, 0.521], [-5.073, -7.204], [-3.054, -8.259], [-0.484, -4.904], [0.03, -0.643]];
pathShape.closed = true;
layer_5_group_2_path_0.property('Path').setValue(pathShape);
var layer_5_group_2_fill_1 = layer_5_group_2.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_5_group_2_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_5_group_2_fill_1.property('Opacity').setValue(100);
var layer_5_group_2_transform = layer_5_group_2.property('Transform');
layer_5_group_2_transform.property('Position').setValue([41.05, 81.117]);
layer_5_group_2_transform.property('Scale').setValue([100, 100]);
layer_5_group_2_transform.property('Rotation').setValue(0);
layer_5_group_2_transform.property('Opacity').setValue(100);
// Shape Group: Group 4
var layer_5_group_3 = layer_5.property('Contents').addProperty('ADBE Vector Group');
layer_5_group_3.name = 'Group 4';
var layer_5_group_3_path_0 = layer_5_group_3.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[2.893, -29.583], [-4.695, -8.926], [-1.053, 15.437], [4.077, 29.489], [5.04, 29.223], [-2.77, 4.844], [-2.085, -17.916], [3.6, -28.876]];
pathShape.inTangents = [[0.405, -0.5], [0.521, -7.445], [-2.505, -7.852], [-1.808, -4.647], [0.235, 0.601], [1.601, 8.41], [-2.214, 7.38], [-2.616, 3.225]];
pathShape.outTangents = [[-4.745, 5.85], [-0.579, 8.28], [1.515, 4.752], [0.23, 0.594], [-3.092, -7.952], [-1.431, -7.518], [1.191, -3.97], [0.402, -0.495]];
pathShape.closed = true;
layer_5_group_3_path_0.property('Path').setValue(pathShape);
var layer_5_group_3_fill_1 = layer_5_group_3.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_5_group_3_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_5_group_3_fill_1.property('Opacity').setValue(100);
var layer_5_group_3_transform = layer_5_group_3.property('Transform');
layer_5_group_3_transform.property('Position').setValue([24.669, 72.924]);
layer_5_group_3_transform.property('Scale').setValue([100, 100]);
layer_5_group_3_transform.property('Rotation').setValue(0);
layer_5_group_3_transform.property('Opacity').setValue(100);
// Shape Group: Group 5
var layer_5_group_4 = layer_5.property('Contents').addProperty('ADBE Vector Group');
layer_5_group_4.name = 'Group 5';
var layer_5_group_4_path_0 = layer_5_group_4.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[4.616, -19.644], [-4.147, -5.517], [-3.74, 11.216], [0.755, 19.551], [1.461, 18.844], [-0.49, -11.954], [5.323, -18.937]];
pathShape.inTangents = [[0.492, -0.408], [1.421, -5.454], [-1.746, -5.376], [-2, -2.481], [0.4, 0.496], [-5.328, 9.44], [-2.353, 1.951]];
pathShape.outTangents = [[-4.357, 3.614], [-1.423, 5.459], [0.983, 3.031], [0.404, 0.501], [-6.906, -8.565], [1.502, -2.661], [0.496, -0.411]];
pathShape.closed = true;
layer_5_group_4_path_0.property('Path').setValue(pathShape);
var layer_5_group_4_fill_1 = layer_5_group_4.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_5_group_4_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_5_group_4_fill_1.property('Opacity').setValue(100);
var layer_5_group_4_transform = layer_5_group_4.property('Transform');
layer_5_group_4_transform.property('Position').setValue([15.679, 57.1]);
layer_5_group_4_transform.property('Scale').setValue([100, 100]);
layer_5_group_4_transform.property('Rotation').setValue(0);
layer_5_group_4_transform.property('Opacity').setValue(100);
// Shape Group: Group 6
var layer_5_group_5 = layer_5.property('Contents').addProperty('ADBE Vector Group');
layer_5_group_5.name = 'Group 6';
var layer_5_group_5_path_0 = layer_5_group_5.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-39.556, -2.158], [12.227, 53.941], [36.679, 43.871], [9.715, -7.953]];
pathShape.inTangents = [[-5.753, -60.413], [-33.084, -8.631], [0, 0], [0, 0]];
pathShape.outTangents = [[0, 0], [33.083, 8.63], [0, 0], [0, 0]];
pathShape.closed = true;
layer_5_group_5_path_0.property('Path').setValue(pathShape);
var layer_5_group_5_fill_1 = layer_5_group_5.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_5_group_5_fill_1.property('Color').setValue([0.972549, 0.305882, 0.192157]);
layer_5_group_5_fill_1.property('Opacity').setValue(100);
var layer_5_group_5_transform = layer_5_group_5.property('Transform');
layer_5_group_5_transform.property('Position').setValue([45.559, 62.82]);
layer_5_group_5_transform.property('Scale').setValue([100, 100]);
layer_5_group_5_transform.property('Rotation').setValue(0);
layer_5_group_5_transform.property('Opacity').setValue(100);
layer_5.inPoint = 0.000000;
layer_5.outPoint = 6.250000;
layer_5.startTime = 0.000000;
layer_5.parent = layer_4;
layer_5.property('Transform').property('Opacity').setValue(100);
layer_5.property('Transform').property('Position').setValue([179.277, 206.553, 0]);
layer_5.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 150 keyframes for layer_5.property('Transform').property('Rotation')
layer_5.property('Transform').property('Rotation').setValueAtTime(0/frameRate, 0);
layer_5.property('Transform').property('Rotation').setValueAtTime(1/frameRate, 0.22);
layer_5.property('Transform').property('Rotation').setValueAtTime(2/frameRate, 0.857);
layer_5.property('Transform').property('Rotation').setValueAtTime(3/frameRate, 1.873);
layer_5.property('Transform').property('Rotation').setValueAtTime(4/frameRate, 3.233);
layer_5.property('Transform').property('Rotation').setValueAtTime(5/frameRate, 4.901);
layer_5.property('Transform').property('Rotation').setValueAtTime(6/frameRate, 6.841);
layer_5.property('Transform').property('Rotation').setValueAtTime(7/frameRate, 9.015);
layer_5.property('Transform').property('Rotation').setValueAtTime(8/frameRate, 11.389);
layer_5.property('Transform').property('Rotation').setValueAtTime(9/frameRate, 13.926);
layer_5.property('Transform').property('Rotation').setValueAtTime(10/frameRate, 16.589);
layer_5.property('Transform').property('Rotation').setValueAtTime(11/frameRate, 19.343);
layer_5.property('Transform').property('Rotation').setValueAtTime(12/frameRate, 22.151);
layer_5.property('Transform').property('Rotation').setValueAtTime(13/frameRate, 24.977);
layer_5.property('Transform').property('Rotation').setValueAtTime(14/frameRate, 27.785);
layer_5.property('Transform').property('Rotation').setValueAtTime(15/frameRate, 30.539);
layer_5.property('Transform').property('Rotation').setValueAtTime(16/frameRate, 33.202);
layer_5.property('Transform').property('Rotation').setValueAtTime(17/frameRate, 35.739);
layer_5.property('Transform').property('Rotation').setValueAtTime(18/frameRate, 38.113);
layer_5.property('Transform').property('Rotation').setValueAtTime(19/frameRate, 40.287);
layer_5.property('Transform').property('Rotation').setValueAtTime(20/frameRate, 42.227);
layer_5.property('Transform').property('Rotation').setValueAtTime(21/frameRate, 43.895);
layer_5.property('Transform').property('Rotation').setValueAtTime(22/frameRate, 45.255);
layer_5.property('Transform').property('Rotation').setValueAtTime(23/frameRate, 46.271);
layer_5.property('Transform').property('Rotation').setValueAtTime(24/frameRate, 46.908);
layer_5.property('Transform').property('Rotation').setValueAtTime(25/frameRate, 47.128);
layer_5.property('Transform').property('Rotation').setValueAtTime(26/frameRate, 46.908);
layer_5.property('Transform').property('Rotation').setValueAtTime(27/frameRate, 46.271);
layer_5.property('Transform').property('Rotation').setValueAtTime(28/frameRate, 45.255);
layer_5.property('Transform').property('Rotation').setValueAtTime(29/frameRate, 43.895);
layer_5.property('Transform').property('Rotation').setValueAtTime(30/frameRate, 42.227);
layer_5.property('Transform').property('Rotation').setValueAtTime(31/frameRate, 40.287);
layer_5.property('Transform').property('Rotation').setValueAtTime(32/frameRate, 38.113);
layer_5.property('Transform').property('Rotation').setValueAtTime(33/frameRate, 35.739);
layer_5.property('Transform').property('Rotation').setValueAtTime(34/frameRate, 33.202);
layer_5.property('Transform').property('Rotation').setValueAtTime(35/frameRate, 30.539);
layer_5.property('Transform').property('Rotation').setValueAtTime(36/frameRate, 27.785);
layer_5.property('Transform').property('Rotation').setValueAtTime(37/frameRate, 24.977);
layer_5.property('Transform').property('Rotation').setValueAtTime(38/frameRate, 22.151);
layer_5.property('Transform').property('Rotation').setValueAtTime(39/frameRate, 19.343);
layer_5.property('Transform').property('Rotation').setValueAtTime(40/frameRate, 16.589);
layer_5.property('Transform').property('Rotation').setValueAtTime(41/frameRate, 13.926);
layer_5.property('Transform').property('Rotation').setValueAtTime(42/frameRate, 11.389);
layer_5.property('Transform').property('Rotation').setValueAtTime(43/frameRate, 9.015);
layer_5.property('Transform').property('Rotation').setValueAtTime(44/frameRate, 6.841);
layer_5.property('Transform').property('Rotation').setValueAtTime(45/frameRate, 4.901);
layer_5.property('Transform').property('Rotation').setValueAtTime(46/frameRate, 3.233);
layer_5.property('Transform').property('Rotation').setValueAtTime(47/frameRate, 1.873);
layer_5.property('Transform').property('Rotation').setValueAtTime(48/frameRate, 0.857);
layer_5.property('Transform').property('Rotation').setValueAtTime(49/frameRate, 0.22);
layer_5.property('Transform').property('Rotation').setValueAtTime(50/frameRate, 0);
layer_5.property('Transform').property('Rotation').setValueAtTime(51/frameRate, 0.22);
layer_5.property('Transform').property('Rotation').setValueAtTime(52/frameRate, 0.857);
layer_5.property('Transform').property('Rotation').setValueAtTime(53/frameRate, 1.873);
layer_5.property('Transform').property('Rotation').setValueAtTime(54/frameRate, 3.233);
layer_5.property('Transform').property('Rotation').setValueAtTime(55/frameRate, 4.901);
layer_5.property('Transform').property('Rotation').setValueAtTime(56/frameRate, 6.841);
layer_5.property('Transform').property('Rotation').setValueAtTime(57/frameRate, 9.015);
layer_5.property('Transform').property('Rotation').setValueAtTime(58/frameRate, 11.389);
layer_5.property('Transform').property('Rotation').setValueAtTime(59/frameRate, 13.926);
layer_5.property('Transform').property('Rotation').setValueAtTime(60/frameRate, 16.589);
layer_5.property('Transform').property('Rotation').setValueAtTime(61/frameRate, 19.343);
layer_5.property('Transform').property('Rotation').setValueAtTime(62/frameRate, 22.151);
layer_5.property('Transform').property('Rotation').setValueAtTime(63/frameRate, 24.977);
layer_5.property('Transform').property('Rotation').setValueAtTime(64/frameRate, 27.785);
layer_5.property('Transform').property('Rotation').setValueAtTime(65/frameRate, 30.539);
layer_5.property('Transform').property('Rotation').setValueAtTime(66/frameRate, 33.202);
layer_5.property('Transform').property('Rotation').setValueAtTime(67/frameRate, 35.739);
layer_5.property('Transform').property('Rotation').setValueAtTime(68/frameRate, 38.113);
layer_5.property('Transform').property('Rotation').setValueAtTime(69/frameRate, 40.287);
layer_5.property('Transform').property('Rotation').setValueAtTime(70/frameRate, 42.227);
layer_5.property('Transform').property('Rotation').setValueAtTime(71/frameRate, 43.895);
layer_5.property('Transform').property('Rotation').setValueAtTime(72/frameRate, 45.255);
layer_5.property('Transform').property('Rotation').setValueAtTime(73/frameRate, 46.271);
layer_5.property('Transform').property('Rotation').setValueAtTime(74/frameRate, 46.908);
layer_5.property('Transform').property('Rotation').setValueAtTime(75/frameRate, 47.128);
layer_5.property('Transform').property('Rotation').setValueAtTime(76/frameRate, 46.908);
layer_5.property('Transform').property('Rotation').setValueAtTime(77/frameRate, 46.271);
layer_5.property('Transform').property('Rotation').setValueAtTime(78/frameRate, 45.255);
layer_5.property('Transform').property('Rotation').setValueAtTime(79/frameRate, 43.895);
layer_5.property('Transform').property('Rotation').setValueAtTime(80/frameRate, 42.227);
layer_5.property('Transform').property('Rotation').setValueAtTime(81/frameRate, 40.287);
layer_5.property('Transform').property('Rotation').setValueAtTime(82/frameRate, 38.113);
layer_5.property('Transform').property('Rotation').setValueAtTime(83/frameRate, 35.739);
layer_5.property('Transform').property('Rotation').setValueAtTime(84/frameRate, 33.202);
layer_5.property('Transform').property('Rotation').setValueAtTime(85/frameRate, 30.539);
layer_5.property('Transform').property('Rotation').setValueAtTime(86/frameRate, 27.785);
layer_5.property('Transform').property('Rotation').setValueAtTime(87/frameRate, 24.977);
layer_5.property('Transform').property('Rotation').setValueAtTime(88/frameRate, 22.151);
layer_5.property('Transform').property('Rotation').setValueAtTime(89/frameRate, 19.343);
layer_5.property('Transform').property('Rotation').setValueAtTime(90/frameRate, 16.589);
layer_5.property('Transform').property('Rotation').setValueAtTime(91/frameRate, 13.926);
layer_5.property('Transform').property('Rotation').setValueAtTime(92/frameRate, 11.389);
layer_5.property('Transform').property('Rotation').setValueAtTime(93/frameRate, 9.015);
layer_5.property('Transform').property('Rotation').setValueAtTime(94/frameRate, 6.841);
layer_5.property('Transform').property('Rotation').setValueAtTime(95/frameRate, 4.901);
layer_5.property('Transform').property('Rotation').setValueAtTime(96/frameRate, 3.233);
layer_5.property('Transform').property('Rotation').setValueAtTime(97/frameRate, 1.873);
layer_5.property('Transform').property('Rotation').setValueAtTime(98/frameRate, 0.857);
layer_5.property('Transform').property('Rotation').setValueAtTime(99/frameRate, 0.22);
layer_5.property('Transform').property('Rotation').setValueAtTime(100/frameRate, 0);
layer_5.property('Transform').property('Rotation').setValueAtTime(101/frameRate, 0.22);
layer_5.property('Transform').property('Rotation').setValueAtTime(102/frameRate, 0.857);
layer_5.property('Transform').property('Rotation').setValueAtTime(103/frameRate, 1.873);
layer_5.property('Transform').property('Rotation').setValueAtTime(104/frameRate, 3.233);
layer_5.property('Transform').property('Rotation').setValueAtTime(105/frameRate, 4.901);
layer_5.property('Transform').property('Rotation').setValueAtTime(106/frameRate, 6.841);
layer_5.property('Transform').property('Rotation').setValueAtTime(107/frameRate, 9.015);
layer_5.property('Transform').property('Rotation').setValueAtTime(108/frameRate, 11.389);
layer_5.property('Transform').property('Rotation').setValueAtTime(109/frameRate, 13.926);
layer_5.property('Transform').property('Rotation').setValueAtTime(110/frameRate, 16.589);
layer_5.property('Transform').property('Rotation').setValueAtTime(111/frameRate, 19.343);
layer_5.property('Transform').property('Rotation').setValueAtTime(112/frameRate, 22.151);
layer_5.property('Transform').property('Rotation').setValueAtTime(113/frameRate, 24.977);
layer_5.property('Transform').property('Rotation').setValueAtTime(114/frameRate, 27.785);
layer_5.property('Transform').property('Rotation').setValueAtTime(115/frameRate, 30.539);
layer_5.property('Transform').property('Rotation').setValueAtTime(116/frameRate, 33.202);
layer_5.property('Transform').property('Rotation').setValueAtTime(117/frameRate, 35.739);
layer_5.property('Transform').property('Rotation').setValueAtTime(118/frameRate, 38.113);
layer_5.property('Transform').property('Rotation').setValueAtTime(119/frameRate, 40.287);
layer_5.property('Transform').property('Rotation').setValueAtTime(120/frameRate, 42.227);
layer_5.property('Transform').property('Rotation').setValueAtTime(121/frameRate, 43.895);
layer_5.property('Transform').property('Rotation').setValueAtTime(122/frameRate, 45.255);
layer_5.property('Transform').property('Rotation').setValueAtTime(123/frameRate, 46.271);
layer_5.property('Transform').property('Rotation').setValueAtTime(124/frameRate, 46.908);
layer_5.property('Transform').property('Rotation').setValueAtTime(125/frameRate, 47.128);
layer_5.property('Transform').property('Rotation').setValueAtTime(126/frameRate, 46.908);
layer_5.property('Transform').property('Rotation').setValueAtTime(127/frameRate, 46.271);
layer_5.property('Transform').property('Rotation').setValueAtTime(128/frameRate, 45.255);
layer_5.property('Transform').property('Rotation').setValueAtTime(129/frameRate, 43.895);
layer_5.property('Transform').property('Rotation').setValueAtTime(130/frameRate, 42.227);
layer_5.property('Transform').property('Rotation').setValueAtTime(131/frameRate, 40.287);
layer_5.property('Transform').property('Rotation').setValueAtTime(132/frameRate, 38.113);
layer_5.property('Transform').property('Rotation').setValueAtTime(133/frameRate, 35.739);
layer_5.property('Transform').property('Rotation').setValueAtTime(134/frameRate, 33.202);
layer_5.property('Transform').property('Rotation').setValueAtTime(135/frameRate, 30.539);
layer_5.property('Transform').property('Rotation').setValueAtTime(136/frameRate, 27.785);
layer_5.property('Transform').property('Rotation').setValueAtTime(137/frameRate, 24.977);
layer_5.property('Transform').property('Rotation').setValueAtTime(138/frameRate, 22.151);
layer_5.property('Transform').property('Rotation').setValueAtTime(139/frameRate, 19.343);
layer_5.property('Transform').property('Rotation').setValueAtTime(140/frameRate, 16.589);
layer_5.property('Transform').property('Rotation').setValueAtTime(141/frameRate, 13.926);
layer_5.property('Transform').property('Rotation').setValueAtTime(142/frameRate, 11.389);
layer_5.property('Transform').property('Rotation').setValueAtTime(143/frameRate, 9.015);
layer_5.property('Transform').property('Rotation').setValueAtTime(144/frameRate, 6.841);
layer_5.property('Transform').property('Rotation').setValueAtTime(145/frameRate, 4.901);
layer_5.property('Transform').property('Rotation').setValueAtTime(146/frameRate, 3.233);
layer_5.property('Transform').property('Rotation').setValueAtTime(147/frameRate, 1.873);
layer_5.property('Transform').property('Rotation').setValueAtTime(148/frameRate, 0.857);
layer_5.property('Transform').property('Rotation').setValueAtTime(149/frameRate, 0.22);
layer_5.property('Transform').property('Anchor Point').setValue([32.56, 36.82, 0]);

// Layer 6: Fether
var layer_6 = createShapeLayer(comp, 'Fether');
// Shape Group: Group 1
var layer_6_group_0 = layer_6.property('Contents').addProperty('ADBE Vector Group');
layer_6_group_0.name = 'Group 1';
var layer_6_group_0_path_0 = layer_6_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[11.401, -27.085], [-7.77, 14.578], [-12.349, 26.786], [-11.385, 27.052], [6.443, -15.178], [12.263, -26.58]];
pathShape.inTangents = [[0.302, -0.569], [5.602, -14.233], [1.462, -4.093], [-0.215, 0.602], [-6.734, 13.726], [-1.998, 3.77]];
pathShape.outTangents = [[-7.165, 13.515], [-1.592, 4.044], [-0.216, 0.608], [5.142, -14.399], [1.88, -3.831], [0.302, -0.568]];
pathShape.closed = true;
layer_6_group_0_path_0.property('Path').setValue(pathShape);
var layer_6_group_0_fill_1 = layer_6_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_6_group_0_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_6_group_0_fill_1.property('Opacity').setValue(100);
var layer_6_group_0_transform = layer_6_group_0.property('Transform');
layer_6_group_0_transform.property('Position').setValue([35.789, 36.382]);
layer_6_group_0_transform.property('Scale').setValue([100, 100]);
layer_6_group_0_transform.property('Rotation').setValue(0);
layer_6_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_6_group_1 = layer_6.property('Contents').addProperty('ADBE Vector Group');
layer_6_group_1.name = 'Group 2';
var layer_6_group_1_path_0 = layer_6_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[6.409, -28.304], [-3.308, 16.026], [-7.246, 28.07], [-6.282, 28.336], [5.364, -15.528], [7.373, -28.038]];
pathShape.inTangents = [[0.088, -0.636], [4.391, -14.494], [1.399, -3.985], [-0.212, 0.604], [-2.728, 14.898], [-0.58, 4.183]];
pathShape.outTangents = [[-2.079, 15.001], [-1.224, 4.043], [-0.214, 0.608], [5.016, -14.289], [0.761, -4.155], [0.087, -0.63]];
pathShape.closed = true;
layer_6_group_1_path_0.property('Path').setValue(pathShape);
var layer_6_group_1_fill_1 = layer_6_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_6_group_1_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_6_group_1_fill_1.property('Opacity').setValue(100);
var layer_6_group_1_transform = layer_6_group_1.property('Transform');
layer_6_group_1_transform.property('Position').setValue([45.811, 36.484]);
layer_6_group_1_transform.property('Scale').setValue([100, 100]);
layer_6_group_1_transform.property('Rotation').setValue(0);
layer_6_group_1_transform.property('Opacity').setValue(100);
// Shape Group: Group 3
var layer_6_group_2 = layer_6.property('Contents').addProperty('ADBE Vector Group');
layer_6_group_2.name = 'Group 3';
var layer_6_group_2_path_0 = layer_6_group_2.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[17.119, -30.431], [0.843, -8.398], [-12.227, 15.778], [-18.023, 30.053], [-17.059, 30.319], [-6.027, 5.408], [8.325, -17.646], [17.826, -29.724]];
pathShape.inTangents = [[0.418, -0.49], [4.9, -7.714], [3.781, -8.353], [1.745, -4.832], [-0.217, 0.602], [-4.256, 8.033], [-5.312, 7.339], [-3.325, 3.899]];
pathShape.outTangents = [[-5.93, 6.954], [-4.917, 7.738], [-2.118, 4.681], [-0.219, 0.606], [3.088, -8.55], [4.241, -8.005], [3.003, -4.151], [0.416, -0.486]];
pathShape.closed = true;
layer_6_group_2_path_0.property('Path').setValue(pathShape);
var layer_6_group_2_fill_1 = layer_6_group_2.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_6_group_2_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_6_group_2_fill_1.property('Opacity').setValue(100);
var layer_6_group_2_transform = layer_6_group_2.property('Transform');
layer_6_group_2_transform.property('Position').setValue([31.105, 34.056]);
layer_6_group_2_transform.property('Scale').setValue([100, 100]);
layer_6_group_2_transform.property('Rotation').setValue(0);
layer_6_group_2_transform.property('Opacity').setValue(100);
// Shape Group: Group 4
var layer_6_group_3 = layer_6.property('Contents').addProperty('ADBE Vector Group');
layer_6_group_3.name = 'Group 4';
var layer_6_group_3_path_0 = layer_6_group_3.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[13.009, -19.15], [-8.503, 9.827], [-13.81, 18.577], [-12.947, 19.081], [7.27, -10.643], [13.715, -18.443]];
pathShape.inTangents = [[0.42, -0.488], [6.457, -10.164], [1.71, -2.953], [-0.323, 0.557], [-7.451, 9.398], [-2.201, 2.557]];
pathShape.outTangents = [[-7.854, 9.126], [-1.829, 2.879], [-0.324, 0.557], [6.012, -10.377], [2.095, -2.644], [0.418, -0.485]];
pathShape.closed = true;
layer_6_group_3_path_0.property('Path').setValue(pathShape);
var layer_6_group_3_fill_1 = layer_6_group_3.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_6_group_3_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_6_group_3_fill_1.property('Opacity').setValue(100);
var layer_6_group_3_transform = layer_6_group_3.property('Transform');
layer_6_group_3_transform.property('Position').setValue([22.393, 26.424]);
layer_6_group_3_transform.property('Scale').setValue([100, 100]);
layer_6_group_3_transform.property('Rotation').setValue(0);
layer_6_group_3_transform.property('Opacity').setValue(100);
// Shape Group: Group 5
var layer_6_group_4 = layer_6.property('Contents').addProperty('ADBE Vector Group');
layer_6_group_4.name = 'Group 5';
var layer_6_group_4_path_0 = layer_6_group_4.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[2.877, -36.679], [-14.384, -20.857], [-20.137, -9.35], [-24.453, 22.296], [8.63, 22.296], [27.329, -32.365]];
pathShape.inTangents = [[0, 0], [0, 0], [-1.439, -4.315], [-8.63, -14.385], [0, 0], [5.754, 44.591]];
pathShape.outTangents = [[0, 0], [0, 0], [0, 0], [8.631, 14.383], [0, 0], [0, 0]];
pathShape.closed = true;
layer_6_group_4_path_0.property('Path').setValue(pathShape);
var layer_6_group_4_fill_1 = layer_6_group_4.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_6_group_4_fill_1.property('Color').setValue([0.972549, 0.305882, 0.192157]);
layer_6_group_4_fill_1.property('Opacity').setValue(100);
var layer_6_group_4_transform = layer_6_group_4.property('Transform');
layer_6_group_4_transform.property('Position').setValue([33.333, 36.929]);
layer_6_group_4_transform.property('Scale').setValue([100, 100]);
layer_6_group_4_transform.property('Rotation').setValue(0);
layer_6_group_4_transform.property('Opacity').setValue(100);
layer_6.inPoint = 0.000000;
layer_6.outPoint = 6.250000;
layer_6.startTime = 0.000000;
layer_6.parent = layer_4;
layer_6.property('Transform').property('Opacity').setValue(100);
layer_6.property('Transform').property('Position').setValue([133.514, 217.937, 0]);
layer_6.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 150 keyframes for layer_6.property('Transform').property('Rotation')
layer_6.property('Transform').property('Rotation').setValueAtTime(0/frameRate, 0);
layer_6.property('Transform').property('Rotation').setValueAtTime(1/frameRate, -0.199);
layer_6.property('Transform').property('Rotation').setValueAtTime(2/frameRate, -0.773);
layer_6.property('Transform').property('Rotation').setValueAtTime(3/frameRate, -1.69);
layer_6.property('Transform').property('Rotation').setValueAtTime(4/frameRate, -2.918);
layer_6.property('Transform').property('Rotation').setValueAtTime(5/frameRate, -4.423);
layer_6.property('Transform').property('Rotation').setValueAtTime(6/frameRate, -6.174);
layer_6.property('Transform').property('Rotation').setValueAtTime(7/frameRate, -8.136);
layer_6.property('Transform').property('Rotation').setValueAtTime(8/frameRate, -10.278);
layer_6.property('Transform').property('Rotation').setValueAtTime(9/frameRate, -12.568);
layer_6.property('Transform').property('Rotation').setValueAtTime(10/frameRate, -14.971);
layer_6.property('Transform').property('Rotation').setValueAtTime(11/frameRate, -17.456);
layer_6.property('Transform').property('Rotation').setValueAtTime(12/frameRate, -19.99);
layer_6.property('Transform').property('Rotation').setValueAtTime(13/frameRate, -22.541);
layer_6.property('Transform').property('Rotation').setValueAtTime(14/frameRate, -25.075);
layer_6.property('Transform').property('Rotation').setValueAtTime(15/frameRate, -27.56);
layer_6.property('Transform').property('Rotation').setValueAtTime(16/frameRate, -29.964);
layer_6.property('Transform').property('Rotation').setValueAtTime(17/frameRate, -32.253);
layer_6.property('Transform').property('Rotation').setValueAtTime(18/frameRate, -34.395);
layer_6.property('Transform').property('Rotation').setValueAtTime(19/frameRate, -36.358);
layer_6.property('Transform').property('Rotation').setValueAtTime(20/frameRate, -38.108);
layer_6.property('Transform').property('Rotation').setValueAtTime(21/frameRate, -39.613);
layer_6.property('Transform').property('Rotation').setValueAtTime(22/frameRate, -40.841);
layer_6.property('Transform').property('Rotation').setValueAtTime(23/frameRate, -41.758);
layer_6.property('Transform').property('Rotation').setValueAtTime(24/frameRate, -42.333);
layer_6.property('Transform').property('Rotation').setValueAtTime(25/frameRate, -42.531);
layer_6.property('Transform').property('Rotation').setValueAtTime(26/frameRate, -42.333);
layer_6.property('Transform').property('Rotation').setValueAtTime(27/frameRate, -41.758);
layer_6.property('Transform').property('Rotation').setValueAtTime(28/frameRate, -40.841);
layer_6.property('Transform').property('Rotation').setValueAtTime(29/frameRate, -39.613);
layer_6.property('Transform').property('Rotation').setValueAtTime(30/frameRate, -38.108);
layer_6.property('Transform').property('Rotation').setValueAtTime(31/frameRate, -36.358);
layer_6.property('Transform').property('Rotation').setValueAtTime(32/frameRate, -34.395);
layer_6.property('Transform').property('Rotation').setValueAtTime(33/frameRate, -32.253);
layer_6.property('Transform').property('Rotation').setValueAtTime(34/frameRate, -29.964);
layer_6.property('Transform').property('Rotation').setValueAtTime(35/frameRate, -27.56);
layer_6.property('Transform').property('Rotation').setValueAtTime(36/frameRate, -25.075);
layer_6.property('Transform').property('Rotation').setValueAtTime(37/frameRate, -22.541);
layer_6.property('Transform').property('Rotation').setValueAtTime(38/frameRate, -19.99);
layer_6.property('Transform').property('Rotation').setValueAtTime(39/frameRate, -17.456);
layer_6.property('Transform').property('Rotation').setValueAtTime(40/frameRate, -14.971);
layer_6.property('Transform').property('Rotation').setValueAtTime(41/frameRate, -12.568);
layer_6.property('Transform').property('Rotation').setValueAtTime(42/frameRate, -10.278);
layer_6.property('Transform').property('Rotation').setValueAtTime(43/frameRate, -8.136);
layer_6.property('Transform').property('Rotation').setValueAtTime(44/frameRate, -6.174);
layer_6.property('Transform').property('Rotation').setValueAtTime(45/frameRate, -4.423);
layer_6.property('Transform').property('Rotation').setValueAtTime(46/frameRate, -2.918);
layer_6.property('Transform').property('Rotation').setValueAtTime(47/frameRate, -1.69);
layer_6.property('Transform').property('Rotation').setValueAtTime(48/frameRate, -0.773);
layer_6.property('Transform').property('Rotation').setValueAtTime(49/frameRate, -0.199);
layer_6.property('Transform').property('Rotation').setValueAtTime(50/frameRate, 0);
layer_6.property('Transform').property('Rotation').setValueAtTime(51/frameRate, -0.199);
layer_6.property('Transform').property('Rotation').setValueAtTime(52/frameRate, -0.773);
layer_6.property('Transform').property('Rotation').setValueAtTime(53/frameRate, -1.69);
layer_6.property('Transform').property('Rotation').setValueAtTime(54/frameRate, -2.918);
layer_6.property('Transform').property('Rotation').setValueAtTime(55/frameRate, -4.423);
layer_6.property('Transform').property('Rotation').setValueAtTime(56/frameRate, -6.174);
layer_6.property('Transform').property('Rotation').setValueAtTime(57/frameRate, -8.136);
layer_6.property('Transform').property('Rotation').setValueAtTime(58/frameRate, -10.278);
layer_6.property('Transform').property('Rotation').setValueAtTime(59/frameRate, -12.568);
layer_6.property('Transform').property('Rotation').setValueAtTime(60/frameRate, -14.971);
layer_6.property('Transform').property('Rotation').setValueAtTime(61/frameRate, -17.456);
layer_6.property('Transform').property('Rotation').setValueAtTime(62/frameRate, -19.99);
layer_6.property('Transform').property('Rotation').setValueAtTime(63/frameRate, -22.541);
layer_6.property('Transform').property('Rotation').setValueAtTime(64/frameRate, -25.075);
layer_6.property('Transform').property('Rotation').setValueAtTime(65/frameRate, -27.56);
layer_6.property('Transform').property('Rotation').setValueAtTime(66/frameRate, -29.964);
layer_6.property('Transform').property('Rotation').setValueAtTime(67/frameRate, -32.253);
layer_6.property('Transform').property('Rotation').setValueAtTime(68/frameRate, -34.395);
layer_6.property('Transform').property('Rotation').setValueAtTime(69/frameRate, -36.358);
layer_6.property('Transform').property('Rotation').setValueAtTime(70/frameRate, -38.108);
layer_6.property('Transform').property('Rotation').setValueAtTime(71/frameRate, -39.613);
layer_6.property('Transform').property('Rotation').setValueAtTime(72/frameRate, -40.841);
layer_6.property('Transform').property('Rotation').setValueAtTime(73/frameRate, -41.758);
layer_6.property('Transform').property('Rotation').setValueAtTime(74/frameRate, -42.333);
layer_6.property('Transform').property('Rotation').setValueAtTime(75/frameRate, -42.531);
layer_6.property('Transform').property('Rotation').setValueAtTime(76/frameRate, -42.333);
layer_6.property('Transform').property('Rotation').setValueAtTime(77/frameRate, -41.758);
layer_6.property('Transform').property('Rotation').setValueAtTime(78/frameRate, -40.841);
layer_6.property('Transform').property('Rotation').setValueAtTime(79/frameRate, -39.613);
layer_6.property('Transform').property('Rotation').setValueAtTime(80/frameRate, -38.108);
layer_6.property('Transform').property('Rotation').setValueAtTime(81/frameRate, -36.358);
layer_6.property('Transform').property('Rotation').setValueAtTime(82/frameRate, -34.395);
layer_6.property('Transform').property('Rotation').setValueAtTime(83/frameRate, -32.253);
layer_6.property('Transform').property('Rotation').setValueAtTime(84/frameRate, -29.964);
layer_6.property('Transform').property('Rotation').setValueAtTime(85/frameRate, -27.56);
layer_6.property('Transform').property('Rotation').setValueAtTime(86/frameRate, -25.075);
layer_6.property('Transform').property('Rotation').setValueAtTime(87/frameRate, -22.541);
layer_6.property('Transform').property('Rotation').setValueAtTime(88/frameRate, -19.99);
layer_6.property('Transform').property('Rotation').setValueAtTime(89/frameRate, -17.456);
layer_6.property('Transform').property('Rotation').setValueAtTime(90/frameRate, -14.971);
layer_6.property('Transform').property('Rotation').setValueAtTime(91/frameRate, -12.568);
layer_6.property('Transform').property('Rotation').setValueAtTime(92/frameRate, -10.278);
layer_6.property('Transform').property('Rotation').setValueAtTime(93/frameRate, -8.136);
layer_6.property('Transform').property('Rotation').setValueAtTime(94/frameRate, -6.174);
layer_6.property('Transform').property('Rotation').setValueAtTime(95/frameRate, -4.423);
layer_6.property('Transform').property('Rotation').setValueAtTime(96/frameRate, -2.918);
layer_6.property('Transform').property('Rotation').setValueAtTime(97/frameRate, -1.69);
layer_6.property('Transform').property('Rotation').setValueAtTime(98/frameRate, -0.773);
layer_6.property('Transform').property('Rotation').setValueAtTime(99/frameRate, -0.199);
layer_6.property('Transform').property('Rotation').setValueAtTime(100/frameRate, 0);
layer_6.property('Transform').property('Rotation').setValueAtTime(101/frameRate, -0.199);
layer_6.property('Transform').property('Rotation').setValueAtTime(102/frameRate, -0.773);
layer_6.property('Transform').property('Rotation').setValueAtTime(103/frameRate, -1.69);
layer_6.property('Transform').property('Rotation').setValueAtTime(104/frameRate, -2.918);
layer_6.property('Transform').property('Rotation').setValueAtTime(105/frameRate, -4.423);
layer_6.property('Transform').property('Rotation').setValueAtTime(106/frameRate, -6.174);
layer_6.property('Transform').property('Rotation').setValueAtTime(107/frameRate, -8.136);
layer_6.property('Transform').property('Rotation').setValueAtTime(108/frameRate, -10.278);
layer_6.property('Transform').property('Rotation').setValueAtTime(109/frameRate, -12.568);
layer_6.property('Transform').property('Rotation').setValueAtTime(110/frameRate, -14.971);
layer_6.property('Transform').property('Rotation').setValueAtTime(111/frameRate, -17.456);
layer_6.property('Transform').property('Rotation').setValueAtTime(112/frameRate, -19.99);
layer_6.property('Transform').property('Rotation').setValueAtTime(113/frameRate, -22.541);
layer_6.property('Transform').property('Rotation').setValueAtTime(114/frameRate, -25.075);
layer_6.property('Transform').property('Rotation').setValueAtTime(115/frameRate, -27.56);
layer_6.property('Transform').property('Rotation').setValueAtTime(116/frameRate, -29.964);
layer_6.property('Transform').property('Rotation').setValueAtTime(117/frameRate, -32.253);
layer_6.property('Transform').property('Rotation').setValueAtTime(118/frameRate, -34.395);
layer_6.property('Transform').property('Rotation').setValueAtTime(119/frameRate, -36.358);
layer_6.property('Transform').property('Rotation').setValueAtTime(120/frameRate, -38.108);
layer_6.property('Transform').property('Rotation').setValueAtTime(121/frameRate, -39.613);
layer_6.property('Transform').property('Rotation').setValueAtTime(122/frameRate, -40.841);
layer_6.property('Transform').property('Rotation').setValueAtTime(123/frameRate, -41.758);
layer_6.property('Transform').property('Rotation').setValueAtTime(124/frameRate, -42.333);
layer_6.property('Transform').property('Rotation').setValueAtTime(125/frameRate, -42.531);
layer_6.property('Transform').property('Rotation').setValueAtTime(126/frameRate, -42.333);
layer_6.property('Transform').property('Rotation').setValueAtTime(127/frameRate, -41.758);
layer_6.property('Transform').property('Rotation').setValueAtTime(128/frameRate, -40.841);
layer_6.property('Transform').property('Rotation').setValueAtTime(129/frameRate, -39.613);
layer_6.property('Transform').property('Rotation').setValueAtTime(130/frameRate, -38.108);
layer_6.property('Transform').property('Rotation').setValueAtTime(131/frameRate, -36.358);
layer_6.property('Transform').property('Rotation').setValueAtTime(132/frameRate, -34.395);
layer_6.property('Transform').property('Rotation').setValueAtTime(133/frameRate, -32.253);
layer_6.property('Transform').property('Rotation').setValueAtTime(134/frameRate, -29.964);
layer_6.property('Transform').property('Rotation').setValueAtTime(135/frameRate, -27.56);
layer_6.property('Transform').property('Rotation').setValueAtTime(136/frameRate, -25.075);
layer_6.property('Transform').property('Rotation').setValueAtTime(137/frameRate, -22.541);
layer_6.property('Transform').property('Rotation').setValueAtTime(138/frameRate, -19.99);
layer_6.property('Transform').property('Rotation').setValueAtTime(139/frameRate, -17.456);
layer_6.property('Transform').property('Rotation').setValueAtTime(140/frameRate, -14.971);
layer_6.property('Transform').property('Rotation').setValueAtTime(141/frameRate, -12.568);
layer_6.property('Transform').property('Rotation').setValueAtTime(142/frameRate, -10.278);
layer_6.property('Transform').property('Rotation').setValueAtTime(143/frameRate, -8.136);
layer_6.property('Transform').property('Rotation').setValueAtTime(144/frameRate, -6.174);
layer_6.property('Transform').property('Rotation').setValueAtTime(145/frameRate, -4.423);
layer_6.property('Transform').property('Rotation').setValueAtTime(146/frameRate, -2.918);
layer_6.property('Transform').property('Rotation').setValueAtTime(147/frameRate, -1.69);
layer_6.property('Transform').property('Rotation').setValueAtTime(148/frameRate, -0.773);
layer_6.property('Transform').property('Rotation').setValueAtTime(149/frameRate, -0.199);
layer_6.property('Transform').property('Anchor Point').setValue([44.333, 7.929, 0]);

// Layer 7: Fether
var layer_7 = createShapeLayer(comp, 'Fether');
// Shape Group: Group 1
var layer_7_group_0 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_0.name = 'Group 1';
var layer_7_group_0_path_0 = layer_7_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-35.165, 6.531], [-7.81, 0.898], [19.757, -3.581], [35.419, -5.6], [35.419, -6.6], [7.74, -2.781], [-19.983, 2.24], [-35.432, 5.566]];
pathShape.inTangents = [[-0.627, 0.143], [-9.157, 1.687], [-9.219, 1.3], [-5.228, 0.612], [0.639, -0.074], [9.199, -1.465], [9.204, -1.869], [5.137, -1.169]];
pathShape.outTangents = [[9.078, -2.067], [9.156, -1.685], [5.213, -0.736], [0.632, -0.073], [-9.251, 1.082], [-9.275, 1.478], [-5.162, 1.048], [-0.627, 0.143]];
pathShape.closed = true;
layer_7_group_0_path_0.property('Path').setValue(pathShape);
var layer_7_group_0_fill_1 = layer_7_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_0_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_7_group_0_fill_1.property('Opacity').setValue(100);
var layer_7_group_0_transform = layer_7_group_0.property('Transform');
layer_7_group_0_transform.property('Position').setValue([46.217, 11.976]);
layer_7_group_0_transform.property('Scale').setValue([100, 100]);
layer_7_group_0_transform.property('Rotation').setValue(0);
layer_7_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_7_group_1 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_1.name = 'Group 2';
var layer_7_group_1_path_0 = layer_7_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-38.909, 11.67], [-8.96, 1.417], [21.692, -6.956], [39.14, -10.808], [38.874, -11.771], [8.06, -4.524], [-22.351, 4.688], [-39.175, 10.705]];
pathShape.inTangents = [[-0.604, 0.228], [-10.088, 3.102], [-10.302, 2.467], [-5.838, 1.181], [0.63, -0.127], [10.192, -2.74], [10.036, -3.391], [5.572, -2.104]];
pathShape.outTangents = [[9.873, -3.729], [10.126, -3.113], [5.793, -1.387], [0.631, -0.127], [-10.344, 2.093], [-10.231, 2.749], [-5.643, 1.906], [-0.596, 0.226]];
pathShape.closed = true;
layer_7_group_1_path_0.property('Path').setValue(pathShape);
var layer_7_group_1_fill_1 = layer_7_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_1_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_7_group_1_fill_1.property('Opacity').setValue(100);
var layer_7_group_1_transform = layer_7_group_1.property('Transform');
layer_7_group_1_transform.property('Position').setValue([44.802, 20.17]);
layer_7_group_1_transform.property('Scale').setValue([100, 100]);
layer_7_group_1_transform.property('Rotation').setValue(0);
layer_7_group_1_transform.property('Opacity').setValue(100);
// Shape Group: Group 3
var layer_7_group_2 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_2.name = 'Group 3';
var layer_7_group_2_path_0 = layer_7_group_2.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-33.547, 21.877], [-6.215, 6.5], [19.849, -10.519], [34.117, -20.921], [33.613, -21.784], [8.007, -3.678], [-18.539, 12.58], [-34.052, 21.014]];
pathShape.inTangents = [[-0.571, 0.297], [-8.938, 5.426], [-8.499, 5.958], [-4.692, 3.555], [0.513, -0.39], [8.733, -5.751], [9.024, -5.126], [5.223, -2.716]];
pathShape.outTangents = [[9.276, -4.823], [8.871, -5.385], [4.82, -3.377], [0.506, -0.384], [-8.334, 6.314], [-8.667, 5.708], [-5.118, 2.907], [-0.571, 0.297]];
pathShape.closed = true;
layer_7_group_2_path_0.property('Path').setValue(pathShape);
var layer_7_group_2_fill_1 = layer_7_group_2.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_2_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_7_group_2_fill_1.property('Opacity').setValue(100);
var layer_7_group_2_transform = layer_7_group_2.property('Transform');
layer_7_group_2_transform.property('Position').setValue([53.833, 27.311]);
layer_7_group_2_transform.property('Scale').setValue([100, 100]);
layer_7_group_2_transform.property('Rotation').setValue(0);
layer_7_group_2_transform.property('Opacity').setValue(100);
// Shape Group: Group 4
var layer_7_group_3 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_3.name = 'Group 4';
var layer_7_group_3_path_0 = layer_7_group_3.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-29.706, 27.063], [-6.234, 6.263], [17.204, -14.596], [30.415, -26.354], [29.708, -27.061], [6.264, -6.196], [-17.176, 14.648], [-30.414, 26.356]];
pathShape.inTangents = [[-0.481, 0.425], [-7.812, 6.947], [-7.813, 6.953], [-4.404, 3.92], [0.48, -0.427], [7.815, -6.955], [7.822, -6.939], [4.42, -3.895]];
pathShape.outTangents = [[7.842, -6.912], [7.815, -6.951], [4.403, -3.919], [0.482, -0.429], [-7.815, 6.955], [-7.81, 6.951], [-4.406, 3.909], [-0.483, 0.427]];
pathShape.closed = true;
layer_7_group_3_path_0.property('Path').setValue(pathShape);
var layer_7_group_3_fill_1 = layer_7_group_3.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_3_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_7_group_3_fill_1.property('Opacity').setValue(100);
var layer_7_group_3_transform = layer_7_group_3.property('Transform');
layer_7_group_3_transform.property('Position').setValue([58.593, 40.315]);
layer_7_group_3_transform.property('Scale').setValue([100, 100]);
layer_7_group_3_transform.property('Rotation').setValue(0);
layer_7_group_3_transform.property('Opacity').setValue(100);
// Shape Group: Group 5
var layer_7_group_4 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_4.name = 'Group 5';
var layer_7_group_4_path_0 = layer_7_group_4.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[22.64, 2.018], [-0.266, 27.335], [-9.365, 32.405], [-13.779, 34.516], [-24.081, 37.266], [-32.02, 27.91], [-37.806, 16.624], [-46.586, 6.404], [-46.934, -15.655], [-31.243, -27.691], [-26.266, -28.188], [31.27, -35.38], [41.339, -13.804]];
pathShape.inTangents = [[0, 0], [0, 0], [3.487, -0.747], [1.356, -1.177], [3.654, 0.991], [0, 5.766], [2.741, 1.673], [1.589, 4.348], [-3.035, 8.665], [-7.09, 0.709], [0, 0], [0, 0], [8.631, -24.453]];
pathShape.outTangents = [[0, 0], [-2.392, 2.645], [-1.469, 0.314], [-2.861, 2.481], [-3.965, -1.074], [0, -6.33], [-3.952, -2.412], [-2.036, -5.57], [2.355, -6.725], [0, 0], [0, 0], [0, 0], [0, 0]];
pathShape.closed = true;
layer_7_group_4_path_0.property('Path').setValue(pathShape);
var layer_7_group_4_fill_1 = layer_7_group_4.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_4_fill_1.property('Color').setValue([0.972549, 0.305882, 0.192157]);
layer_7_group_4_fill_1.property('Opacity').setValue(100);
var layer_7_group_4_transform = layer_7_group_4.property('Transform');
layer_7_group_4_transform.property('Position').setValue([50.219, 38.507]);
layer_7_group_4_transform.property('Scale').setValue([100, 100]);
layer_7_group_4_transform.property('Rotation').setValue(0);
layer_7_group_4_transform.property('Opacity').setValue(100);
layer_7.inPoint = 0.000000;
layer_7.outPoint = 6.250000;
layer_7.startTime = 0.000000;
layer_7.parent = layer_4;
layer_7.property('Transform').property('Opacity').setValue(100);
layer_7.property('Transform').property('Position').setValue([100.353, 191.869, 0]);
layer_7.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 150 keyframes for layer_7.property('Transform').property('Rotation')
layer_7.property('Transform').property('Rotation').setValueAtTime(0/frameRate, 0);
layer_7.property('Transform').property('Rotation').setValueAtTime(1/frameRate, -0.143);
layer_7.property('Transform').property('Rotation').setValueAtTime(2/frameRate, -0.557);
layer_7.property('Transform').property('Rotation').setValueAtTime(3/frameRate, -1.218);
layer_7.property('Transform').property('Rotation').setValueAtTime(4/frameRate, -2.103);
layer_7.property('Transform').property('Rotation').setValueAtTime(5/frameRate, -3.187);
layer_7.property('Transform').property('Rotation').setValueAtTime(6/frameRate, -4.448);
layer_7.property('Transform').property('Rotation').setValueAtTime(7/frameRate, -5.863);
layer_7.property('Transform').property('Rotation').setValueAtTime(8/frameRate, -7.406);
layer_7.property('Transform').property('Rotation').setValueAtTime(9/frameRate, -9.056);
layer_7.property('Transform').property('Rotation').setValueAtTime(10/frameRate, -10.788);
layer_7.property('Transform').property('Rotation').setValueAtTime(11/frameRate, -12.578);
layer_7.property('Transform').property('Rotation').setValueAtTime(12/frameRate, -14.404);
layer_7.property('Transform').property('Rotation').setValueAtTime(13/frameRate, -16.242);
layer_7.property('Transform').property('Rotation').setValueAtTime(14/frameRate, -18.068);
layer_7.property('Transform').property('Rotation').setValueAtTime(15/frameRate, -19.859);
layer_7.property('Transform').property('Rotation').setValueAtTime(16/frameRate, -21.591);
layer_7.property('Transform').property('Rotation').setValueAtTime(17/frameRate, -23.24);
layer_7.property('Transform').property('Rotation').setValueAtTime(18/frameRate, -24.784);
layer_7.property('Transform').property('Rotation').setValueAtTime(19/frameRate, -26.198);
layer_7.property('Transform').property('Rotation').setValueAtTime(20/frameRate, -27.459);
layer_7.property('Transform').property('Rotation').setValueAtTime(21/frameRate, -28.544);
layer_7.property('Transform').property('Rotation').setValueAtTime(22/frameRate, -29.429);
layer_7.property('Transform').property('Rotation').setValueAtTime(23/frameRate, -30.09);
layer_7.property('Transform').property('Rotation').setValueAtTime(24/frameRate, -30.503);
layer_7.property('Transform').property('Rotation').setValueAtTime(25/frameRate, -30.647);
layer_7.property('Transform').property('Rotation').setValueAtTime(26/frameRate, -30.503);
layer_7.property('Transform').property('Rotation').setValueAtTime(27/frameRate, -30.09);
layer_7.property('Transform').property('Rotation').setValueAtTime(28/frameRate, -29.429);
layer_7.property('Transform').property('Rotation').setValueAtTime(29/frameRate, -28.544);
layer_7.property('Transform').property('Rotation').setValueAtTime(30/frameRate, -27.459);
layer_7.property('Transform').property('Rotation').setValueAtTime(31/frameRate, -26.198);
layer_7.property('Transform').property('Rotation').setValueAtTime(32/frameRate, -24.784);
layer_7.property('Transform').property('Rotation').setValueAtTime(33/frameRate, -23.24);
layer_7.property('Transform').property('Rotation').setValueAtTime(34/frameRate, -21.591);
layer_7.property('Transform').property('Rotation').setValueAtTime(35/frameRate, -19.859);
layer_7.property('Transform').property('Rotation').setValueAtTime(36/frameRate, -18.068);
layer_7.property('Transform').property('Rotation').setValueAtTime(37/frameRate, -16.242);
layer_7.property('Transform').property('Rotation').setValueAtTime(38/frameRate, -14.404);
layer_7.property('Transform').property('Rotation').setValueAtTime(39/frameRate, -12.578);
layer_7.property('Transform').property('Rotation').setValueAtTime(40/frameRate, -10.788);
layer_7.property('Transform').property('Rotation').setValueAtTime(41/frameRate, -9.056);
layer_7.property('Transform').property('Rotation').setValueAtTime(42/frameRate, -7.406);
layer_7.property('Transform').property('Rotation').setValueAtTime(43/frameRate, -5.863);
layer_7.property('Transform').property('Rotation').setValueAtTime(44/frameRate, -4.448);
layer_7.property('Transform').property('Rotation').setValueAtTime(45/frameRate, -3.187);
layer_7.property('Transform').property('Rotation').setValueAtTime(46/frameRate, -2.103);
layer_7.property('Transform').property('Rotation').setValueAtTime(47/frameRate, -1.218);
layer_7.property('Transform').property('Rotation').setValueAtTime(48/frameRate, -0.557);
layer_7.property('Transform').property('Rotation').setValueAtTime(49/frameRate, -0.143);
layer_7.property('Transform').property('Rotation').setValueAtTime(50/frameRate, 0);
layer_7.property('Transform').property('Rotation').setValueAtTime(51/frameRate, -0.143);
layer_7.property('Transform').property('Rotation').setValueAtTime(52/frameRate, -0.557);
layer_7.property('Transform').property('Rotation').setValueAtTime(53/frameRate, -1.218);
layer_7.property('Transform').property('Rotation').setValueAtTime(54/frameRate, -2.103);
layer_7.property('Transform').property('Rotation').setValueAtTime(55/frameRate, -3.187);
layer_7.property('Transform').property('Rotation').setValueAtTime(56/frameRate, -4.448);
layer_7.property('Transform').property('Rotation').setValueAtTime(57/frameRate, -5.863);
layer_7.property('Transform').property('Rotation').setValueAtTime(58/frameRate, -7.406);
layer_7.property('Transform').property('Rotation').setValueAtTime(59/frameRate, -9.056);
layer_7.property('Transform').property('Rotation').setValueAtTime(60/frameRate, -10.788);
layer_7.property('Transform').property('Rotation').setValueAtTime(61/frameRate, -12.578);
layer_7.property('Transform').property('Rotation').setValueAtTime(62/frameRate, -14.404);
layer_7.property('Transform').property('Rotation').setValueAtTime(63/frameRate, -16.242);
layer_7.property('Transform').property('Rotation').setValueAtTime(64/frameRate, -18.068);
layer_7.property('Transform').property('Rotation').setValueAtTime(65/frameRate, -19.859);
layer_7.property('Transform').property('Rotation').setValueAtTime(66/frameRate, -21.591);
layer_7.property('Transform').property('Rotation').setValueAtTime(67/frameRate, -23.24);
layer_7.property('Transform').property('Rotation').setValueAtTime(68/frameRate, -24.784);
layer_7.property('Transform').property('Rotation').setValueAtTime(69/frameRate, -26.198);
layer_7.property('Transform').property('Rotation').setValueAtTime(70/frameRate, -27.459);
layer_7.property('Transform').property('Rotation').setValueAtTime(71/frameRate, -28.544);
layer_7.property('Transform').property('Rotation').setValueAtTime(72/frameRate, -29.429);
layer_7.property('Transform').property('Rotation').setValueAtTime(73/frameRate, -30.09);
layer_7.property('Transform').property('Rotation').setValueAtTime(74/frameRate, -30.503);
layer_7.property('Transform').property('Rotation').setValueAtTime(75/frameRate, -30.647);
layer_7.property('Transform').property('Rotation').setValueAtTime(76/frameRate, -30.503);
layer_7.property('Transform').property('Rotation').setValueAtTime(77/frameRate, -30.09);
layer_7.property('Transform').property('Rotation').setValueAtTime(78/frameRate, -29.429);
layer_7.property('Transform').property('Rotation').setValueAtTime(79/frameRate, -28.544);
layer_7.property('Transform').property('Rotation').setValueAtTime(80/frameRate, -27.459);
layer_7.property('Transform').property('Rotation').setValueAtTime(81/frameRate, -26.198);
layer_7.property('Transform').property('Rotation').setValueAtTime(82/frameRate, -24.784);
layer_7.property('Transform').property('Rotation').setValueAtTime(83/frameRate, -23.24);
layer_7.property('Transform').property('Rotation').setValueAtTime(84/frameRate, -21.591);
layer_7.property('Transform').property('Rotation').setValueAtTime(85/frameRate, -19.859);
layer_7.property('Transform').property('Rotation').setValueAtTime(86/frameRate, -18.068);
layer_7.property('Transform').property('Rotation').setValueAtTime(87/frameRate, -16.242);
layer_7.property('Transform').property('Rotation').setValueAtTime(88/frameRate, -14.404);
layer_7.property('Transform').property('Rotation').setValueAtTime(89/frameRate, -12.578);
layer_7.property('Transform').property('Rotation').setValueAtTime(90/frameRate, -10.788);
layer_7.property('Transform').property('Rotation').setValueAtTime(91/frameRate, -9.056);
layer_7.property('Transform').property('Rotation').setValueAtTime(92/frameRate, -7.406);
layer_7.property('Transform').property('Rotation').setValueAtTime(93/frameRate, -5.863);
layer_7.property('Transform').property('Rotation').setValueAtTime(94/frameRate, -4.448);
layer_7.property('Transform').property('Rotation').setValueAtTime(95/frameRate, -3.187);
layer_7.property('Transform').property('Rotation').setValueAtTime(96/frameRate, -2.103);
layer_7.property('Transform').property('Rotation').setValueAtTime(97/frameRate, -1.218);
layer_7.property('Transform').property('Rotation').setValueAtTime(98/frameRate, -0.557);
layer_7.property('Transform').property('Rotation').setValueAtTime(99/frameRate, -0.143);
layer_7.property('Transform').property('Rotation').setValueAtTime(100/frameRate, 0);
layer_7.property('Transform').property('Rotation').setValueAtTime(101/frameRate, -0.143);
layer_7.property('Transform').property('Rotation').setValueAtTime(102/frameRate, -0.557);
layer_7.property('Transform').property('Rotation').setValueAtTime(103/frameRate, -1.218);
layer_7.property('Transform').property('Rotation').setValueAtTime(104/frameRate, -2.103);
layer_7.property('Transform').property('Rotation').setValueAtTime(105/frameRate, -3.187);
layer_7.property('Transform').property('Rotation').setValueAtTime(106/frameRate, -4.448);
layer_7.property('Transform').property('Rotation').setValueAtTime(107/frameRate, -5.863);
layer_7.property('Transform').property('Rotation').setValueAtTime(108/frameRate, -7.406);
layer_7.property('Transform').property('Rotation').setValueAtTime(109/frameRate, -9.056);
layer_7.property('Transform').property('Rotation').setValueAtTime(110/frameRate, -10.788);
layer_7.property('Transform').property('Rotation').setValueAtTime(111/frameRate, -12.578);
layer_7.property('Transform').property('Rotation').setValueAtTime(112/frameRate, -14.404);
layer_7.property('Transform').property('Rotation').setValueAtTime(113/frameRate, -16.242);
layer_7.property('Transform').property('Rotation').setValueAtTime(114/frameRate, -18.068);
layer_7.property('Transform').property('Rotation').setValueAtTime(115/frameRate, -19.859);
layer_7.property('Transform').property('Rotation').setValueAtTime(116/frameRate, -21.591);
layer_7.property('Transform').property('Rotation').setValueAtTime(117/frameRate, -23.24);
layer_7.property('Transform').property('Rotation').setValueAtTime(118/frameRate, -24.784);
layer_7.property('Transform').property('Rotation').setValueAtTime(119/frameRate, -26.198);
layer_7.property('Transform').property('Rotation').setValueAtTime(120/frameRate, -27.459);
layer_7.property('Transform').property('Rotation').setValueAtTime(121/frameRate, -28.544);
layer_7.property('Transform').property('Rotation').setValueAtTime(122/frameRate, -29.429);
layer_7.property('Transform').property('Rotation').setValueAtTime(123/frameRate, -30.09);
layer_7.property('Transform').property('Rotation').setValueAtTime(124/frameRate, -30.503);
layer_7.property('Transform').property('Rotation').setValueAtTime(125/frameRate, -30.647);
layer_7.property('Transform').property('Rotation').setValueAtTime(126/frameRate, -30.503);
layer_7.property('Transform').property('Rotation').setValueAtTime(127/frameRate, -30.09);
layer_7.property('Transform').property('Rotation').setValueAtTime(128/frameRate, -29.429);
layer_7.property('Transform').property('Rotation').setValueAtTime(129/frameRate, -28.544);
layer_7.property('Transform').property('Rotation').setValueAtTime(130/frameRate, -27.459);
layer_7.property('Transform').property('Rotation').setValueAtTime(131/frameRate, -26.198);
layer_7.property('Transform').property('Rotation').setValueAtTime(132/frameRate, -24.784);
layer_7.property('Transform').property('Rotation').setValueAtTime(133/frameRate, -23.24);
layer_7.property('Transform').property('Rotation').setValueAtTime(134/frameRate, -21.591);
layer_7.property('Transform').property('Rotation').setValueAtTime(135/frameRate, -19.859);
layer_7.property('Transform').property('Rotation').setValueAtTime(136/frameRate, -18.068);
layer_7.property('Transform').property('Rotation').setValueAtTime(137/frameRate, -16.242);
layer_7.property('Transform').property('Rotation').setValueAtTime(138/frameRate, -14.404);
layer_7.property('Transform').property('Rotation').setValueAtTime(139/frameRate, -12.578);
layer_7.property('Transform').property('Rotation').setValueAtTime(140/frameRate, -10.788);
layer_7.property('Transform').property('Rotation').setValueAtTime(141/frameRate, -9.056);
layer_7.property('Transform').property('Rotation').setValueAtTime(142/frameRate, -7.406);
layer_7.property('Transform').property('Rotation').setValueAtTime(143/frameRate, -5.863);
layer_7.property('Transform').property('Rotation').setValueAtTime(144/frameRate, -4.448);
layer_7.property('Transform').property('Rotation').setValueAtTime(145/frameRate, -3.187);
layer_7.property('Transform').property('Rotation').setValueAtTime(146/frameRate, -2.103);
layer_7.property('Transform').property('Rotation').setValueAtTime(147/frameRate, -1.218);
layer_7.property('Transform').property('Rotation').setValueAtTime(148/frameRate, -0.557);
layer_7.property('Transform').property('Rotation').setValueAtTime(149/frameRate, -0.143);
layer_7.property('Transform').property('Anchor Point').setValue([85.22, 13.507, 0]);

// Layer 8: fether
var layer_8 = createShapeLayer(comp, 'fether');
// Shape Group: Group 1
var layer_8_group_0 = layer_8.property('Contents').addProperty('ADBE Vector Group');
layer_8_group_0.name = 'Group 1';
var layer_8_group_0_path_0 = layer_8_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-14.343, 1.947], [14.344, -0.949], [14.344, -1.949], [-14.343, 0.947]];
pathShape.inTangents = [[-0.641, 0.065], [-9.562, 0.965], [0.641, -0.064], [9.562, -0.965]];
pathShape.outTangents = [[9.562, -0.965], [0.634, -0.064], [-9.562, 0.965], [-0.634, 0.064]];
pathShape.closed = true;
layer_8_group_0_path_0.property('Path').setValue(pathShape);
var layer_8_group_0_fill_1 = layer_8_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_8_group_0_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_8_group_0_fill_1.property('Opacity').setValue(100);
var layer_8_group_0_transform = layer_8_group_0.property('Transform');
layer_8_group_0_transform.property('Position').setValue([57.461, 117.463]);
layer_8_group_0_transform.property('Scale').setValue([100, 100]);
layer_8_group_0_transform.property('Rotation').setValue(0);
layer_8_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_8_group_1 = layer_8.property('Contents').addProperty('ADBE Vector Group');
layer_8_group_1.name = 'Group 2';
var layer_8_group_1_path_0 = layer_8_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-29.501, -25.588], [-7.864, -3.909], [15.27, 15.987], [28.919, 26.388], [29.424, 25.526], [5.481, 6.63], [-16.887, -13.958], [-28.795, -26.294]];
pathShape.inTangents = [[-0.437, -0.472], [-7.484, -6.948], [-7.959, -6.336], [-4.621, -3.371], [0.515, 0.375], [7.738, 6.6], [7.193, 7.141], [3.882, 4.195]];
pathShape.outTangents = [[6.935, 7.496], [7.456, 6.922], [4.476, 3.563], [0.521, 0.38], [-8.217, -5.993], [-7.711, -6.577], [-4.056, -4.027], [-0.438, -0.474]];
pathShape.closed = true;
layer_8_group_1_path_0.property('Path').setValue(pathShape);
var layer_8_group_1_fill_1 = layer_8_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_8_group_1_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_8_group_1_fill_1.property('Opacity').setValue(100);
var layer_8_group_1_transform = layer_8_group_1.property('Transform');
layer_8_group_1_transform.property('Position').setValue([67.821, 58.891]);
layer_8_group_1_transform.property('Scale').setValue([100, 100]);
layer_8_group_1_transform.property('Rotation').setValue(0);
layer_8_group_1_transform.property('Opacity').setValue(100);
// Shape Group: Group 3
var layer_8_group_2 = layer_8.property('Contents').addProperty('ADBE Vector Group');
layer_8_group_2.name = 'Group 3';
var layer_8_group_2_path_0 = layer_8_group_2.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-40.854, -24.266], [-9.542, -4.59], [22.234, 14.408], [40.332, 25.152], [40.837, 24.29], [8.811, 5.267], [-22.805, -13.925], [-40.35, -25.129]];
pathShape.inTangents = [[-0.537, -0.352], [-10.536, -6.401], [-10.611, -6.3], [-6.033, -3.581], [0.554, 0.329], [10.667, 6.355], [10.459, 6.527], [5.805, 3.802]];
pathShape.outTangents = [[10.313, 6.755], [10.548, 6.407], [6.033, 3.582], [0.555, 0.33], [-10.676, -6.339], [-10.591, -6.31], [-5.887, -3.673], [-0.54, -0.354]];
pathShape.closed = true;
layer_8_group_2_path_0.property('Path').setValue(pathShape);
var layer_8_group_2_fill_1 = layer_8_group_2.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_8_group_2_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_8_group_2_fill_1.property('Opacity').setValue(100);
var layer_8_group_2_transform = layer_8_group_2.property('Transform');
layer_8_group_2_transform.property('Position').setValue([52.361, 66.089]);
layer_8_group_2_transform.property('Scale').setValue([100, 100]);
layer_8_group_2_transform.property('Rotation').setValue(0);
layer_8_group_2_transform.property('Opacity').setValue(100);
// Shape Group: Group 4
var layer_8_group_3 = layer_8.property('Contents').addProperty('ADBE Vector Group');
layer_8_group_3.name = 'Group 4';
var layer_8_group_3_path_0 = layer_8_group_3.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-35.575, -16.629], [-8.513, -1.387], [18.418, 9.9], [35.019, 17.572], [35.524, 16.709], [6.968, 4.089], [-19.589, -7.949], [-35.071, -17.492]];
pathShape.inTangents = [[-0.524, -0.366], [-9.46, -4.279], [-8.953, -3.818], [-5.444, -2.748], [0.575, 0.29], [9.642, 3.933], [8.572, 4.616], [4.973, 3.477]];
pathShape.outTangents = [[8.514, 5.954], [8.871, 4.011], [5.609, 2.391], [0.573, 0.29], [-9.302, -4.695], [-9.004, -3.674], [-5.342, -2.876], [-0.528, -0.37]];
pathShape.closed = true;
layer_8_group_3_path_0.property('Path').setValue(pathShape);
var layer_8_group_3_fill_1 = layer_8_group_3.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_8_group_3_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_8_group_3_fill_1.property('Opacity').setValue(100);
var layer_8_group_3_transform = layer_8_group_3.property('Transform');
layer_8_group_3_transform.property('Position').setValue([48.539, 78.838]);
layer_8_group_3_transform.property('Scale').setValue([100, 100]);
layer_8_group_3_transform.property('Rotation').setValue(0);
layer_8_group_3_transform.property('Opacity').setValue(100);
// Shape Group: Group 5
var layer_8_group_4 = layer_8.property('Contents').addProperty('ADBE Vector Group');
layer_8_group_4.name = 'Group 5';
var layer_8_group_4_path_0 = layer_8_group_4.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-28.731, -9.95], [-7.579, -1.214], [16.794, 6.67], [28.197, 10.883], [28.702, 10.02], [6.969, 2.442], [-17.247, -5.754], [-28.227, -10.814]];
pathShape.inTangents = [[-0.559, -0.321], [-7.19, -2.428], [-8.098, -2.709], [-3.723, -1.6], [0.589, 0.254], [7.3, 2.318], [7.941, 3.096], [3.498, 2.007]];
pathShape.outTangents = [[6.608, 3.79], [8.091, 2.731], [3.841, 1.284], [0.585, 0.251], [-7.046, -3.029], [-8.119, -2.578], [-3.752, -1.463], [-0.559, -0.32]];
pathShape.closed = true;
layer_8_group_4_path_0.property('Path').setValue(pathShape);
var layer_8_group_4_fill_1 = layer_8_group_4.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_8_group_4_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_8_group_4_fill_1.property('Opacity').setValue(100);
var layer_8_group_4_transform = layer_8_group_4.property('Transform');
layer_8_group_4_transform.property('Position').setValue([53.791, 101.994]);
layer_8_group_4_transform.property('Scale').setValue([100, 100]);
layer_8_group_4_transform.property('Rotation').setValue(0);
layer_8_group_4_transform.property('Opacity').setValue(100);
// Shape Group: Group 6
var layer_8_group_5 = layer_8.property('Contents').addProperty('ADBE Vector Group');
layer_8_group_5.name = 'Group 6';
var layer_8_group_5_path_0 = layer_8_group_5.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-25.532, 13.107], [14.619, -5.805], [25.83, -12.164], [25.325, -13.027], [-13.856, 7.036], [-25.798, 12.142]];
pathShape.inTangents = [[-0.595, 0.247], [-13.009, 7.08], [-3.701, 2.182], [0.557, -0.327], [13.435, -5.944], [3.999, -1.659]];
pathShape.outTangents = [[13.675, -5.674], [3.774, -2.054], [0.554, -0.327], [-12.657, 7.461], [-3.959, 1.751], [-0.586, 0.243]];
pathShape.closed = true;
layer_8_group_5_path_0.property('Path').setValue(pathShape);
var layer_8_group_5_fill_1 = layer_8_group_5.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_8_group_5_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_8_group_5_fill_1.property('Opacity').setValue(100);
var layer_8_group_5_transform = layer_8_group_5.property('Transform');
layer_8_group_5_transform.property('Position').setValue([52.347, 133.671]);
layer_8_group_5_transform.property('Scale').setValue([100, 100]);
layer_8_group_5_transform.property('Rotation').setValue(0);
layer_8_group_5_transform.property('Opacity').setValue(100);
// Shape Group: Group 7
var layer_8_group_6 = layer_8.property('Contents').addProperty('ADBE Vector Group');
layer_8_group_6.name = 'Group 7';
var layer_8_group_6_path_0 = layer_8_group_6.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-35.264, 27.771], [-5.498, 8.89], [21.655, -13.093], [35.883, -26.913], [35.176, -27.62], [9.444, -3.854], [-18.939, 16.833], [-35.768, 26.908]];
pathShape.inTangents = [[-0.563, 0.312], [-9.546, 6.864], [-8.621, 7.839], [-4.591, 4.759], [0.449, -0.463], [9.026, -7.418], [9.85, -6.34], [5.715, -3.178]];
pathShape.outTangents = [[10.276, -5.712], [9.46, -6.802], [4.892, -4.449], [0.448, -0.462], [-8.112, 8.409], [-9.05, 7.438], [-5.499, 3.539], [-0.563, 0.312]];
pathShape.closed = true;
layer_8_group_6_path_0.property('Path').setValue(pathShape);
var layer_8_group_6_fill_1 = layer_8_group_6.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_8_group_6_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_8_group_6_fill_1.property('Opacity').setValue(100);
var layer_8_group_6_transform = layer_8_group_6.property('Transform');
layer_8_group_6_transform.property('Position').setValue([48.966, 152.268]);
layer_8_group_6_transform.property('Scale').setValue([100, 100]);
layer_8_group_6_transform.property('Rotation').setValue(0);
layer_8_group_6_transform.property('Opacity').setValue(100);
// Shape Group: Group 8
var layer_8_group_7 = layer_8.property('Contents').addProperty('ADBE Vector Group');
layer_8_group_7.name = 'Group 8';
var layer_8_group_7_path_0 = layer_8_group_7.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-38.813, 37.69], [-20.964, 26.322], [-4.824, 12.938], [10.219, -2.146], [24.282, -18.173], [37.838, -34.743], [39.484, -36.788], [38.778, -37.496], [25.337, -20.99], [11.367, -4.87], [-3.39, 10.233], [-19.469, 23.962], [-37.024, 35.562], [-39.318, 36.828]];
pathShape.inTangents = [[-0.567, 0.307], [-5.683, 4.19], [-5.143, 4.738], [-4.825, 5.211], [-4.569, 5.445], [-4.476, 5.558], [-0.548, 0.682], [0.403, -0.501], [4.539, -5.454], [4.79, -5.254], [5.117, -4.836], [5.613, -4.268], [6.116, -3.447], [0.769, -0.415]];
pathShape.outTangents = [[6.214, -3.355], [5.629, -4.151], [5.224, -4.812], [4.831, -5.215], [4.588, -5.467], [0.549, -0.681], [0.4, -0.497], [-4.448, 5.53], [-4.547, 5.466], [-4.742, 5.203], [-5.123, 4.843], [-5.588, 4.248], [-0.761, 0.429], [-0.566, 0.305]];
pathShape.closed = true;
layer_8_group_7_path_0.property('Path').setValue(pathShape);
var layer_8_group_7_fill_1 = layer_8_group_7.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_8_group_7_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_8_group_7_fill_1.property('Opacity').setValue(100);
var layer_8_group_7_transform = layer_8_group_7.property('Transform');
layer_8_group_7_transform.property('Position').setValue([50.8, 168.36]);
layer_8_group_7_transform.property('Scale').setValue([100, 100]);
layer_8_group_7_transform.property('Rotation').setValue(0);
layer_8_group_7_transform.property('Opacity').setValue(100);
// Shape Group: Group 9
var layer_8_group_8 = layer_8.property('Contents').addProperty('ADBE Vector Group');
layer_8_group_8.name = 'Group 9';
var layer_8_group_8_path_0 = layer_8_group_8.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-33.138, 42.841], [-3.914, 11.981], [21.505, -21.967], [33.995, -42.218], [33.132, -42.722], [10.035, -7.305], [-17.051, 25.282], [-33.845, 42.134]];
pathShape.inTangents = [[-0.469, 0.44], [-9.135, 10.84], [-7.811, 11.791], [-3.944, 6.883], [0.32, -0.559], [8.371, -11.35], [9.646, -10.329], [5.778, -5.435]];
pathShape.outTangents = [[10.326, -9.712], [9.114, -10.817], [4.381, -6.612], [0.321, -0.559], [-7.011, 12.236], [-8.389, 11.373], [-5.414, 5.797], [-0.47, 0.441]];
pathShape.closed = true;
layer_8_group_8_path_0.property('Path').setValue(pathShape);
var layer_8_group_8_fill_1 = layer_8_group_8.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_8_group_8_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_8_group_8_fill_1.property('Opacity').setValue(100);
var layer_8_group_8_transform = layer_8_group_8.property('Transform');
layer_8_group_8_transform.property('Position').setValue([63.454, 177.807]);
layer_8_group_8_transform.property('Scale').setValue([100, 100]);
layer_8_group_8_transform.property('Rotation').setValue(0);
layer_8_group_8_transform.property('Opacity').setValue(100);
// Shape Group: Group 10
var layer_8_group_9 = layer_8.property('Contents').addProperty('ADBE Vector Group');
layer_8_group_9.name = 'Group 10';
var layer_8_group_9_path_0 = layer_8_group_9.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[28.313, -29.795], [12.946, -30.207], [-80.551, -97.811], [-84.866, -66.166], [-54.659, -4.316], [-66.166, 27.329], [-81.989, 93.496], [-47.467, 99.249], [12.946, 27.329], [35.468, 27.264]];
pathShape.inTangents = [[62.307, -6.166], [0, 0], [33.084, -21.576], [-5.754, -27.33], [-10.069, -7.192], [20.137, -24.453], [-8.631, -25.892], [0, 0], [-15.823, 33.083], [-18.207, -10.003]];
pathShape.outTangents = [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.631, 25.891], [0, 0], [0, 0], [35.014, 4.38]];
pathShape.closed = true;
layer_8_group_9_path_0.property('Path').setValue(pathShape);
var layer_8_group_9_fill_1 = layer_8_group_9.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_8_group_9_fill_1.property('Color').setValue([0.972549, 0.305882, 0.192157]);
layer_8_group_9_fill_1.property('Opacity').setValue(100);
var layer_8_group_9_transform = layer_8_group_9.property('Transform');
layer_8_group_9_transform.property('Position').setValue([90.87, 119.638]);
layer_8_group_9_transform.property('Scale').setValue([100, 100]);
layer_8_group_9_transform.property('Rotation').setValue(0);
layer_8_group_9_transform.property('Opacity').setValue(100);
layer_8.inPoint = 0.000000;
layer_8.outPoint = 6.250000;
layer_8.startTime = 0.000000;
layer_8.parent = layer_4;
layer_8.property('Transform').property('Opacity').setValue(100);
layer_8.property('Transform').property('Position').setValue([69.826, 115.323, 0]);
layer_8.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 150 keyframes for layer_8.property('Transform').property('Rotation')
layer_8.property('Transform').property('Rotation').setValueAtTime(0/frameRate, 12.433);
layer_8.property('Transform').property('Rotation').setValueAtTime(1/frameRate, 12.334);
layer_8.property('Transform').property('Rotation').setValueAtTime(2/frameRate, 12.048);
layer_8.property('Transform').property('Rotation').setValueAtTime(3/frameRate, 11.592);
layer_8.property('Transform').property('Rotation').setValueAtTime(4/frameRate, 10.981);
layer_8.property('Transform').property('Rotation').setValueAtTime(5/frameRate, 10.233);
layer_8.property('Transform').property('Rotation').setValueAtTime(6/frameRate, 9.362);
layer_8.property('Transform').property('Rotation').setValueAtTime(7/frameRate, 8.386);
layer_8.property('Transform').property('Rotation').setValueAtTime(8/frameRate, 7.32);
layer_8.property('Transform').property('Rotation').setValueAtTime(9/frameRate, 6.182);
layer_8.property('Transform').property('Rotation').setValueAtTime(10/frameRate, 4.986);
layer_8.property('Transform').property('Rotation').setValueAtTime(11/frameRate, 3.75);
layer_8.property('Transform').property('Rotation').setValueAtTime(12/frameRate, 2.49);
layer_8.property('Transform').property('Rotation').setValueAtTime(13/frameRate, 1.221);
layer_8.property('Transform').property('Rotation').setValueAtTime(14/frameRate, -0.039);
layer_8.property('Transform').property('Rotation').setValueAtTime(15/frameRate, -1.275);
layer_8.property('Transform').property('Rotation').setValueAtTime(16/frameRate, -2.471);
layer_8.property('Transform').property('Rotation').setValueAtTime(17/frameRate, -3.609);
layer_8.property('Transform').property('Rotation').setValueAtTime(18/frameRate, -4.675);
layer_8.property('Transform').property('Rotation').setValueAtTime(19/frameRate, -5.651);
layer_8.property('Transform').property('Rotation').setValueAtTime(20/frameRate, -6.521);
layer_8.property('Transform').property('Rotation').setValueAtTime(21/frameRate, -7.27);
layer_8.property('Transform').property('Rotation').setValueAtTime(22/frameRate, -7.88);
layer_8.property('Transform').property('Rotation').setValueAtTime(23/frameRate, -8.337);
layer_8.property('Transform').property('Rotation').setValueAtTime(24/frameRate, -8.622);
layer_8.property('Transform').property('Rotation').setValueAtTime(25/frameRate, -8.721);
layer_8.property('Transform').property('Rotation').setValueAtTime(26/frameRate, -8.622);
layer_8.property('Transform').property('Rotation').setValueAtTime(27/frameRate, -8.337);
layer_8.property('Transform').property('Rotation').setValueAtTime(28/frameRate, -7.88);
layer_8.property('Transform').property('Rotation').setValueAtTime(29/frameRate, -7.27);
layer_8.property('Transform').property('Rotation').setValueAtTime(30/frameRate, -6.521);
layer_8.property('Transform').property('Rotation').setValueAtTime(31/frameRate, -5.651);
layer_8.property('Transform').property('Rotation').setValueAtTime(32/frameRate, -4.675);
layer_8.property('Transform').property('Rotation').setValueAtTime(33/frameRate, -3.609);
layer_8.property('Transform').property('Rotation').setValueAtTime(34/frameRate, -2.471);
layer_8.property('Transform').property('Rotation').setValueAtTime(35/frameRate, -1.275);
layer_8.property('Transform').property('Rotation').setValueAtTime(36/frameRate, -0.039);
layer_8.property('Transform').property('Rotation').setValueAtTime(37/frameRate, 1.221);
layer_8.property('Transform').property('Rotation').setValueAtTime(38/frameRate, 2.49);
layer_8.property('Transform').property('Rotation').setValueAtTime(39/frameRate, 3.75);
layer_8.property('Transform').property('Rotation').setValueAtTime(40/frameRate, 4.986);
layer_8.property('Transform').property('Rotation').setValueAtTime(41/frameRate, 6.182);
layer_8.property('Transform').property('Rotation').setValueAtTime(42/frameRate, 7.32);
layer_8.property('Transform').property('Rotation').setValueAtTime(43/frameRate, 8.386);
layer_8.property('Transform').property('Rotation').setValueAtTime(44/frameRate, 9.362);
layer_8.property('Transform').property('Rotation').setValueAtTime(45/frameRate, 10.233);
layer_8.property('Transform').property('Rotation').setValueAtTime(46/frameRate, 10.981);
layer_8.property('Transform').property('Rotation').setValueAtTime(47/frameRate, 11.592);
layer_8.property('Transform').property('Rotation').setValueAtTime(48/frameRate, 12.048);
layer_8.property('Transform').property('Rotation').setValueAtTime(49/frameRate, 12.334);
layer_8.property('Transform').property('Rotation').setValueAtTime(50/frameRate, 12.433);
layer_8.property('Transform').property('Rotation').setValueAtTime(51/frameRate, 12.334);
layer_8.property('Transform').property('Rotation').setValueAtTime(52/frameRate, 12.048);
layer_8.property('Transform').property('Rotation').setValueAtTime(53/frameRate, 11.592);
layer_8.property('Transform').property('Rotation').setValueAtTime(54/frameRate, 10.981);
layer_8.property('Transform').property('Rotation').setValueAtTime(55/frameRate, 10.233);
layer_8.property('Transform').property('Rotation').setValueAtTime(56/frameRate, 9.362);
layer_8.property('Transform').property('Rotation').setValueAtTime(57/frameRate, 8.386);
layer_8.property('Transform').property('Rotation').setValueAtTime(58/frameRate, 7.32);
layer_8.property('Transform').property('Rotation').setValueAtTime(59/frameRate, 6.182);
layer_8.property('Transform').property('Rotation').setValueAtTime(60/frameRate, 4.986);
layer_8.property('Transform').property('Rotation').setValueAtTime(61/frameRate, 3.75);
layer_8.property('Transform').property('Rotation').setValueAtTime(62/frameRate, 2.49);
layer_8.property('Transform').property('Rotation').setValueAtTime(63/frameRate, 1.221);
layer_8.property('Transform').property('Rotation').setValueAtTime(64/frameRate, -0.039);
layer_8.property('Transform').property('Rotation').setValueAtTime(65/frameRate, -1.275);
layer_8.property('Transform').property('Rotation').setValueAtTime(66/frameRate, -2.471);
layer_8.property('Transform').property('Rotation').setValueAtTime(67/frameRate, -3.609);
layer_8.property('Transform').property('Rotation').setValueAtTime(68/frameRate, -4.675);
layer_8.property('Transform').property('Rotation').setValueAtTime(69/frameRate, -5.651);
layer_8.property('Transform').property('Rotation').setValueAtTime(70/frameRate, -6.521);
layer_8.property('Transform').property('Rotation').setValueAtTime(71/frameRate, -7.27);
layer_8.property('Transform').property('Rotation').setValueAtTime(72/frameRate, -7.88);
layer_8.property('Transform').property('Rotation').setValueAtTime(73/frameRate, -8.337);
layer_8.property('Transform').property('Rotation').setValueAtTime(74/frameRate, -8.622);
layer_8.property('Transform').property('Rotation').setValueAtTime(75/frameRate, -8.721);
layer_8.property('Transform').property('Rotation').setValueAtTime(76/frameRate, -8.622);
layer_8.property('Transform').property('Rotation').setValueAtTime(77/frameRate, -8.337);
layer_8.property('Transform').property('Rotation').setValueAtTime(78/frameRate, -7.88);
layer_8.property('Transform').property('Rotation').setValueAtTime(79/frameRate, -7.27);
layer_8.property('Transform').property('Rotation').setValueAtTime(80/frameRate, -6.521);
layer_8.property('Transform').property('Rotation').setValueAtTime(81/frameRate, -5.651);
layer_8.property('Transform').property('Rotation').setValueAtTime(82/frameRate, -4.675);
layer_8.property('Transform').property('Rotation').setValueAtTime(83/frameRate, -3.609);
layer_8.property('Transform').property('Rotation').setValueAtTime(84/frameRate, -2.471);
layer_8.property('Transform').property('Rotation').setValueAtTime(85/frameRate, -1.275);
layer_8.property('Transform').property('Rotation').setValueAtTime(86/frameRate, -0.039);
layer_8.property('Transform').property('Rotation').setValueAtTime(87/frameRate, 1.221);
layer_8.property('Transform').property('Rotation').setValueAtTime(88/frameRate, 2.49);
layer_8.property('Transform').property('Rotation').setValueAtTime(89/frameRate, 3.75);
layer_8.property('Transform').property('Rotation').setValueAtTime(90/frameRate, 4.986);
layer_8.property('Transform').property('Rotation').setValueAtTime(91/frameRate, 6.182);
layer_8.property('Transform').property('Rotation').setValueAtTime(92/frameRate, 7.32);
layer_8.property('Transform').property('Rotation').setValueAtTime(93/frameRate, 8.386);
layer_8.property('Transform').property('Rotation').setValueAtTime(94/frameRate, 9.362);
layer_8.property('Transform').property('Rotation').setValueAtTime(95/frameRate, 10.233);
layer_8.property('Transform').property('Rotation').setValueAtTime(96/frameRate, 10.981);
layer_8.property('Transform').property('Rotation').setValueAtTime(97/frameRate, 11.592);
layer_8.property('Transform').property('Rotation').setValueAtTime(98/frameRate, 12.048);
layer_8.property('Transform').property('Rotation').setValueAtTime(99/frameRate, 12.334);
layer_8.property('Transform').property('Rotation').setValueAtTime(100/frameRate, 12.433);
layer_8.property('Transform').property('Rotation').setValueAtTime(101/frameRate, 12.334);
layer_8.property('Transform').property('Rotation').setValueAtTime(102/frameRate, 12.048);
layer_8.property('Transform').property('Rotation').setValueAtTime(103/frameRate, 11.592);
layer_8.property('Transform').property('Rotation').setValueAtTime(104/frameRate, 10.981);
layer_8.property('Transform').property('Rotation').setValueAtTime(105/frameRate, 10.233);
layer_8.property('Transform').property('Rotation').setValueAtTime(106/frameRate, 9.362);
layer_8.property('Transform').property('Rotation').setValueAtTime(107/frameRate, 8.386);
layer_8.property('Transform').property('Rotation').setValueAtTime(108/frameRate, 7.32);
layer_8.property('Transform').property('Rotation').setValueAtTime(109/frameRate, 6.182);
layer_8.property('Transform').property('Rotation').setValueAtTime(110/frameRate, 4.986);
layer_8.property('Transform').property('Rotation').setValueAtTime(111/frameRate, 3.75);
layer_8.property('Transform').property('Rotation').setValueAtTime(112/frameRate, 2.49);
layer_8.property('Transform').property('Rotation').setValueAtTime(113/frameRate, 1.221);
layer_8.property('Transform').property('Rotation').setValueAtTime(114/frameRate, -0.039);
layer_8.property('Transform').property('Rotation').setValueAtTime(115/frameRate, -1.275);
layer_8.property('Transform').property('Rotation').setValueAtTime(116/frameRate, -2.471);
layer_8.property('Transform').property('Rotation').setValueAtTime(117/frameRate, -3.609);
layer_8.property('Transform').property('Rotation').setValueAtTime(118/frameRate, -4.675);
layer_8.property('Transform').property('Rotation').setValueAtTime(119/frameRate, -5.651);
layer_8.property('Transform').property('Rotation').setValueAtTime(120/frameRate, -6.521);
layer_8.property('Transform').property('Rotation').setValueAtTime(121/frameRate, -7.27);
layer_8.property('Transform').property('Rotation').setValueAtTime(122/frameRate, -7.88);
layer_8.property('Transform').property('Rotation').setValueAtTime(123/frameRate, -8.337);
layer_8.property('Transform').property('Rotation').setValueAtTime(124/frameRate, -8.622);
layer_8.property('Transform').property('Rotation').setValueAtTime(125/frameRate, -8.721);
layer_8.property('Transform').property('Rotation').setValueAtTime(126/frameRate, -8.622);
layer_8.property('Transform').property('Rotation').setValueAtTime(127/frameRate, -8.337);
layer_8.property('Transform').property('Rotation').setValueAtTime(128/frameRate, -7.88);
layer_8.property('Transform').property('Rotation').setValueAtTime(129/frameRate, -7.27);
layer_8.property('Transform').property('Rotation').setValueAtTime(130/frameRate, -6.521);
layer_8.property('Transform').property('Rotation').setValueAtTime(131/frameRate, -5.651);
layer_8.property('Transform').property('Rotation').setValueAtTime(132/frameRate, -4.675);
layer_8.property('Transform').property('Rotation').setValueAtTime(133/frameRate, -3.609);
layer_8.property('Transform').property('Rotation').setValueAtTime(134/frameRate, -2.471);
layer_8.property('Transform').property('Rotation').setValueAtTime(135/frameRate, -1.275);
layer_8.property('Transform').property('Rotation').setValueAtTime(136/frameRate, -0.039);
layer_8.property('Transform').property('Rotation').setValueAtTime(137/frameRate, 1.221);
layer_8.property('Transform').property('Rotation').setValueAtTime(138/frameRate, 2.49);
layer_8.property('Transform').property('Rotation').setValueAtTime(139/frameRate, 3.75);
layer_8.property('Transform').property('Rotation').setValueAtTime(140/frameRate, 4.986);
layer_8.property('Transform').property('Rotation').setValueAtTime(141/frameRate, 6.182);
layer_8.property('Transform').property('Rotation').setValueAtTime(142/frameRate, 7.32);
layer_8.property('Transform').property('Rotation').setValueAtTime(143/frameRate, 8.386);
layer_8.property('Transform').property('Rotation').setValueAtTime(144/frameRate, 9.362);
layer_8.property('Transform').property('Rotation').setValueAtTime(145/frameRate, 10.233);
layer_8.property('Transform').property('Rotation').setValueAtTime(146/frameRate, 10.981);
layer_8.property('Transform').property('Rotation').setValueAtTime(147/frameRate, 11.592);
layer_8.property('Transform').property('Rotation').setValueAtTime(148/frameRate, 12.048);
layer_8.property('Transform').property('Rotation').setValueAtTime(149/frameRate, 12.334);
layer_8.property('Transform').property('Anchor Point').setValue([138.87, 119.638, 0]);

// Layer 9: Fether
var layer_9 = createShapeLayer(comp, 'Fether');
// Shape Group: Group 1
var layer_9_group_0 = layer_9.property('Contents').addProperty('ADBE Vector Group');
layer_9_group_0.name = 'Group 1';
var layer_9_group_0_path_0 = layer_9_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-70.911, -20.198], [-33.514, -14.444], [70.911, 20.198]];
pathShape.inTangents = [[0, 0], [0, 0], [0, 0]];
pathShape.outTangents = [[0, 0], [0, 0], [0, 0]];
pathShape.closed = false;
layer_9_group_0_path_0.property('Path').setValue(pathShape);
var layer_9_group_0_stroke_1 = layer_9_group_0.property('Contents').addProperty('ADBE Vector Graphic - Stroke');
layer_9_group_0_stroke_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_9_group_0_stroke_1.property('Stroke Width').setValue(1);
layer_9_group_0_stroke_1.property('Opacity').setValue(100);
var layer_9_group_0_transform = layer_9_group_0.property('Transform');
layer_9_group_0_transform.property('Position').setValue([74.039, 136.959]);
layer_9_group_0_transform.property('Scale').setValue([100, 100]);
layer_9_group_0_transform.property('Rotation').setValue(0);
layer_9_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_9_group_1 = layer_9.property('Contents').addProperty('ADBE Vector Group');
layer_9_group_1.name = 'Group 2';
var layer_9_group_1_path_0 = layer_9_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-20.857, -14.384], [20.857, 14.384]];
pathShape.inTangents = [[0, 0], [-10.069, -10.069]];
pathShape.outTangents = [[0, 0], [0, 0]];
pathShape.closed = false;
layer_9_group_1_path_0.property('Path').setValue(pathShape);
var layer_9_group_1_stroke_1 = layer_9_group_1.property('Contents').addProperty('ADBE Vector Graphic - Stroke');
layer_9_group_1_stroke_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_9_group_1_stroke_1.property('Stroke Width').setValue(1);
layer_9_group_1_stroke_1.property('Opacity').setValue(100);
var layer_9_group_1_transform = layer_9_group_1.property('Transform');
layer_9_group_1_transform.property('Position').setValue([32.615, 100.939]);
layer_9_group_1_transform.property('Scale').setValue([100, 100]);
layer_9_group_1_transform.property('Rotation').setValue(0);
layer_9_group_1_transform.property('Opacity').setValue(100);
// Shape Group: Group 3
var layer_9_group_2 = layer_9.property('Contents').addProperty('ADBE Vector Group');
layer_9_group_2.name = 'Group 3';
var layer_9_group_2_path_0 = layer_9_group_2.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-64.728, -30.926], [-15.823, 10.789], [64.728, 51.064], [7.191, 10.789], [-53.221, -51.064]];
pathShape.inTangents = [[0, 0], [-14.384, -20.138], [0, 0], [0, 0], [0, 0]];
pathShape.outTangents = [[0, 0], [0, 0], [0, 0], [-8.63, -18.699], [0, 0]];
pathShape.closed = false;
layer_9_group_2_path_0.property('Path').setValue(pathShape);
var layer_9_group_2_stroke_1 = layer_9_group_2.property('Contents').addProperty('ADBE Vector Graphic - Stroke');
layer_9_group_2_stroke_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_9_group_2_stroke_1.property('Stroke Width').setValue(1);
layer_9_group_2_stroke_1.property('Opacity').setValue(100);
var layer_9_group_2_transform = layer_9_group_2.property('Transform');
layer_9_group_2_transform.property('Position').setValue([83.678, 100.219]);
layer_9_group_2_transform.property('Scale').setValue([100, 100]);
layer_9_group_2_transform.property('Rotation').setValue(0);
layer_9_group_2_transform.property('Opacity').setValue(100);
// Shape Group: Group 4
var layer_9_group_3 = layer_9.property('Contents').addProperty('ADBE Vector Group');
layer_9_group_3.name = 'Group 4';
var layer_9_group_3_path_0 = layer_9_group_3.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-53.729, -50.376], [13.876, 18.667], [53.729, 66.198], [32.575, 12.914], [-39.345, -66.198]];
pathShape.inTangents = [[0, 0], [-8.63, -25.891], [0, 0], [0, 0], [0, 0]];
pathShape.outTangents = [[0, 0], [0, 0], [0, 0], [-18.699, -44.591], [0, 0]];
pathShape.closed = false;
layer_9_group_3_path_0.property('Path').setValue(pathShape);
var layer_9_group_3_stroke_1 = layer_9_group_3.property('Contents').addProperty('ADBE Vector Graphic - Stroke');
layer_9_group_3_stroke_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_9_group_3_stroke_1.property('Stroke Width').setValue(1);
layer_9_group_3_stroke_1.property('Opacity').setValue(100);
var layer_9_group_3_transform = layer_9_group_3.property('Transform');
layer_9_group_3_transform.property('Position').setValue([94.255, 85.149]);
layer_9_group_3_transform.property('Scale').setValue([100, 100]);
layer_9_group_3_transform.property('Rotation').setValue(0);
layer_9_group_3_transform.property('Opacity').setValue(100);
// Shape Group: Group 5
var layer_9_group_4 = layer_9.property('Contents').addProperty('ADBE Vector Group');
layer_9_group_4.name = 'Group 5';
var layer_9_group_4_path_0 = layer_9_group_4.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-4.928, -73.536], [61.239, 12.768], [76.13, 73.536], [-35.855, 36.503], [-76.13, 22.119]];
pathShape.inTangents = [[0, 0], [-7.193, -54.659], [0, 0], [0, 0], [0, 0]];
pathShape.outTangents = [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]];
pathShape.closed = false;
layer_9_group_4_path_0.property('Path').setValue(pathShape);
var layer_9_group_4_stroke_1 = layer_9_group_4.property('Contents').addProperty('ADBE Vector Graphic - Stroke');
layer_9_group_4_stroke_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_9_group_4_stroke_1.property('Stroke Width').setValue(1);
layer_9_group_4_stroke_1.property('Opacity').setValue(100);
var layer_9_group_4_transform = layer_9_group_4.property('Transform');
layer_9_group_4_transform.property('Position').setValue([79.257, 80.259]);
layer_9_group_4_transform.property('Scale').setValue([100, 100]);
layer_9_group_4_transform.property('Rotation').setValue(0);
layer_9_group_4_transform.property('Opacity').setValue(100);
// Shape Group: Group 6
var layer_9_group_5 = layer_9.property('Contents').addProperty('ADBE Vector Group');
layer_9_group_5.name = 'Group 6';
var layer_9_group_5_path_0 = layer_9_group_5.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[78.393, 17.98], [9.35, -79.832], [-7.911, -72.639], [-23.733, -61.132], [-40.994, -45.31], [-52.502, -30.926], [-62.57, -12.226], [-68.324, 5.035], [-74.077, 20.857], [-75.516, 33.803], [-74.077, 46.749], [-42.433, 53.94], [29.488, 71.201], [55.379, 76.955], [78.393, 66.886]];
pathShape.inTangents = [[0, 0], [57.536, 11.508], [5.754, -7.193], [5.753, -11.507], [2.877, -14.384], [-1.438, -10.068], [-2.877, -11.507], [-5.753, -12.946], [-4.316, -14.384], [-4.315, -5.754], [-7.193, -4.316], [0, 0], [0, 0], [0, 0], [-2.876, 12.946]];
pathShape.outTangents = [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.877, -12.946]];
pathShape.closed = true;
layer_9_group_5_path_0.property('Path').setValue(pathShape);
var layer_9_group_5_fill_1 = layer_9_group_5.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_9_group_5_fill_1.property('Color').setValue([0.972549, 0.305882, 0.192157]);
layer_9_group_5_fill_1.property('Opacity').setValue(100);
var layer_9_group_5_transform = layer_9_group_5.property('Transform');
layer_9_group_5_transform.property('Position').setValue([81.52, 80.082]);
layer_9_group_5_transform.property('Scale').setValue([100, 100]);
layer_9_group_5_transform.property('Rotation').setValue(0);
layer_9_group_5_transform.property('Opacity').setValue(100);
layer_9.inPoint = 0.000000;
layer_9.outPoint = 6.250000;
layer_9.startTime = 0.000000;
layer_9.parent = layer_4;
layer_9.property('Transform').property('Opacity').setValue(100);
layer_9.property('Transform').property('Position').setValue([157.15, 44.639, 0]);
layer_9.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 150 keyframes for layer_9.property('Transform').property('Rotation')
layer_9.property('Transform').property('Rotation').setValueAtTime(0/frameRate, 0);
layer_9.property('Transform').property('Rotation').setValueAtTime(1/frameRate, -0.087);
layer_9.property('Transform').property('Rotation').setValueAtTime(2/frameRate, -0.34);
layer_9.property('Transform').property('Rotation').setValueAtTime(3/frameRate, -0.743);
layer_9.property('Transform').property('Rotation').setValueAtTime(4/frameRate, -1.283);
layer_9.property('Transform').property('Rotation').setValueAtTime(5/frameRate, -1.945);
layer_9.property('Transform').property('Rotation').setValueAtTime(6/frameRate, -2.715);
layer_9.property('Transform').property('Rotation').setValueAtTime(7/frameRate, -3.578);
layer_9.property('Transform').property('Rotation').setValueAtTime(8/frameRate, -4.52);
layer_9.property('Transform').property('Rotation').setValueAtTime(9/frameRate, -5.527);
layer_9.property('Transform').property('Rotation').setValueAtTime(10/frameRate, -6.584);
layer_9.property('Transform').property('Rotation').setValueAtTime(11/frameRate, -7.677);
layer_9.property('Transform').property('Rotation').setValueAtTime(12/frameRate, -8.791);
layer_9.property('Transform').property('Rotation').setValueAtTime(13/frameRate, -9.913);
layer_9.property('Transform').property('Rotation').setValueAtTime(14/frameRate, -11.027);
layer_9.property('Transform').property('Rotation').setValueAtTime(15/frameRate, -12.12);
layer_9.property('Transform').property('Rotation').setValueAtTime(16/frameRate, -13.177);
layer_9.property('Transform').property('Rotation').setValueAtTime(17/frameRate, -14.184);
layer_9.property('Transform').property('Rotation').setValueAtTime(18/frameRate, -15.126);
layer_9.property('Transform').property('Rotation').setValueAtTime(19/frameRate, -15.989);
layer_9.property('Transform').property('Rotation').setValueAtTime(20/frameRate, -16.758);
layer_9.property('Transform').property('Rotation').setValueAtTime(21/frameRate, -17.42);
layer_9.property('Transform').property('Rotation').setValueAtTime(22/frameRate, -17.96);
layer_9.property('Transform').property('Rotation').setValueAtTime(23/frameRate, -18.364);
layer_9.property('Transform').property('Rotation').setValueAtTime(24/frameRate, -18.616);
layer_9.property('Transform').property('Rotation').setValueAtTime(25/frameRate, -18.704);
layer_9.property('Transform').property('Rotation').setValueAtTime(26/frameRate, -18.616);
layer_9.property('Transform').property('Rotation').setValueAtTime(27/frameRate, -18.364);
layer_9.property('Transform').property('Rotation').setValueAtTime(28/frameRate, -17.96);
layer_9.property('Transform').property('Rotation').setValueAtTime(29/frameRate, -17.42);
layer_9.property('Transform').property('Rotation').setValueAtTime(30/frameRate, -16.758);
layer_9.property('Transform').property('Rotation').setValueAtTime(31/frameRate, -15.989);
layer_9.property('Transform').property('Rotation').setValueAtTime(32/frameRate, -15.126);
layer_9.property('Transform').property('Rotation').setValueAtTime(33/frameRate, -14.184);
layer_9.property('Transform').property('Rotation').setValueAtTime(34/frameRate, -13.177);
layer_9.property('Transform').property('Rotation').setValueAtTime(35/frameRate, -12.12);
layer_9.property('Transform').property('Rotation').setValueAtTime(36/frameRate, -11.027);
layer_9.property('Transform').property('Rotation').setValueAtTime(37/frameRate, -9.913);
layer_9.property('Transform').property('Rotation').setValueAtTime(38/frameRate, -8.791);
layer_9.property('Transform').property('Rotation').setValueAtTime(39/frameRate, -7.677);
layer_9.property('Transform').property('Rotation').setValueAtTime(40/frameRate, -6.584);
layer_9.property('Transform').property('Rotation').setValueAtTime(41/frameRate, -5.527);
layer_9.property('Transform').property('Rotation').setValueAtTime(42/frameRate, -4.52);
layer_9.property('Transform').property('Rotation').setValueAtTime(43/frameRate, -3.578);
layer_9.property('Transform').property('Rotation').setValueAtTime(44/frameRate, -2.715);
layer_9.property('Transform').property('Rotation').setValueAtTime(45/frameRate, -1.945);
layer_9.property('Transform').property('Rotation').setValueAtTime(46/frameRate, -1.283);
layer_9.property('Transform').property('Rotation').setValueAtTime(47/frameRate, -0.743);
layer_9.property('Transform').property('Rotation').setValueAtTime(48/frameRate, -0.34);
layer_9.property('Transform').property('Rotation').setValueAtTime(49/frameRate, -0.087);
layer_9.property('Transform').property('Rotation').setValueAtTime(50/frameRate, 0);
layer_9.property('Transform').property('Rotation').setValueAtTime(51/frameRate, -0.087);
layer_9.property('Transform').property('Rotation').setValueAtTime(52/frameRate, -0.34);
layer_9.property('Transform').property('Rotation').setValueAtTime(53/frameRate, -0.743);
layer_9.property('Transform').property('Rotation').setValueAtTime(54/frameRate, -1.283);
layer_9.property('Transform').property('Rotation').setValueAtTime(55/frameRate, -1.945);
layer_9.property('Transform').property('Rotation').setValueAtTime(56/frameRate, -2.715);
layer_9.property('Transform').property('Rotation').setValueAtTime(57/frameRate, -3.578);
layer_9.property('Transform').property('Rotation').setValueAtTime(58/frameRate, -4.52);
layer_9.property('Transform').property('Rotation').setValueAtTime(59/frameRate, -5.527);
layer_9.property('Transform').property('Rotation').setValueAtTime(60/frameRate, -6.584);
layer_9.property('Transform').property('Rotation').setValueAtTime(61/frameRate, -7.677);
layer_9.property('Transform').property('Rotation').setValueAtTime(62/frameRate, -8.791);
layer_9.property('Transform').property('Rotation').setValueAtTime(63/frameRate, -9.913);
layer_9.property('Transform').property('Rotation').setValueAtTime(64/frameRate, -11.027);
layer_9.property('Transform').property('Rotation').setValueAtTime(65/frameRate, -12.12);
layer_9.property('Transform').property('Rotation').setValueAtTime(66/frameRate, -13.177);
layer_9.property('Transform').property('Rotation').setValueAtTime(67/frameRate, -14.184);
layer_9.property('Transform').property('Rotation').setValueAtTime(68/frameRate, -15.126);
layer_9.property('Transform').property('Rotation').setValueAtTime(69/frameRate, -15.989);
layer_9.property('Transform').property('Rotation').setValueAtTime(70/frameRate, -16.758);
layer_9.property('Transform').property('Rotation').setValueAtTime(71/frameRate, -17.42);
layer_9.property('Transform').property('Rotation').setValueAtTime(72/frameRate, -17.96);
layer_9.property('Transform').property('Rotation').setValueAtTime(73/frameRate, -18.364);
layer_9.property('Transform').property('Rotation').setValueAtTime(74/frameRate, -18.616);
layer_9.property('Transform').property('Rotation').setValueAtTime(75/frameRate, -18.704);
layer_9.property('Transform').property('Rotation').setValueAtTime(76/frameRate, -18.616);
layer_9.property('Transform').property('Rotation').setValueAtTime(77/frameRate, -18.364);
layer_9.property('Transform').property('Rotation').setValueAtTime(78/frameRate, -17.96);
layer_9.property('Transform').property('Rotation').setValueAtTime(79/frameRate, -17.42);
layer_9.property('Transform').property('Rotation').setValueAtTime(80/frameRate, -16.758);
layer_9.property('Transform').property('Rotation').setValueAtTime(81/frameRate, -15.989);
layer_9.property('Transform').property('Rotation').setValueAtTime(82/frameRate, -15.126);
layer_9.property('Transform').property('Rotation').setValueAtTime(83/frameRate, -14.184);
layer_9.property('Transform').property('Rotation').setValueAtTime(84/frameRate, -13.177);
layer_9.property('Transform').property('Rotation').setValueAtTime(85/frameRate, -12.12);
layer_9.property('Transform').property('Rotation').setValueAtTime(86/frameRate, -11.027);
layer_9.property('Transform').property('Rotation').setValueAtTime(87/frameRate, -9.913);
layer_9.property('Transform').property('Rotation').setValueAtTime(88/frameRate, -8.791);
layer_9.property('Transform').property('Rotation').setValueAtTime(89/frameRate, -7.677);
layer_9.property('Transform').property('Rotation').setValueAtTime(90/frameRate, -6.584);
layer_9.property('Transform').property('Rotation').setValueAtTime(91/frameRate, -5.527);
layer_9.property('Transform').property('Rotation').setValueAtTime(92/frameRate, -4.52);
layer_9.property('Transform').property('Rotation').setValueAtTime(93/frameRate, -3.578);
layer_9.property('Transform').property('Rotation').setValueAtTime(94/frameRate, -2.715);
layer_9.property('Transform').property('Rotation').setValueAtTime(95/frameRate, -1.945);
layer_9.property('Transform').property('Rotation').setValueAtTime(96/frameRate, -1.283);
layer_9.property('Transform').property('Rotation').setValueAtTime(97/frameRate, -0.743);
layer_9.property('Transform').property('Rotation').setValueAtTime(98/frameRate, -0.34);
layer_9.property('Transform').property('Rotation').setValueAtTime(99/frameRate, -0.087);
layer_9.property('Transform').property('Rotation').setValueAtTime(100/frameRate, 0);
layer_9.property('Transform').property('Rotation').setValueAtTime(101/frameRate, -0.087);
layer_9.property('Transform').property('Rotation').setValueAtTime(102/frameRate, -0.34);
layer_9.property('Transform').property('Rotation').setValueAtTime(103/frameRate, -0.743);
layer_9.property('Transform').property('Rotation').setValueAtTime(104/frameRate, -1.283);
layer_9.property('Transform').property('Rotation').setValueAtTime(105/frameRate, -1.945);
layer_9.property('Transform').property('Rotation').setValueAtTime(106/frameRate, -2.715);
layer_9.property('Transform').property('Rotation').setValueAtTime(107/frameRate, -3.578);
layer_9.property('Transform').property('Rotation').setValueAtTime(108/frameRate, -4.52);
layer_9.property('Transform').property('Rotation').setValueAtTime(109/frameRate, -5.527);
layer_9.property('Transform').property('Rotation').setValueAtTime(110/frameRate, -6.584);
layer_9.property('Transform').property('Rotation').setValueAtTime(111/frameRate, -7.677);
layer_9.property('Transform').property('Rotation').setValueAtTime(112/frameRate, -8.791);
layer_9.property('Transform').property('Rotation').setValueAtTime(113/frameRate, -9.913);
layer_9.property('Transform').property('Rotation').setValueAtTime(114/frameRate, -11.027);
layer_9.property('Transform').property('Rotation').setValueAtTime(115/frameRate, -12.12);
layer_9.property('Transform').property('Rotation').setValueAtTime(116/frameRate, -13.177);
layer_9.property('Transform').property('Rotation').setValueAtTime(117/frameRate, -14.184);
layer_9.property('Transform').property('Rotation').setValueAtTime(118/frameRate, -15.126);
layer_9.property('Transform').property('Rotation').setValueAtTime(119/frameRate, -15.989);
layer_9.property('Transform').property('Rotation').setValueAtTime(120/frameRate, -16.758);
layer_9.property('Transform').property('Rotation').setValueAtTime(121/frameRate, -17.42);
layer_9.property('Transform').property('Rotation').setValueAtTime(122/frameRate, -17.96);
layer_9.property('Transform').property('Rotation').setValueAtTime(123/frameRate, -18.364);
layer_9.property('Transform').property('Rotation').setValueAtTime(124/frameRate, -18.616);
layer_9.property('Transform').property('Rotation').setValueAtTime(125/frameRate, -18.704);
layer_9.property('Transform').property('Rotation').setValueAtTime(126/frameRate, -18.616);
layer_9.property('Transform').property('Rotation').setValueAtTime(127/frameRate, -18.364);
layer_9.property('Transform').property('Rotation').setValueAtTime(128/frameRate, -17.96);
layer_9.property('Transform').property('Rotation').setValueAtTime(129/frameRate, -17.42);
layer_9.property('Transform').property('Rotation').setValueAtTime(130/frameRate, -16.758);
layer_9.property('Transform').property('Rotation').setValueAtTime(131/frameRate, -15.989);
layer_9.property('Transform').property('Rotation').setValueAtTime(132/frameRate, -15.126);
layer_9.property('Transform').property('Rotation').setValueAtTime(133/frameRate, -14.184);
layer_9.property('Transform').property('Rotation').setValueAtTime(134/frameRate, -13.177);
layer_9.property('Transform').property('Rotation').setValueAtTime(135/frameRate, -12.12);
layer_9.property('Transform').property('Rotation').setValueAtTime(136/frameRate, -11.027);
layer_9.property('Transform').property('Rotation').setValueAtTime(137/frameRate, -9.913);
layer_9.property('Transform').property('Rotation').setValueAtTime(138/frameRate, -8.791);
layer_9.property('Transform').property('Rotation').setValueAtTime(139/frameRate, -7.677);
layer_9.property('Transform').property('Rotation').setValueAtTime(140/frameRate, -6.584);
layer_9.property('Transform').property('Rotation').setValueAtTime(141/frameRate, -5.527);
layer_9.property('Transform').property('Rotation').setValueAtTime(142/frameRate, -4.52);
layer_9.property('Transform').property('Rotation').setValueAtTime(143/frameRate, -3.578);
layer_9.property('Transform').property('Rotation').setValueAtTime(144/frameRate, -2.715);
layer_9.property('Transform').property('Rotation').setValueAtTime(145/frameRate, -1.945);
layer_9.property('Transform').property('Rotation').setValueAtTime(146/frameRate, -1.283);
layer_9.property('Transform').property('Rotation').setValueAtTime(147/frameRate, -0.743);
layer_9.property('Transform').property('Rotation').setValueAtTime(148/frameRate, -0.34);
layer_9.property('Transform').property('Rotation').setValueAtTime(149/frameRate, -0.087);
layer_9.property('Transform').property('Anchor Point').setValue([148.52, 151.082, 0]);

// Layer 10: Bg
var layer_10 = createShapeLayer(comp, 'Bg');
// Shape Group: Group 1
var layer_10_group_0 = layer_10.property('Contents').addProperty('ADBE Vector Group');
layer_10_group_0.name = 'Group 1';
var layer_10_group_0_path_0 = layer_10_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-539.819, 540], [539.819, 540], [539.819, -540], [-539.819, -540]];
pathShape.inTangents = [[0, 0], [0, 0], [0, 0], [0, 0]];
pathShape.outTangents = [[0, 0], [0, 0], [0, 0], [0, 0]];
pathShape.closed = true;
layer_10_group_0_path_0.property('Path').setValue(pathShape);
var layer_10_group_0_fill_1 = layer_10_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_10_group_0_fill_1.property('Color').setValue([0.156863, 0.431373, 0.552941]);
layer_10_group_0_fill_1.property('Opacity').setValue(100);
var layer_10_group_0_transform = layer_10_group_0.property('Transform');
layer_10_group_0_transform.property('Position').setValue([540.069, 540.25]);
layer_10_group_0_transform.property('Scale').setValue([100, 100]);
layer_10_group_0_transform.property('Rotation').setValue(0);
layer_10_group_0_transform.property('Opacity').setValue(100);
layer_10.inPoint = 0.000000;
layer_10.outPoint = 6.250000;
layer_10.startTime = 0.000000;
layer_10.property('Transform').property('Opacity').setValue(100);
layer_10.property('Transform').property('Position').setValue([540.181, 540, 0]);
layer_10.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_10.property('Transform').property('Rotation').setValue(0);
layer_10.property('Transform').property('Anchor Point').setValue([540.069, 540.25, 0]);


// Set up track matte relationships

// Animation creation complete
alert('Animation "07" created successfully!\n' +
      'Duration: 6.00 seconds\n' +
      'Layers: 10\n' +
      'Assets: 0');

app.endUndoGroup();