// Main - Converted from <PERSON><PERSON> JSON
// Original Bodymovin version: 5.12.1
// Generated by <PERSON><PERSON> to JSX Converter

// Disable undo for performance
app.beginUndoGroup('Create Animation');

// Helper Functions
function setKeyframes(property, keyframes, frameRate) {
    if (!keyframes || !keyframes.length) return;
    
    for (var i = 0; i < keyframes.length; i++) {
        var kf = keyframes[i];
        var time = kf.t / frameRate;
        
        if (i === 0) {
            property.setValueAtTime(time, kf.s);
        } else {
            property.setValueAtTime(time, kf.s);
            
            // Set easing if available
            if (kf.i && kf.o) {
                var keyIndex = property.nearestKeyIndex(time);
                try {
                    property.setTemporalEaseAtKey(keyIndex, kf.i, kf.o);
                } catch(e) {
                    // Ignore easing errors
                }
            }
        }
    }
}

function createSolidLayer(comp, name, color, width, height) {
    return comp.layers.addSolid(color, name, width, height, 1.0);
}

function createShapeLayer(comp, name) {
    var layer = comp.layers.addShape();
    layer.name = name;
    return layer;
}

function createNullLayer(comp, name) {
    var layer = comp.layers.addNull();
    layer.name = name;
    return layer;
}

function createTextLayer(comp, name) {
    var layer = comp.layers.addText(name);
    return layer;
}

function createPrecomp(comp, name, refId) {
    // This would reference a precomp - simplified for now
    return createNullLayer(comp, name + ' (Precomp)');
}

function rgbToAE(r, g, b, a) {
    a = a || 1;
    return [r, g, b, a];
}

function bezierToAE(bezierData) {
    var shape = new Shape();
    if (bezierData.v && bezierData.v.length > 0) {
        shape.vertices = bezierData.v;
        if (bezierData.i) shape.inTangents = bezierData.i;
        if (bezierData.o) shape.outTangents = bezierData.o;
        shape.closed = bezierData.c || false;
    }
    return shape;
}

// === INTELLIGENT ASSET IMPORT WITH CONTEXT-AWARE DIALOGS ===
function importAssetWithDialog(comp, assetId, originalPath, assetType, assetName, dimensions, description) {
    // Try original path first
    if (originalPath && originalPath !== '') {
        var originalFile = new File(originalPath);
        if (originalFile.exists) {
            return importAssetFile(comp, assetId, originalFile, assetName);
        }
    }
    
    // Build hyper-intelligent dialog with detailed context
    var message = '🚨 MISSING ASSET REQUIRED\n';
    message += '════════════════════════════════════════\n\n';
    
    // Asset identification section
    message += '📋 WHAT FILE IS NEEDED:\n';
    message += '• Asset Name: "' + (assetName || assetId) + '"\n';
    message += '• File Type: ' + assetType.toUpperCase() + '\n';
    message += '• Asset ID: ' + assetId + '\n';
    
    if (dimensions && dimensions !== '') {
        message += '• Expected Size: ' + dimensions + ' pixels\n';
    }
    
    message += '• Original Path: ' + (originalPath || 'Not specified') + '\n';
    
    // Context and purpose section
    if (description && description !== '') {
        message += '\n🎯 PURPOSE & CONTEXT:\n';
        message += '• ' + description + '\n';
    }
    
    // Smart guidance based on asset type and name
    message += '\n💡 WHAT TO LOOK FOR:\n';
    if (assetType === 'image') {
        message += '• Look for PNG, JPG, or other image files\n';
        if (assetName && assetName.toLowerCase().indexOf('logo') !== -1) {
            message += '• This should be your company/brand logo\n';
            message += '• Usually a PNG with transparent background\n';
        } else if (assetName && assetName.toLowerCase().indexOf('background') !== -1) {
            message += '• This should be a background image\n';
            message += '• Usually covers the full composition size\n';
        } else if (assetName && assetName.toLowerCase().indexOf('placeholder') !== -1) {
            message += '• This is a placeholder - replace with your image\n';
            message += '• Can be any image that fits your design\n';
        }
    } else if (assetType === 'audio') {
        message += '• Look for MP3, WAV, or other audio files\n';
        if (assetName && assetName.toLowerCase().indexOf('music') !== -1) {
            message += '• This should be background music\n';
            message += '• Usually a longer audio track\n';
        } else {
            message += '• This should be a sound effect or audio clip\n';
            message += '• Usually a shorter audio file\n';
        }
    } else if (assetType === 'video') {
        message += '• Look for MP4, MOV, or other video files\n';
        message += '• Should match the expected dimensions if specified\n';
    }
    
    message += '\n📂 NEXT STEPS:\n';
    message += '• Click OK to open file browser and locate the file\n';
    message += '• Click Cancel to create a placeholder instead\n';
    message += '\n⚠️  If you can\'t find the exact file, choose a similar one or cancel for placeholder.';
    
    var userChoice = confirm(message);
    if (!userChoice) {
        return createPlaceholderLayer(comp, assetId, assetName, assetType, dimensions);
    }
    
    // Show file dialog with smart filters and detailed title
    var fileFilter = getSmartFileFilter(assetType);
    var dialogTitle = 'SELECT ' + assetType.toUpperCase() + ': ' + (assetName || assetId);
    if (description) {
        dialogTitle += ' (' + description + ')';
    }
    
    var selectedFile = File.openDialog(dialogTitle, fileFilter);
    
    if (selectedFile && selectedFile.exists) {
        return importAssetFile(comp, assetId, selectedFile, assetName);
    } else {
        alert('❌ No file selected. Creating placeholder layer instead.');
        return createPlaceholderLayer(comp, assetId, assetName, assetType, dimensions);
    }
}

function importAssetFile(comp, assetId, file, assetName) {
    try {
        var importOptions = new ImportOptions(file);
        var footage = app.project.importFile(importOptions);
        var layer = comp.layers.add(footage);
        layer.name = assetName || assetId;
        return layer;
    } catch (e) {
        alert('Error importing file: ' + file.fsName + '\nError: ' + e.toString() + '\nCreating placeholder layer.');
        return createNullLayer(comp, (assetName || assetId) + ' (Import Error)');
    }
}

function createPlaceholderLayer(comp, assetId, assetName, assetType, dimensions) {
    var placeholderName = (assetName || assetId) + ' [MISSING ' + assetType.toUpperCase() + ']';
    
    // Create different placeholder types based on asset type
    if (assetType === 'image') {
        // Create a colored solid as image placeholder
        var width = 500, height = 500;
        if (dimensions) {
            var parts = dimensions.split('x');
            if (parts.length === 2) {
                width = parseInt(parts[0]) || 500;
                height = parseInt(parts[1]) || 500;
            }
        }
        var placeholder = comp.layers.addSolid([0.8, 0.2, 0.2], placeholderName, width, height, 1.0);
        return placeholder;
    } else if (assetType === 'audio') {
        // Create null layer for audio placeholder
        var placeholder = comp.layers.addNull();
        placeholder.name = placeholderName;
        return placeholder;
    } else if (assetType === 'video') {
        // Create solid for video placeholder
        var placeholder = comp.layers.addSolid([0.2, 0.2, 0.8], placeholderName, 1920, 1080, 1.0);
        return placeholder;
    } else {
        // Generic null layer placeholder
        var placeholder = comp.layers.addNull();
        placeholder.name = placeholderName;
        return placeholder;
    }
}

function getSmartFileFilter(assetType) {
    switch (assetType.toLowerCase()) {
        case 'image':
            return 'Image Files:*.png;*.jpg;*.jpeg;*.gif;*.bmp;*.tiff;*.tga;*.psd;*.ai;*.eps';
        case 'video':
            return 'Video Files:*.mp4;*.mov;*.avi;*.wmv;*.flv;*.webm;*.mkv;*.m4v;*.3gp';
        case 'audio':
            return 'Audio Files:*.mp3;*.wav;*.aac;*.ogg;*.flac;*.m4a;*.wma;*.aiff';
        default:
            return 'All Files:*.*';
    }
}

function getFileTypesForAsset(assetType) {
    switch (assetType.toLowerCase()) {
        case 'image':
            return 'Images:*.png;*.jpg;*.jpeg;*.gif;*.bmp;*.tiff;*.psd';
        case 'video':
            return 'Videos:*.mp4;*.mov;*.avi;*.wmv;*.flv;*.webm;*.mkv';
        case 'audio':
            return 'Audio:*.mp3;*.wav;*.aac;*.ogg;*.flac;*.m4a';
        default:
            return 'All Files:*.*';
    }
}

// Create main composition: Main
var comp = app.project.items.addComp('Main', 1920, 1080, 1.0, 10.000, 30);
var frameRate = 30;

// Asset References and Precomps
// Creating precomps in dependency order
// Create precomp: Text
var comp_1 = app.project.items.addComp('Text', 1920, 1080, 1.0, 10.000, 30);

// Processing 1 layers in Text
// Preserving original JSON layer order for correct visual stacking
// 3D TEXT (index 1)
// Layer 1: 3D TEXT
var layer_1 = createTextLayer(comp_1, '3D TEXT');
var textDoc = layer_1.property('Source Text');
var textValue = textDoc.value;
textValue.text = '3D TEXT';
textValue.font = 'BoomerTantrum';
textValue.fontSize = 280;
textValue.fillColor = [1.000000, 1.000000, 1.000000];
textValue.justification = ParagraphJustification.CENTER_JUSTIFY;
textDoc.setValue(textValue);
layer_1.inPoint = 0.000000;
layer_1.outPoint = 12.500000;
layer_1.startTime = 0.000000;
layer_1.property('Transform').property('Opacity').setValue(100);
layer_1.property('Transform').property('Position').setValue([960, 540, 0]);
layer_1.property('Transform').property('Scale').setValue([139, 139, 100]);
layer_1.property('Transform').property('Rotation').setValue(0);
layer_1.property('Transform').property('Anchor Point').setValue([-13.672, -109.375, 0]);
layer_1.blendingMode = BlendingMode.NORMAL;


// Create precomp: Top
var comp_0 = app.project.items.addComp('Top', 1920, 1080, 1.0, 10.000, 30);

// Processing 6 layers in Top
// Preserving original JSON layer order for correct visual stacking
// Text (index 1)
// Medium Gray-Turquoise Solid 2 (index 2)
// Text (index 3)
// Text (index 4)
// Text (index 5)
// Text (index 6)
// Layer 1: Text
var layer_1 = comp_0.layers.add(comp_1);
layer_1.name = 'Text';
layer_1.inPoint = 0.000000;
layer_1.outPoint = 12.500000;
layer_1.startTime = 0.000000;
layer_1.property('Transform').property('Opacity').setValue(100);
layer_1.property('Transform').property('Position').setValue([960, 540, 0]);
layer_1.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_1.property('Transform').property('Rotation').setValue(0);
layer_1.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
// Effect: Fill (type 21, match: ADBE Fill)
var layer_1_fill_0 = layer_1.property('Effects').addProperty('ADBE Fill');
layer_1_fill_0.property('Fill Mask').setValue(0);
layer_1_fill_0.property('All Masks').setValue(0);
layer_1_fill_0.property('Color').setValue([0.99609375, 0.992156982422, 0.87451171875, 1]);
layer_1_fill_0.property('Invert').setValue(0);
layer_1_fill_0.property('Horizontal Feather').setValue(0);
layer_1_fill_0.property('Vertical Feather').setValue(0);
layer_1_fill_0.property('Opacity').setValue(1);
layer_1.blendingMode = BlendingMode.NORMAL;
// Layer Style: Inner Shadow (type 2)
// Strategy B: Create inner shadow using Drop Shadow effect (works with native AE layers)
var layer_1_innerShadow = layer_1.property('Effects').addProperty('ADBE Drop Shadow');
layer_1_innerShadow.property('Shadow Color').setValue([0.549450, 0.690196, 0.338331]);
layer_1_innerShadow.property('Opacity').setValue(49.5);
layer_1_innerShadow.property('Direction').setValue(30);
layer_1_innerShadow.property('Distance').setValue(10.5);
layer_1_innerShadow.property('Softness').setValue(5); // Default softness

// Layer 2: Medium Gray-Turquoise Solid 2
var layer_2 = createSolidLayer(comp_0, 'Medium Gray-Turquoise Solid 2', [0.458824, 0.674510, 0.615686], 1920, 1080);
layer_2.inPoint = 0.000000;
layer_2.outPoint = 12.500000;
layer_2.startTime = 0.000000;
layer_2.property('Transform').property('Opacity').setValue(100);
layer_2.property('Transform').property('Position').setValue([960, 540, 0]);
layer_2.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_2.property('Transform').property('Rotation').setValue(10.3);
layer_2.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
// Effect: Grid (type 5, match: ADBE Grid)
var layer_2_grid_0 = layer_2.property('Effects').addProperty('ADBE Grid');
layer_2_grid_0.property('Anchor').setValue([960, 540]);
layer_2_grid_0.property('Size From').setValue(2);
layer_2_grid_0.property('Corner').setValue([1152, 648]);
layer_2_grid_0.property('Width').setValue(25);
layer_2_grid_0.property('Height').setValue(24);
layer_2_grid_0.property('Border').setValue(1.5);
// Skipping NO_VALUE property: Feather
// WARNING: grid Width value 0 clamped to minimum 1 (Grid dimensions must be > 0)
layer_2_grid_0.property('Width').setValue(1);
// WARNING: grid Height value 0 clamped to minimum 1 (Grid dimensions must be > 0)
layer_2_grid_0.property('Height').setValue(1);
// Skipping NO_VALUE property: Height
layer_2_grid_0.property('Invert Grid').setValue(0);
layer_2_grid_0.property('Color').setValue([0, 0, 0, 1]);
layer_2_grid_0.property('Opacity').setValue(100);
layer_2_grid_0.property('Blending Mode').setValue(1);
layer_2.blendingMode = BlendingMode.NORMAL;
// Track matte will be set up later
// Type: TrackMatteType.ALPHA

// Layer 3: Text
var layer_3 = comp_0.layers.add(comp_1);
layer_3.name = 'Text';
layer_3.inPoint = 0.000000;
layer_3.outPoint = 12.500000;
layer_3.startTime = 0.000000;
layer_3.property('Transform').property('Opacity').setValue(100);
layer_3.property('Transform').property('Position').setValue([960, 540, 0]);
layer_3.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_3.property('Transform').property('Rotation').setValue(0);
layer_3.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
// Effect: Fill (type 21, match: ADBE Fill)
var layer_3_fill_0 = layer_3.property('Effects').addProperty('ADBE Fill');
layer_3_fill_0.property('Fill Mask').setValue(0);
layer_3_fill_0.property('All Masks').setValue(0);
layer_3_fill_0.property('Color').setValue([0.99609375, 0.992156982422, 0.87451171875, 1]);
layer_3_fill_0.property('Invert').setValue(0);
layer_3_fill_0.property('Horizontal Feather').setValue(0);
layer_3_fill_0.property('Vertical Feather').setValue(0);
layer_3_fill_0.property('Opacity').setValue(1);
layer_3.blendingMode = BlendingMode.NORMAL;
// Layer Style: Inner Shadow (type 2)
// Strategy B: Create inner shadow using Drop Shadow effect (works with native AE layers)
var layer_3_innerShadow = layer_3.property('Effects').addProperty('ADBE Drop Shadow');
layer_3_innerShadow.property('Shadow Color').setValue([0.549450, 0.690196, 0.338331]);
layer_3_innerShadow.property('Opacity').setValue(49.5);
layer_3_innerShadow.property('Direction').setValue(30);
layer_3_innerShadow.property('Distance').setValue(10.5);
layer_3_innerShadow.property('Softness').setValue(5); // Default softness

// Layer 4: Text
var layer_4 = comp_0.layers.add(comp_1);
layer_4.name = 'Text';
layer_4.inPoint = 0.000000;
layer_4.outPoint = 12.500000;
layer_4.startTime = 0.000000;
layer_4.property('Transform').property('Opacity').setValue(100);
layer_4.property('Transform').property('Position').setValue([960, 540, 0]);
layer_4.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_4.property('Transform').property('Rotation').setValue(0);
layer_4.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
// Effect: Fill (type 21, match: ADBE Fill)
var layer_4_fill_0 = layer_4.property('Effects').addProperty('ADBE Fill');
layer_4_fill_0.property('Fill Mask').setValue(0);
layer_4_fill_0.property('All Masks').setValue(0);
layer_4_fill_0.property('Color').setValue([0.99609375, 0.992156982422, 0.87451171875, 1]);
layer_4_fill_0.property('Invert').setValue(0);
layer_4_fill_0.property('Horizontal Feather').setValue(0);
layer_4_fill_0.property('Vertical Feather').setValue(0);
layer_4_fill_0.property('Opacity').setValue(1);
layer_4.blendingMode = BlendingMode.NORMAL;
// Layer Style: Stroke (type 0)
// Strategy B: Create stroke using effect-stack replacement (works with native AE layers)
// Step 1: Duplicate the layer for stroke effect
var layer_4_strokeDup = layer_4.duplicate();
layer_4_strokeDup.moveAfter(layer_4);
layer_4_strokeDup.name = layer_4.name + '_STROKE';

// Step 2: Add Minimax effect to grow the edge
var layer_4_minimax = layer_4_strokeDup.property('Effects').addProperty('ADBE Minimax');
layer_4_minimax.property('Operation').setValue(1); // Max operation
layer_4_minimax.property('Radius').setValue(3);

// Step 3: Add Fill effect for stroke color
var layer_4_strokeFill = layer_4_strokeDup.property('Effects').addProperty('ADBE Fill');
layer_4_strokeFill.property('Color').setValue([0.000000, 0.000000, 0.000000]);

// Step 4: Set blend mode and ensure original layer is on top
layer_4_strokeDup.blendingMode = BlendingMode.NORMAL;
layer_4.moveToBeginning(); // Ensure original text is on top of stroke

// Layer 5: Text
var layer_5 = comp_0.layers.add(comp_1);
layer_5.name = 'Text';
layer_5.inPoint = 0.000000;
layer_5.outPoint = 12.500000;
layer_5.startTime = 0.000000;
layer_5.property('Transform').property('Opacity').setValue(100);
layer_5.property('Transform').property('Position').setValue([960, 540, 0]);
layer_5.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_5.property('Transform').property('Rotation').setValue(0);
layer_5.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
// Effect: Fill (type 21, match: ADBE Fill)
var layer_5_fill_0 = layer_5.property('Effects').addProperty('ADBE Fill');
layer_5_fill_0.property('Fill Mask').setValue(0);
layer_5_fill_0.property('All Masks').setValue(0);
layer_5_fill_0.property('Color').setValue([0.99609375, 0.992156982422, 0.87451171875, 1]);
layer_5_fill_0.property('Invert').setValue(0);
layer_5_fill_0.property('Horizontal Feather').setValue(0);
layer_5_fill_0.property('Vertical Feather').setValue(0);
layer_5_fill_0.property('Opacity').setValue(1);
layer_5.blendingMode = BlendingMode.NORMAL;
// Layer Style: Stroke (type 0)
// Strategy B: Create stroke using effect-stack replacement (works with native AE layers)
// Step 1: Duplicate the layer for stroke effect
var layer_5_strokeDup = layer_5.duplicate();
layer_5_strokeDup.moveAfter(layer_5);
layer_5_strokeDup.name = layer_5.name + '_STROKE';

// Step 2: Add Minimax effect to grow the edge
var layer_5_minimax = layer_5_strokeDup.property('Effects').addProperty('ADBE Minimax');
layer_5_minimax.property('Operation').setValue(1); // Max operation
layer_5_minimax.property('Radius').setValue(9.5);

// Step 3: Add Fill effect for stroke color
var layer_5_strokeFill = layer_5_strokeDup.property('Effects').addProperty('ADBE Fill');
layer_5_strokeFill.property('Color').setValue([0.749020, 0.937255, 0.466667]);

// Step 4: Set blend mode and ensure original layer is on top
layer_5_strokeDup.blendingMode = BlendingMode.NORMAL;
layer_5.moveToBeginning(); // Ensure original text is on top of stroke

// Layer 6: Text
var layer_6 = comp_0.layers.add(comp_1);
layer_6.name = 'Text';
layer_6.inPoint = 0.000000;
layer_6.outPoint = 12.500000;
layer_6.startTime = 0.000000;
layer_6.property('Transform').property('Opacity').setValue(100);
layer_6.property('Transform').property('Position').setValue([960, 540, 0]);
layer_6.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_6.property('Transform').property('Rotation').setValue(0);
layer_6.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
// Effect: Fill (type 21, match: ADBE Fill)
var layer_6_fill_0 = layer_6.property('Effects').addProperty('ADBE Fill');
layer_6_fill_0.property('Fill Mask').setValue(0);
layer_6_fill_0.property('All Masks').setValue(0);
layer_6_fill_0.property('Color').setValue([0.99609375, 0.992156982422, 0.87451171875, 1]);
layer_6_fill_0.property('Invert').setValue(0);
layer_6_fill_0.property('Horizontal Feather').setValue(0);
layer_6_fill_0.property('Vertical Feather').setValue(0);
layer_6_fill_0.property('Opacity').setValue(1);
layer_6.blendingMode = BlendingMode.NORMAL;
// Layer Style: Stroke (type 0)
// Strategy B: Create stroke using effect-stack replacement (works with native AE layers)
// Step 1: Duplicate the layer for stroke effect
var layer_6_strokeDup = layer_6.duplicate();
layer_6_strokeDup.moveAfter(layer_6);
layer_6_strokeDup.name = layer_6.name + '_STROKE';

// Step 2: Add Minimax effect to grow the edge
var layer_6_minimax = layer_6_strokeDup.property('Effects').addProperty('ADBE Minimax');
layer_6_minimax.property('Operation').setValue(1); // Max operation
layer_6_minimax.property('Radius').setValue(12.77);

// Step 3: Add Fill effect for stroke color
var layer_6_strokeFill = layer_6_strokeDup.property('Effects').addProperty('ADBE Fill');
layer_6_strokeFill.property('Color').setValue([0.000000, 0.000000, 0.000000]);

// Step 4: Set blend mode and ensure original layer is on top
layer_6_strokeDup.blendingMode = BlendingMode.NORMAL;
layer_6.moveToBeginning(); // Ensure original text is on top of stroke


// Create precomp: Mid
var comp_2 = app.project.items.addComp('Mid', 1920, 1080, 1.0, 10.000, 30);

// Processing 3 layers in Mid
// Preserving original JSON layer order for correct visual stacking
// Text (index 1)
// Medium Gray-Turquoise Solid 3 (index 2)
// Text (index 3)
// Layer 1: Text
var layer_1 = comp_2.layers.add(comp_1);
layer_1.name = 'Text';
layer_1.inPoint = 0.000000;
layer_1.outPoint = 12.500000;
layer_1.startTime = 0.000000;
layer_1.property('Transform').property('Opacity').setValue(100);
layer_1.property('Transform').property('Position').setValue([960, 540, 0]);
layer_1.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_1.property('Transform').property('Rotation').setValue(0);
layer_1.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
// Effect: Fill (type 21, match: ADBE Fill)
var layer_1_fill_0 = layer_1.property('Effects').addProperty('ADBE Fill');
layer_1_fill_0.property('Fill Mask').setValue(0);
layer_1_fill_0.property('All Masks').setValue(0);
layer_1_fill_0.property('Color').setValue([0.207843154669, 0.368627458811, 0.541176497936, 1]);
layer_1_fill_0.property('Invert').setValue(0);
layer_1_fill_0.property('Horizontal Feather').setValue(0);
layer_1_fill_0.property('Vertical Feather').setValue(0);
layer_1_fill_0.property('Opacity').setValue(1);
// Effect: CC Radial Fast Blur (type 5, match: CC Radial Fast Blur)
var layer_1_ccRadialBlur_1 = layer_1.property('Effects').addProperty('CC Radial Fast Blur');
layer_1_ccRadialBlur_1.property('Center').setValue([1836, 4]);
layer_1_ccRadialBlur_1.property('Amount').setValue(12.5);
layer_1_ccRadialBlur_1.property('Zoom').setValue(1);
layer_1.blendingMode = BlendingMode.NORMAL;
// Layer Style: Stroke (type 0)
// Strategy B: Create stroke using effect-stack replacement (works with native AE layers)
// Step 1: Duplicate the layer for stroke effect
var layer_1_strokeDup = layer_1.duplicate();
layer_1_strokeDup.moveAfter(layer_1);
layer_1_strokeDup.name = layer_1.name + '_STROKE';

// Step 2: Add Minimax effect to grow the edge
var layer_1_minimax = layer_1_strokeDup.property('Effects').addProperty('ADBE Minimax');
layer_1_minimax.property('Operation').setValue(1); // Max operation
layer_1_minimax.property('Radius').setValue(12.77);

// Step 3: Add Fill effect for stroke color
var layer_1_strokeFill = layer_1_strokeDup.property('Effects').addProperty('ADBE Fill');
layer_1_strokeFill.property('Color').setValue([0.207843, 0.368627, 0.541176]);

// Step 4: Set blend mode and ensure original layer is on top
layer_1_strokeDup.blendingMode = BlendingMode.NORMAL;
layer_1.moveToBeginning(); // Ensure original text is on top of stroke

// Layer 2: Medium Gray-Turquoise Solid 3
var layer_2 = createSolidLayer(comp_2, 'Medium Gray-Turquoise Solid 3', [0.458824, 0.674510, 0.615686], 1920, 1080);
layer_2.inPoint = 0.000000;
layer_2.outPoint = 12.500000;
layer_2.startTime = 0.000000;
layer_2.property('Transform').property('Opacity').setValue(100);
layer_2.property('Transform').property('Position').setValue([960, 540, 0]);
layer_2.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_2.property('Transform').property('Rotation').setValue(0);
layer_2.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
// Effect: Grid (type 5, match: ADBE Grid)
var layer_2_grid_0 = layer_2.property('Effects').addProperty('ADBE Grid');
layer_2_grid_0.property('Anchor').setValue([1798, 540]);
layer_2_grid_0.property('Size From').setValue(3);
layer_2_grid_0.property('Corner').setValue([1152, 648]);
layer_2_grid_0.property('Width').setValue(1581);
layer_2_grid_0.property('Height').setValue(18.4);
layer_2_grid_0.property('Border').setValue(2.3);
// Skipping NO_VALUE property: Feather
// WARNING: grid Width value 0 clamped to minimum 1 (Grid dimensions must be > 0)
layer_2_grid_0.property('Width').setValue(1);
// WARNING: grid Height value 0 clamped to minimum 1 (Grid dimensions must be > 0)
layer_2_grid_0.property('Height').setValue(1);
// Skipping NO_VALUE property: Height
layer_2_grid_0.property('Invert Grid').setValue(0);
layer_2_grid_0.property('Color').setValue([0, 0, 0, 1]);
layer_2_grid_0.property('Opacity').setValue(100);
layer_2_grid_0.property('Blending Mode').setValue(1);
// Effect: Turbulent Displace (type 5, match: ADBE Turbulent Displace)
var layer_2_turbulentDisplace_1 = layer_2.property('Effects').addProperty('ADBE Turbulent Displace');
layer_2_turbulentDisplace_1.property('Displacement').setValue(1);
layer_2_turbulentDisplace_1.property('Amount').setValue(85);
layer_2_turbulentDisplace_1.property('Size').setValue(153);
layer_2_turbulentDisplace_1.property('Offset (Turbulence)').setValue([960, 540]);
layer_2_turbulentDisplace_1.property('Complexity').setValue(1);
layer_2_turbulentDisplace_1.property('Evolution').setValue(0);
// Skipping NO_VALUE property: Evolution Options
layer_2_turbulentDisplace_1.property('Cycle Evolution').setValue(0);
layer_2_turbulentDisplace_1.property('Cycle (in Revolutions)').setValue(1);
// WARNING: Random Seed value 0 clamped to minimum 1
layer_2_turbulentDisplace_1.property('Random Seed').setValue(1);
// Skipping NO_VALUE property: Random Seed
layer_2_turbulentDisplace_1.property('Pinning').setValue(3);
// WARNING: Resize Layer value 0 clamped to minimum 1
layer_2_turbulentDisplace_1.property('Resize Layer').setValue(1);
layer_2_turbulentDisplace_1.property('Antialiasing for Best Quality').setValue(1);
layer_2.blendingMode = BlendingMode.NORMAL;
// Track matte will be set up later
// Type: TrackMatteType.ALPHA

// Layer 3: Text
var layer_3 = comp_2.layers.add(comp_1);
layer_3.name = 'Text';
layer_3.inPoint = 0.000000;
layer_3.outPoint = 12.500000;
layer_3.startTime = 0.000000;
layer_3.property('Transform').property('Opacity').setValue(100);
layer_3.property('Transform').property('Position').setValue([960, 540, 0]);
layer_3.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_3.property('Transform').property('Rotation').setValue(0);
layer_3.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
// Effect: Fill (type 21, match: ADBE Fill)
var layer_3_fill_0 = layer_3.property('Effects').addProperty('ADBE Fill');
layer_3_fill_0.property('Fill Mask').setValue(0);
layer_3_fill_0.property('All Masks').setValue(0);
layer_3_fill_0.property('Color').setValue([0.207843154669, 0.368627458811, 0.541176497936, 1]);
layer_3_fill_0.property('Invert').setValue(0);
layer_3_fill_0.property('Horizontal Feather').setValue(0);
layer_3_fill_0.property('Vertical Feather').setValue(0);
layer_3_fill_0.property('Opacity').setValue(1);
// Effect: CC Radial Fast Blur (type 5, match: CC Radial Fast Blur)
var layer_3_ccRadialBlur_1 = layer_3.property('Effects').addProperty('CC Radial Fast Blur');
layer_3_ccRadialBlur_1.property('Center').setValue([1836, 4]);
layer_3_ccRadialBlur_1.property('Amount').setValue(12.5);
layer_3_ccRadialBlur_1.property('Zoom').setValue(1);
layer_3.blendingMode = BlendingMode.NORMAL;
// Layer Style: Stroke (type 0)
// Strategy B: Create stroke using effect-stack replacement (works with native AE layers)
// Step 1: Duplicate the layer for stroke effect
var layer_3_strokeDup = layer_3.duplicate();
layer_3_strokeDup.moveAfter(layer_3);
layer_3_strokeDup.name = layer_3.name + '_STROKE';

// Step 2: Add Minimax effect to grow the edge
var layer_3_minimax = layer_3_strokeDup.property('Effects').addProperty('ADBE Minimax');
layer_3_minimax.property('Operation').setValue(1); // Max operation
layer_3_minimax.property('Radius').setValue(12.77);

// Step 3: Add Fill effect for stroke color
var layer_3_strokeFill = layer_3_strokeDup.property('Effects').addProperty('ADBE Fill');
layer_3_strokeFill.property('Color').setValue([0.207843, 0.368627, 0.541176]);

// Step 4: Set blend mode and ensure original layer is on top
layer_3_strokeDup.blendingMode = BlendingMode.NORMAL;
layer_3.moveToBeginning(); // Ensure original text is on top of stroke


// Create precomp: Bottom
var comp_3 = app.project.items.addComp('Bottom', 1920, 1080, 1.0, 10.000, 30);

// Processing 1 layers in Bottom
// Preserving original JSON layer order for correct visual stacking
// Text (index 1)
// Layer 1: Text
var layer_1 = comp_3.layers.add(comp_1);
layer_1.name = 'Text';
layer_1.inPoint = 0.000000;
layer_1.outPoint = 12.500000;
layer_1.startTime = 0.000000;
layer_1.property('Transform').property('Opacity').setValue(100);
layer_1.property('Transform').property('Position').setValue([960, 540, 0]);
layer_1.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_1.property('Transform').property('Rotation').setValue(0);
layer_1.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
// Effect: Fill (type 21, match: ADBE Fill)
var layer_1_fill_0 = layer_1.property('Effects').addProperty('ADBE Fill');
layer_1_fill_0.property('Fill Mask').setValue(0);
layer_1_fill_0.property('All Masks').setValue(0);
layer_1_fill_0.property('Color').setValue([0.980377197266, 0.494110107422, 0.101959228516, 1]);
layer_1_fill_0.property('Invert').setValue(0);
layer_1_fill_0.property('Horizontal Feather').setValue(0);
layer_1_fill_0.property('Vertical Feather').setValue(0);
layer_1_fill_0.property('Opacity').setValue(1);
// Effect: CC Radial Fast Blur (type 5, match: CC Radial Fast Blur)
var layer_1_ccRadialBlur_1 = layer_1.property('Effects').addProperty('CC Radial Fast Blur');
layer_1_ccRadialBlur_1.property('Center').setValue([1836, 4]);
layer_1_ccRadialBlur_1.property('Amount').setValue(39.5);
layer_1_ccRadialBlur_1.property('Zoom').setValue(1);
layer_1.blendingMode = BlendingMode.NORMAL;
// Layer Style: Stroke (type 0)
// Strategy B: Create stroke using effect-stack replacement (works with native AE layers)
// Step 1: Duplicate the layer for stroke effect
var layer_1_strokeDup = layer_1.duplicate();
layer_1_strokeDup.moveAfter(layer_1);
layer_1_strokeDup.name = layer_1.name + '_STROKE';

// Step 2: Add Minimax effect to grow the edge
var layer_1_minimax = layer_1_strokeDup.property('Effects').addProperty('ADBE Minimax');
layer_1_minimax.property('Operation').setValue(1); // Max operation
layer_1_minimax.property('Radius').setValue(12.77);

// Step 3: Add Fill effect for stroke color
var layer_1_strokeFill = layer_1_strokeDup.property('Effects').addProperty('ADBE Fill');
layer_1_strokeFill.property('Color').setValue([0.980377, 0.494110, 0.101959]);

// Step 4: Set blend mode and ensure original layer is on top
layer_1_strokeDup.blendingMode = BlendingMode.NORMAL;
layer_1.moveToBeginning(); // Ensure original text is on top of stroke



// Font References
// Font: BoomerTantrum (BoomerTantrum)

// Creating 5 layers
// Preserving original JSON layer order for correct visual stacking
// Top (index 1)
// Mid (index 2)
// Bottom (index 3)
// Bottom (index 4)
// Medium Gray-Turquoise Solid 1 (index 5)
// Layer 1: Top
var layer_1 = comp.layers.add(comp_0);
layer_1.name = 'Top';
layer_1.inPoint = 0.000000;
layer_1.outPoint = 12.500000;
layer_1.startTime = 0.000000;
layer_1.property('Transform').property('Opacity').setValue(100);
layer_1.property('Transform').property('Position').setValue([960, 540, 0]);
layer_1.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_1.property('Transform').property('Rotation').setValue(0);
layer_1.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
layer_1.blendingMode = BlendingMode.NORMAL;

// Layer 2: Mid
var layer_2 = comp.layers.add(comp_2);
layer_2.name = 'Mid';
layer_2.inPoint = 0.000000;
layer_2.outPoint = 12.500000;
layer_2.startTime = 0.000000;
layer_2.property('Transform').property('Opacity').setValue(100);
layer_2.property('Transform').property('Position').setValue([960, 540, 0]);
layer_2.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_2.property('Transform').property('Rotation').setValue(0);
layer_2.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
layer_2.blendingMode = BlendingMode.NORMAL;
// Layer Style: Stroke (type 0)
// Strategy B: Create stroke using effect-stack replacement (works with native AE layers)
// Step 1: Duplicate the layer for stroke effect
var layer_2_strokeDup = layer_2.duplicate();
layer_2_strokeDup.moveAfter(layer_2);
layer_2_strokeDup.name = layer_2.name + '_STROKE';

// Step 2: Add Minimax effect to grow the edge
var layer_2_minimax = layer_2_strokeDup.property('Effects').addProperty('ADBE Minimax');
layer_2_minimax.property('Operation').setValue(1); // Max operation
layer_2_minimax.property('Radius').setValue(3.64);

// Step 3: Add Fill effect for stroke color
var layer_2_strokeFill = layer_2_strokeDup.property('Effects').addProperty('ADBE Fill');
layer_2_strokeFill.property('Color').setValue([0.000000, 0.000000, 0.000000]);

// Step 4: Set blend mode and ensure original layer is on top
layer_2_strokeDup.blendingMode = BlendingMode.NORMAL;
layer_2.moveToBeginning(); // Ensure original text is on top of stroke

// Layer 3: Bottom
var layer_3 = comp.layers.add(comp_3);
layer_3.name = 'Bottom';
layer_3.inPoint = 0.000000;
layer_3.outPoint = 12.500000;
layer_3.startTime = 0.000000;
layer_3.property('Transform').property('Opacity').setValue(100);
layer_3.property('Transform').property('Position').setValue([960, 540, 0]);
layer_3.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_3.property('Transform').property('Rotation').setValue(0);
layer_3.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
layer_3.blendingMode = BlendingMode.NORMAL;
// Layer Style: Stroke (type 0)
// Strategy B: Create stroke using effect-stack replacement (works with native AE layers)
// Step 1: Duplicate the layer for stroke effect
var layer_3_strokeDup = layer_3.duplicate();
layer_3_strokeDup.moveAfter(layer_3);
layer_3_strokeDup.name = layer_3.name + '_STROKE';

// Step 2: Add Minimax effect to grow the edge
var layer_3_minimax = layer_3_strokeDup.property('Effects').addProperty('ADBE Minimax');
layer_3_minimax.property('Operation').setValue(1); // Max operation
layer_3_minimax.property('Radius').setValue(3.6);

// Step 3: Add Fill effect for stroke color
var layer_3_strokeFill = layer_3_strokeDup.property('Effects').addProperty('ADBE Fill');
layer_3_strokeFill.property('Color').setValue([0.000000, 0.000000, 0.000000]);

// Step 4: Set blend mode and ensure original layer is on top
layer_3_strokeDup.blendingMode = BlendingMode.NORMAL;
layer_3.moveToBeginning(); // Ensure original text is on top of stroke

// Layer 4: Bottom
var layer_4 = comp.layers.add(comp_3);
layer_4.name = 'Bottom';
layer_4.inPoint = 0.000000;
layer_4.outPoint = 12.500000;
layer_4.startTime = 0.000000;
layer_4.property('Transform').property('Opacity').setValue(100);
layer_4.property('Transform').property('Position').setValue([960, 540, 0]);
layer_4.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_4.property('Transform').property('Rotation').setValue(0);
layer_4.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
// Effect: Drop Shadow (type 25, match: ADBE Drop Shadow)
var layer_4_dropShadow_0 = layer_4.property('Effects').addProperty('ADBE Drop Shadow');
layer_4_dropShadow_0.property('Shadow Color').setValue([0, 0, 0, 1]);
// WARNING: Opacity value 160.2 clamped to maximum 100
layer_4_dropShadow_0.property('Opacity').setValue(100);
layer_4_dropShadow_0.property('Direction').setValue(225);
layer_4_dropShadow_0.property('Distance').setValue(36.4);
layer_4_dropShadow_0.property('Softness').setValue(59);
layer_4_dropShadow_0.property('Shadow Only').setValue(0);
layer_4.blendingMode = BlendingMode.NORMAL;

// Layer 5: Medium Gray-Turquoise Solid 1
var layer_5 = createSolidLayer(comp, 'Medium Gray-Turquoise Solid 1', [0.458824, 0.674510, 0.615686], 1920, 1080);
layer_5.inPoint = 0.000000;
layer_5.outPoint = 12.500000;
layer_5.startTime = 0.000000;
layer_5.property('Transform').property('Opacity').setValue(100);
layer_5.property('Transform').property('Position').setValue([960, 540, 0]);
layer_5.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_5.property('Transform').property('Rotation').setValue(0);
layer_5.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
// Effect: Fill (type 21, match: ADBE Fill)
var layer_5_fill_0 = layer_5.property('Effects').addProperty('ADBE Fill');
layer_5_fill_0.property('Fill Mask').setValue(0);
layer_5_fill_0.property('All Masks').setValue(0);
layer_5_fill_0.property('Color').setValue([0.882354736328, 0.301971435547, 0.168640136719, 1]);
layer_5_fill_0.property('Invert').setValue(0);
layer_5_fill_0.property('Horizontal Feather').setValue(0);
layer_5_fill_0.property('Vertical Feather').setValue(0);
layer_5_fill_0.property('Opacity').setValue(1);
layer_5.blendingMode = BlendingMode.NORMAL;


// Set up track matte relationships
// Set up track matte in comp_0: layer_2 uses layer_1 as TrackMatteType.ALPHA
try {
    // Get layers from the correct composition
    var matteLayer = comp_0.layer(1);
    var targetLayer = comp_0.layer(2);
    // Ensure track matte layer is directly above the layer that uses it
    matteLayer.moveBefore(targetLayer);
    // Set the track matte type
    targetLayer.trackMatteType = TrackMatteType.ALPHA;
    // IMPORTANT: Hide the matte layer (turn off visibility)
    matteLayer.enabled = false;
} catch(e) {
    // Track matte setup failed for layer_2 in comp_0
    alert('Track matte setup failed for layer_2 in comp_0: ' + e.toString());
}
// Set up track matte in comp_2: layer_2 uses layer_1 as TrackMatteType.ALPHA
try {
    // Get layers from the correct composition
    var matteLayer = comp_2.layer(1);
    var targetLayer = comp_2.layer(2);
    // Ensure track matte layer is directly above the layer that uses it
    matteLayer.moveBefore(targetLayer);
    // Set the track matte type
    targetLayer.trackMatteType = TrackMatteType.ALPHA;
    // IMPORTANT: Hide the matte layer (turn off visibility)
    matteLayer.enabled = false;
} catch(e) {
    // Track matte setup failed for layer_2 in comp_2
    alert('Track matte setup failed for layer_2 in comp_2: ' + e.toString());
}


// Animation creation complete
alert('Animation "Main" created successfully!\n' +
      'Duration: 10.00 seconds\n' +
      'Layers: 5\n' +
      'Assets: 4');

app.endUndoGroup();