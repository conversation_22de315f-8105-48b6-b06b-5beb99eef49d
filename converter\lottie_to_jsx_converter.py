#!/usr/bin/env python3
"""
INTELLIGENT LOTTIE TO JSX CONVERTER with ASSET DEPENDENCY MANAGEMENT
Converts Bodymovin/Lottie JSON files to complete After Effects JSX scripts
with smart layer ordering and interactive asset resolution
"""

import json
import math
import os
import re
import sys
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from asset_dependency_manager import AssetDependencyManager

# Velocity Spike System - Dynamic structures for bezier-to-velocity conversion
@dataclass
class Point2D:
    """2D point for mathematical calculations"""
    x: float
    y: float

@dataclass
class VelocitySpike:
    """Represents a velocity spike in the animation"""
    time_position: float    # 0-1, when in the segment the spike occurs
    intensity: float        # 0-1, how intense the spike is
    duration: float         # 0-1, how long the spike lasts
    spike_type: str         # 'acceleration', 'deceleration', 'instant'

@dataclass
class VelocityPattern:
    """Analysis of velocity pattern from bezier curve"""
    spikes: List[VelocitySpike]  # List of velocity spikes
    rest_periods: List[float]    # Periods of low/zero velocity
    overall_intensity: float     # 0-1, overall animation intensity
    pattern_type: str           # 'single_spike', 'double_spike', 'continuous', 'bounce'

@dataclass
class SpikeKeyframe:
    """Keyframe specifically designed for velocity spikes"""
    time_offset: float      # Relative time from start (0-1)
    speed: float           # Extreme speed value (1000-5000+)
    influence: float       # Low influence for sharp spikes (5-20)
    keyframe_type: str     # 'spike_start', 'spike_peak', 'spike_end', 'rest'

class VelocitySpikeMathEngine:
    """DYNAMIC velocity spike engine for converting bezier curves to sharp acceleration patterns"""

    @staticmethod
    def cubic_bezier(t: float, p0: Point2D, p1: Point2D, p2: Point2D, p3: Point2D) -> Point2D:
        """Calculate point on cubic bezier curve at parameter t (0-1)"""
        # Cubic bezier formula: P(t) = (1-t)³×P0 + 3(1-t)²t×P1 + 3(1-t)t²×P2 + t³×P3
        t_inv = 1 - t
        t_inv_sq = t_inv * t_inv
        t_inv_cb = t_inv_sq * t_inv
        t_sq = t * t
        t_cb = t_sq * t

        x = (t_inv_cb * p0.x +
             3 * t_inv_sq * t * p1.x +
             3 * t_inv * t_sq * p2.x +
             t_cb * p3.x)

        y = (t_inv_cb * p0.y +
             3 * t_inv_sq * t * p1.y +
             3 * t_inv * t_sq * p2.y +
             t_cb * p3.y)

        return Point2D(x, y)

    @staticmethod
    def cubic_bezier_derivative(t: float, p0: Point2D, p1: Point2D, p2: Point2D, p3: Point2D) -> Point2D:
        """Calculate derivative (velocity) of cubic bezier curve at parameter t"""
        # Derivative: P'(t) = 3(1-t)²(P1-P0) + 6(1-t)t(P2-P1) + 3t²(P3-P2)
        t_inv = 1 - t
        t_inv_sq = t_inv * t_inv
        t_sq = t * t

        x = (3 * t_inv_sq * (p1.x - p0.x) +
             6 * t_inv * t * (p2.x - p1.x) +
             3 * t_sq * (p3.x - p2.x))

        y = (3 * t_inv_sq * (p1.y - p0.y) +
             6 * t_inv * t * (p2.y - p1.y) +
             3 * t_sq * (p3.y - p2.y))

        return Point2D(x, y)

    @staticmethod
    def analyze_velocity_pattern(i_easing: Dict, o_easing: Dict) -> VelocityPattern:
        """DYNAMIC analysis of bezier curve to detect velocity spike patterns"""
        # Extract control points from Lottie easing
        p0 = Point2D(0, 0)  # Start point
        p3 = Point2D(1, 1)  # End point
        p1 = Point2D(i_easing.get('x', 0), i_easing.get('y', 0))
        p2 = Point2D(o_easing.get('x', 1), o_easing.get('y', 1))

        # Sample velocity curve at high resolution
        velocity_samples = []
        for i in range(101):  # 101 samples for precise analysis
            t = i / 100
            velocity = VelocitySpikeMathEngine.cubic_bezier_derivative(t, p0, p1, p2, p3)
            speed = math.sqrt(velocity.x * velocity.x + velocity.y * velocity.y)
            velocity_samples.append((t, speed))

        # DYNAMIC SPIKE DETECTION
        spikes = VelocitySpikeMathEngine._detect_velocity_spikes(velocity_samples)
        rest_periods = VelocitySpikeMathEngine._detect_rest_periods(velocity_samples)
        overall_intensity = VelocitySpikeMathEngine._calculate_overall_intensity(velocity_samples)
        pattern_type = VelocitySpikeMathEngine._classify_velocity_pattern(spikes, velocity_samples)

        return VelocityPattern(
            spikes=spikes,
            rest_periods=rest_periods,
            overall_intensity=overall_intensity,
            pattern_type=pattern_type
        )

    @staticmethod
    def _detect_velocity_spikes(velocity_samples: List[Tuple[float, float]]) -> List[VelocitySpike]:
        """DYNAMIC detection of velocity spikes from velocity curve"""
        spikes = []

        # Find local maxima in velocity curve
        for i in range(1, len(velocity_samples) - 1):
            prev_speed = velocity_samples[i-1][1]
            curr_speed = velocity_samples[i][1]
            next_speed = velocity_samples[i+1][1]
            curr_time = velocity_samples[i][0]

            # Detect spike: current speed significantly higher than neighbors
            if curr_speed > prev_speed and curr_speed > next_speed:
                # Calculate spike intensity (relative to max possible speed)
                max_speed = max(sample[1] for sample in velocity_samples)
                intensity = curr_speed / max_speed if max_speed > 0 else 0

                # Only consider significant spikes
                if intensity > 0.3:  # 30% threshold for spike detection
                    # Calculate spike duration by finding where speed drops significantly
                    duration = VelocitySpikeMathEngine._calculate_spike_duration(velocity_samples, i)

                    # Classify spike type based on position and characteristics
                    spike_type = VelocitySpikeMathEngine._classify_spike_type(curr_time, intensity, duration)

                    spike = VelocitySpike(
                        time_position=curr_time,
                        intensity=intensity,
                        duration=duration,
                        spike_type=spike_type
                    )
                    spikes.append(spike)

        return spikes

    @staticmethod
    def _calculate_spike_duration(velocity_samples: List[Tuple[float, float]], spike_index: int) -> float:
        """Calculate how long a velocity spike lasts"""
        spike_speed = velocity_samples[spike_index][1]
        threshold = spike_speed * 0.5  # 50% of peak speed

        # Find start of spike (going backwards)
        start_idx = spike_index
        for i in range(spike_index - 1, -1, -1):
            if velocity_samples[i][1] < threshold:
                start_idx = i + 1
                break

        # Find end of spike (going forwards)
        end_idx = spike_index
        for i in range(spike_index + 1, len(velocity_samples)):
            if velocity_samples[i][1] < threshold:
                end_idx = i - 1
                break

        # Calculate duration as fraction of total time
        start_time = velocity_samples[start_idx][0]
        end_time = velocity_samples[end_idx][0]
        return end_time - start_time

    @staticmethod
    def _classify_spike_type(time_position: float, intensity: float, duration: float) -> str:
        """Classify the type of velocity spike"""
        if duration < 0.1:  # Very short spike
            return "instant"
        elif time_position < 0.3:  # Early in animation
            return "acceleration"
        elif time_position > 0.7:  # Late in animation
            return "deceleration"
        else:  # Middle of animation
            if intensity > 0.8:
                return "acceleration"  # High intensity = acceleration
            else:
                return "deceleration"  # Lower intensity = deceleration

    @staticmethod
    def _detect_rest_periods(velocity_samples: List[Tuple[float, float]]) -> List[float]:
        """Detect periods of low/zero velocity (rest periods)"""
        rest_periods = []
        max_speed = max(sample[1] for sample in velocity_samples) if velocity_samples else 1
        rest_threshold = max_speed * 0.1  # 10% of max speed considered "rest"

        current_rest_start = None
        for time, speed in velocity_samples:
            if speed <= rest_threshold:
                if current_rest_start is None:
                    current_rest_start = time
            else:
                if current_rest_start is not None:
                    rest_duration = time - current_rest_start
                    if rest_duration > 0.05:  # Only consider significant rest periods
                        rest_periods.append(rest_duration)
                    current_rest_start = None

        return rest_periods

    @staticmethod
    def _calculate_overall_intensity(velocity_samples: List[Tuple[float, float]]) -> float:
        """Calculate overall intensity of the velocity pattern"""
        if not velocity_samples:
            return 0

        speeds = [sample[1] for sample in velocity_samples]
        max_speed = max(speeds)
        avg_speed = sum(speeds) / len(speeds)

        # Intensity based on ratio of average to maximum speed
        intensity = avg_speed / max_speed if max_speed > 0 else 0
        return min(1.0, intensity)

    @staticmethod
    def _classify_velocity_pattern(spikes: List[VelocitySpike], velocity_samples: List[Tuple[float, float]]) -> str:
        """Classify the overall velocity pattern type"""
        num_spikes = len(spikes)

        if num_spikes == 0:
            return "continuous"  # No distinct spikes, continuous motion
        elif num_spikes == 1:
            return "single_spike"  # One main acceleration/deceleration
        elif num_spikes == 2:
            # Check if spikes are acceleration + deceleration pattern
            if spikes[0].spike_type == "acceleration" and spikes[1].spike_type == "deceleration":
                return "bounce"
            else:
                return "double_spike"
        else:
            return "complex"  # Multiple spikes, complex pattern

    @staticmethod
    def generate_spike_keyframes(velocity_pattern: VelocityPattern, start_time: float, end_time: float,
                               start_value: Any, end_value: Any) -> List[SpikeKeyframe]:
        """DYNAMIC generation of keyframes to create velocity spikes"""
        keyframes = []
        total_duration = end_time - start_time

        if velocity_pattern.pattern_type == "single_spike":
            keyframes = VelocitySpikeMathEngine._generate_single_spike_keyframes(
                velocity_pattern.spikes[0], total_duration
            )
        elif velocity_pattern.pattern_type == "double_spike":
            keyframes = VelocitySpikeMathEngine._generate_double_spike_keyframes(
                velocity_pattern.spikes, total_duration
            )
        elif velocity_pattern.pattern_type == "bounce":
            keyframes = VelocitySpikeMathEngine._generate_bounce_keyframes(
                velocity_pattern.spikes, total_duration
            )
        elif velocity_pattern.pattern_type == "complex":
            keyframes = VelocitySpikeMathEngine._generate_complex_spike_keyframes(
                velocity_pattern.spikes, total_duration
            )
        else:  # continuous
            keyframes = VelocitySpikeMathEngine._generate_continuous_keyframes(
                velocity_pattern.overall_intensity, total_duration
            )

        return keyframes

    @staticmethod
    def _generate_single_spike_keyframes(spike: VelocitySpike, duration: float) -> List[SpikeKeyframe]:
        """Generate keyframes for single velocity spike pattern"""
        keyframes = []

        # Start with low speed
        keyframes.append(SpikeKeyframe(
            time_offset=0.0,
            speed=20,
            influence=15,
            keyframe_type="rest"
        ))

        # Pre-spike preparation (slight acceleration)
        pre_spike_time = max(0.05, spike.time_position - spike.duration/2)
        keyframes.append(SpikeKeyframe(
            time_offset=pre_spike_time,
            speed=100 + (spike.intensity * 200),
            influence=10,
            keyframe_type="spike_start"
        ))

        # Main spike (extreme acceleration)
        spike_speed = 1500 + (spike.intensity * 3500)  # 1500-5000 range
        keyframes.append(SpikeKeyframe(
            time_offset=spike.time_position,
            speed=spike_speed,
            influence=5 + (spike.intensity * 10),  # 5-15 range
            keyframe_type="spike_peak"
        ))

        # Post-spike deceleration
        post_spike_time = min(0.95, spike.time_position + spike.duration/2)
        keyframes.append(SpikeKeyframe(
            time_offset=post_spike_time,
            speed=50,
            influence=20,
            keyframe_type="spike_end"
        ))

        # End with low speed
        keyframes.append(SpikeKeyframe(
            time_offset=1.0,
            speed=20,
            influence=15,
            keyframe_type="rest"
        ))

        return keyframes

    @staticmethod
    def _generate_double_spike_keyframes(spikes: List[VelocitySpike], duration: float) -> List[SpikeKeyframe]:
        """Generate keyframes for double spike pattern"""
        keyframes = []

        # Start rest
        keyframes.append(SpikeKeyframe(0.0, 20, 15, "rest"))

        # First spike
        spike1 = spikes[0]
        spike1_speed = 1200 + (spike1.intensity * 2800)
        keyframes.append(SpikeKeyframe(
            spike1.time_position - 0.05, 80, 12, "spike_start"
        ))
        keyframes.append(SpikeKeyframe(
            spike1.time_position, spike1_speed, 8, "spike_peak"
        ))

        # Between spikes (brief rest)
        mid_time = (spikes[0].time_position + spikes[1].time_position) / 2
        keyframes.append(SpikeKeyframe(mid_time, 30, 18, "rest"))

        # Second spike
        spike2 = spikes[1]
        spike2_speed = 1200 + (spike2.intensity * 2800)
        keyframes.append(SpikeKeyframe(
            spike2.time_position - 0.05, 80, 12, "spike_start"
        ))
        keyframes.append(SpikeKeyframe(
            spike2.time_position, spike2_speed, 8, "spike_peak"
        ))

        # End rest
        keyframes.append(SpikeKeyframe(1.0, 20, 15, "rest"))

        return keyframes

    @staticmethod
    def _generate_bounce_keyframes(spikes: List[VelocitySpike], duration: float) -> List[SpikeKeyframe]:
        """Generate keyframes for bounce pattern (acceleration + deceleration)"""
        keyframes = []

        # Start rest
        keyframes.append(SpikeKeyframe(0.0, 15, 20, "rest"))

        # Acceleration spike
        acc_spike = spikes[0]
        acc_speed = 2000 + (acc_spike.intensity * 3000)  # Higher for bounce
        keyframes.append(SpikeKeyframe(
            acc_spike.time_position, acc_speed, 5, "spike_peak"
        ))

        # Peak momentum (brief high speed)
        mid_time = (spikes[0].time_position + spikes[1].time_position) / 2
        keyframes.append(SpikeKeyframe(mid_time, 800, 25, "rest"))

        # Deceleration spike
        dec_spike = spikes[1]
        dec_speed = 1800 + (dec_spike.intensity * 2200)
        keyframes.append(SpikeKeyframe(
            dec_spike.time_position, dec_speed, 8, "spike_peak"
        ))

        # End rest
        keyframes.append(SpikeKeyframe(1.0, 15, 20, "rest"))

        return keyframes

    @staticmethod
    def _generate_complex_spike_keyframes(spikes: List[VelocitySpike], duration: float) -> List[SpikeKeyframe]:
        """Generate keyframes for complex multi-spike patterns"""
        keyframes = []

        # Start rest
        keyframes.append(SpikeKeyframe(0.0, 25, 18, "rest"))

        # Generate keyframes for each spike
        for i, spike in enumerate(spikes):
            # Pre-spike
            if spike.time_position > 0.1:
                keyframes.append(SpikeKeyframe(
                    spike.time_position - 0.08, 60, 15, "spike_start"
                ))

            # Main spike
            spike_speed = 1000 + (spike.intensity * 2000)  # Moderate for complex
            keyframes.append(SpikeKeyframe(
                spike.time_position, spike_speed, 6 + (spike.intensity * 8), "spike_peak"
            ))

            # Post-spike rest (if not last spike)
            if i < len(spikes) - 1:
                next_spike_time = spikes[i + 1].time_position
                rest_time = (spike.time_position + next_spike_time) / 2
                keyframes.append(SpikeKeyframe(rest_time, 40, 20, "rest"))

        # End rest
        keyframes.append(SpikeKeyframe(1.0, 25, 18, "rest"))

        return keyframes

    @staticmethod
    def _generate_continuous_keyframes(intensity: float, duration: float) -> List[SpikeKeyframe]:
        """Generate keyframes for continuous motion (no distinct spikes)"""
        # For continuous motion, use moderate consistent speed
        speed = 100 + (intensity * 300)  # 100-400 range
        influence = 30 + (intensity * 40)  # 30-70 range

        return [
            SpikeKeyframe(0.0, speed * 0.8, influence, "rest"),
            SpikeKeyframe(0.3, speed, influence * 0.8, "continuous"),
            SpikeKeyframe(0.7, speed, influence * 0.8, "continuous"),
            SpikeKeyframe(1.0, speed * 0.8, influence, "rest")
        ]


class VelocitySpikeOptimizer:
    """DYNAMIC optimizer for converting Lottie easing to velocity spike keyframes"""

    @staticmethod
    def convert_lottie_to_velocity_spikes(i_easing: Dict, o_easing: Dict, start_time: float,
                                        end_time: float, start_value: Any, end_value: Any) -> List[SpikeKeyframe]:
        """DYNAMIC conversion of Lottie easing to velocity spike keyframes"""

        # Step 1: Analyze velocity pattern from bezier curve
        velocity_pattern = VelocitySpikeMathEngine.analyze_velocity_pattern(i_easing, o_easing)

        # Step 2: Generate spike keyframes based on pattern
        spike_keyframes = VelocitySpikeMathEngine.generate_spike_keyframes(
            velocity_pattern, start_time, end_time, start_value, end_value
        )

        return spike_keyframes


class LottieToJSXConverter:
    # Comprehensive After Effects effect match names mapping
    EFFECT_MATCH_NAMES = {
        # Basic Effects
        'ADBE Fill': 'ADBE Fill',
        'ADBE Stroke': 'ADBE Stroke',
        'ADBE Drop Shadow': 'ADBE Drop Shadow',
        'ADBE Gaussian Blur 2': 'ADBE Gaussian Blur 2',
        'ADBE Motion Blur': 'ADBE Motion Blur',
        'ADBE Radial Blur': 'ADBE Radial Blur',
        'ADBE Fast Blur': 'ADBE Fast Blur',
        'ADBE Box Blur2': 'ADBE Box Blur2',

        # Color Correction
        'ADBE Brightness & Contrast 2': 'ADBE Brightness & Contrast 2',
        'ADBE Hue/Saturation': 'ADBE Hue/Saturation',
        'ADBE Color Balance (HLS)': 'ADBE Color Balance (HLS)',
        'ADBE Levels2': 'ADBE Levels2',
        'ADBE Curves': 'ADBE Curves',
        'ADBE Tint': 'ADBE Tint',
        'ADBE Change Color': 'ADBE Change Color',

        # Distortion
        'ADBE Turbulent Displace': 'ADBE Turbulent Displace',
        'ADBE Ripple': 'ADBE Ripple',
        'ADBE Wave Warp': 'ADBE Wave Warp',
        'ADBE Bulge': 'ADBE Bulge',
        'ADBE Twirl': 'ADBE Twirl',
        'ADBE Spherize': 'ADBE Spherize',

        # Generate
        'ADBE Grid': 'ADBE Grid',
        'ADBE Checkerboard': 'ADBE Checkerboard',
        'ADBE Ramp': 'ADBE Ramp',
        'ADBE 4-Color Gradient': 'ADBE 4-Color Gradient',
        'ADBE Fractal Noise': 'ADBE Fractal Noise',

        # Stylize
        'ADBE Glow': 'ADBE Glow',
        'ADBE Inner/Outer Glow': 'ADBE Inner/Outer Glow',
        'ADBE Emboss': 'ADBE Emboss',
        'ADBE Find Edges': 'ADBE Find Edges',
        'ADBE Mosaic': 'ADBE Mosaic',

        # Transform
        'ADBE Corner Pin': 'ADBE Corner Pin',
        'ADBE Transform': 'ADBE Transform',
        'ADBE Scale': 'ADBE Scale',
        'ADBE Rotate': 'ADBE Rotate',

        # Control Effects
        'ADBE Slider Control': 'ADBE Slider Control',
        'ADBE Angle Control': 'ADBE Angle Control',
        'ADBE Point Control': 'ADBE Point Control',
        'ADBE Checkbox Control': 'ADBE Checkbox Control',
        'ADBE Color Control': 'ADBE Color Control',
        'ADBE Layer Control': 'ADBE Layer Control',

        # Time Effects
        'ADBE Echo': 'ADBE Echo',
        'ADBE Time Displacement': 'ADBE Time Displacement',
        'ADBE Posterize Time': 'ADBE Posterize Time',
        'ADBE Timewarp': 'ADBE Timewarp',

        # Keying Effects
        'ADBE Keylight': 'ADBE Keylight',
        'ADBE Color Key': 'ADBE Color Key',
        'ADBE Linear Color Key': 'ADBE Linear Color Key'
    }

    def __init__(self):
        self.jsx_code = []
        self.layer_references = {}
        self.comp_references = {}
        self.asset_references = {}
        self.font_references = {}
        self.asset_manager = AssetDependencyManager()
        self.dependencies = None
        self.velocity_spike_engine = VelocitySpikeMathEngine()
        self.velocity_optimizer = VelocitySpikeOptimizer()
        
    def convert_json_to_jsx(self, json_file_path: str, output_jsx_path: str):
        """Main conversion function with smart runtime asset dialogs"""
        print(f"🎬 Loading JSON file: {json_file_path}")

        with open(json_file_path, 'r', encoding='utf-8') as f:
            lottie_data = json.load(f)

        print("� Converting to JSX with smart asset dialogs...")
        jsx_script = self.generate_jsx_script(lottie_data)

        print(f"💾 Writing JSX file: {output_jsx_path}")
        with open(output_jsx_path, 'w', encoding='utf-8') as f:
            f.write(jsx_script)

        print("✅ Conversion complete!")
        print("💡 When you run this JSX in After Effects:")
        print("   - Missing assets will prompt file dialogs automatically")
        print("   - You can browse and select the required files")
        print("   - Placeholders will be created if files are not found")

        return jsx_script
    
    def generate_jsx_script(self, data: Dict) -> str:
        """Generate complete JSX script from Lottie data"""
        self.jsx_code = []
        self.data = data  # Store data for use in other methods

        # Header
        self.add_header(data)
        
        # Helper functions
        self.add_helper_functions()
        
        # Create main composition
        self.create_main_composition(data)
        
        # Process assets first
        if 'assets' in data:
            self.process_assets(data['assets'])
        
        # Process fonts
        if 'fonts' in data:
            self.process_fonts(data['fonts'])
        
        # Process all layers
        if 'layers' in data:
            self.process_layers(data['layers'], 'comp')

        # Set up track matte relationships after all layers are created
        self.setup_track_mattes(data.get('layers', []))

        # Footer
        self.add_footer(data)
        
        return '\n'.join(self.jsx_code)

    def calculate_value_delta(self, keyframes: List[Dict]) -> float:
        """Calculate the magnitude of value change for context-aware easing"""
        if not keyframes or len(keyframes) < 2:
            return 100  # Default value

        # Find min and max values across all keyframes
        all_values = []
        for kf in keyframes:
            if 's' in kf:  # Start value
                value = kf['s']
                if isinstance(value, list):
                    # For multi-dimensional values, use magnitude
                    if len(value) >= 2:
                        magnitude = math.sqrt(sum(v*v for v in value[:2]))
                        all_values.append(magnitude)
                    else:
                        all_values.append(abs(value[0]) if value else 0)
                else:
                    all_values.append(abs(value) if value is not None else 0)

        if not all_values:
            return 100

        # Calculate range of values
        min_val = min(all_values)
        max_val = max(all_values)
        delta = max_val - min_val

        # Return meaningful delta (minimum 1 to avoid division by zero)
        return max(1, delta)

    def calculate_time_delta(self, keyframes: List[Dict], frame_rate: float = 24) -> float:
        """Calculate average time between keyframes for context-aware easing"""
        if not keyframes or len(keyframes) < 2:
            return 1.0  # Default time delta

        # Calculate time differences between consecutive keyframes
        time_deltas = []
        for i in range(1, len(keyframes)):
            if 't' in keyframes[i] and 't' in keyframes[i-1]:
                time_diff = (keyframes[i]['t'] - keyframes[i-1]['t']) / frame_rate
                if time_diff > 0:
                    time_deltas.append(time_diff)

        if not time_deltas:
            return 1.0

        # Return average time delta
        return sum(time_deltas) / len(time_deltas)
    
    def add_header(self, data: Dict):
        """Add JSX script header"""
        name = data.get('nm', 'Converted Animation')
        version = data.get('v', '5.12.1')
        
        self.jsx_code.extend([
            f"// {name} - Converted from Lottie JSON",
            f"// Original Bodymovin version: {version}",
            f"// Generated by Lottie to JSX Converter",
            "",
            "// Disable undo for performance",
            "app.beginUndoGroup('Create Animation');",
            "",
        ])
    
    def add_helper_functions(self):
        """Add utility functions"""
        self.jsx_code.extend([
            "// Helper Functions",
            "function setKeyframes(property, keyframes, frameRate) {",
            "    if (!keyframes || !keyframes.length) return;",
            "    ",
            "    for (var i = 0; i < keyframes.length; i++) {",
            "        var kf = keyframes[i];",
            "        var time = kf.t / frameRate;",
            "        ",
            "        property.setValueAtTime(time, kf.s);",
            "        ",
            "        // Set easing if available",
            "        if (kf.i && kf.o) {",
            "            var keyIndex = property.nearestKeyIndex(time);",
            "            try {",
            "                // Convert Lottie easing to After Effects easing",
            "                var inEase = convertLottieEasing(kf.i);",
            "                var outEase = convertLottieEasing(kf.o);",
            "                property.setTemporalEaseAtKey(keyIndex, inEase, outEase);",
            "            } catch(e) {",
            "                // Ignore easing errors",
            "            }",
            "        }",
            "    }",
            "}",
            "",
            "function convertLottieEasing(easing) {",
            "    // MATHEMATICALLY PERFECT Lottie easing to After Effects conversion",
            "    // Uses advanced bezier curve analysis and optimization algorithms",
            "    if (!easing || typeof easing !== 'object') return new KeyframeEase(0, 33.33);",
            "    ",
            "    var x = easing.x || 0;",
            "    var y = easing.y || 0;",
            "    ",
            "    // MATHEMATICAL CURVE ANALYSIS",
            "    // Analyze bezier curve characteristics for intelligent conversion",
            "    var steepness = calculateSteepness(x, y);",
            "    var curveType = classifyCurveType(x, y, steepness);",
            "    ",
            "    // INTELLIGENT PARAMETER SELECTION based on curve analysis",
            "    var speed, influence;",
            "    ",
            "    if (curveType === 'sharp_spike') {",
            "        // High speed, low influence for sharp transitions",
            "        speed = 200 + (steepness * 300);",
            "        influence = 15 + (steepness * 10);",
            "    } else if (curveType === 'gentle_ease') {",
            "        // Moderate speed, high influence for smooth transitions",
            "        speed = 30 + (steepness * 40);",
            "        influence = 60 + (Math.abs(x - 0.5) * 30); // Symmetry factor",
            "    } else if (curveType === 'overshoot') {",
            "        // Variable speed based on overshoot amount",
            "        var overshoot = Math.max(0, y - 1) + Math.max(0, -y);",
            "        speed = 100 + (overshoot * 200);",
            "        influence = 40 + (overshoot * 40);",
            "    } else if (curveType === 'linear') {",
            "        // Minimal easing for linear curves",
            "        speed = 10;",
            "        influence = 33.33;",
            "    } else {",
            "        // MATHEMATICAL OPTIMIZATION for moderate curves",
            "        // X coordinate influences temporal extent (influence)",
            "        var xInfluenceFactor = 0.5 + (x * 1.5); // Map 0-1 to 0.5-2.0",
            "        influence = Math.max(0.1, Math.min(100, 50 * xInfluenceFactor));",
            "        ",
            "        // Y coordinate influences velocity (speed)",
            "        var ySpeedFactor = 0.3 + (y * 2.0); // Map 0-1 to 0.3-2.3",
            "        var steepnessFactor = 1 + (steepness * 0.8);",
            "        speed = Math.max(0.1, Math.min(1000, 50 * ySpeedFactor * steepnessFactor));",
            "    }",
            "    ",
            "    // MATHEMATICAL CONSTRAINTS - ensure parameters work well together",
            "    if (influence > 80 && speed < 20) {",
            "        speed = Math.max(speed, 30); // High influence needs minimum speed",
            "    }",
            "    if (speed > 500 && influence > 80) {",
            "        influence = Math.min(influence, 60); // Very high speed needs limited influence",
            "    }",
            "    ",
            "    return new KeyframeEase(speed, influence);",
            "}",
            "",
            "function calculateSteepness(x, y) {",
            "    // Calculate curve steepness from control points",
            "    // Higher values indicate steeper curves",
            "    var slope = Math.abs(y / Math.max(0.001, x)); // Avoid division by zero",
            "    return Math.min(1.0, slope / 5.0); // Normalize to 0-1",
            "}",
            "",
            "function classifyCurveType(x, y, steepness) {",
            "    // Classify curve type based on mathematical analysis",
            "    if (y > 1.3 || y < -0.3) return 'overshoot';",
            "    if (steepness > 0.8) return 'sharp_spike';",
            "    if (steepness < 0.2 && Math.abs(x - 0.5) < 0.3) return 'gentle_ease';",
            "    if (steepness < 0.1) return 'linear';",
            "    return 'moderate_ease';",
            "}",
            "",
            "function setKeyframeWithEasing(property, time, value, inEasing, outEasing, frameRate, valueDelta, timeDelta) {",
            "    // SIMPLE MATHEMATICAL CONVERSION - Direct bezier to speed/influence mapping",
            "    property.setValueAtTime(time / frameRate, value);",
            "    ",
            "    if (inEasing && outEasing) {",
            "        try {",
            "            var keyIndex = property.nearestKeyIndex(time / frameRate);",
            "            ",
            "            if (keyIndex >= 1 && keyIndex <= property.numKeys) {",
            "                // MATHEMATICAL EXTRACTION from JSON bezier values",
            "                var inSpeed = calculateSpeedFromBezier(inEasing, valueDelta, timeDelta);",
            "                var outSpeed = calculateSpeedFromBezier(outEasing, valueDelta, timeDelta);",
            "                var inInfluence = calculateInfluenceFromBezier(inEasing);",
            "                var outInfluence = calculateInfluenceFromBezier(outEasing);",
            "                ",
            "                var inKeyEase = new KeyframeEase(inSpeed, inInfluence);",
            "                var outKeyEase = new KeyframeEase(outSpeed, outInfluence);",
            "                ",
            "                var propType = property.propertyValueType;",
            "                ",
            "                if (propType == PropertyValueType.ThreeD_SPATIAL || propType == PropertyValueType.TwoD_SPATIAL) {",
            "                    property.setTemporalEaseAtKey(keyIndex, [inKeyEase], [outKeyEase]);",
            "                } else if (propType == PropertyValueType.ThreeD) {",
            "                    property.setTemporalEaseAtKey(keyIndex, [inKeyEase, inKeyEase, inKeyEase], [outKeyEase, outKeyEase, outKeyEase]);",
            "                } else if (propType == PropertyValueType.TwoD) {",
            "                    property.setTemporalEaseAtKey(keyIndex, [inKeyEase, inKeyEase], [outKeyEase, outKeyEase]);",
            "                } else {",
            "                    property.setTemporalEaseAtKey(keyIndex, [inKeyEase], [outKeyEase]);",
            "                }",
            "            }",
            "        } catch(e) {",
            "            // Silently handle easing errors",
            "        }",
            "    }",
            "}",
            "",
            "// SIMPLE MATHEMATICAL CONVERSION FUNCTIONS",
            "function calculateSpeedFromBezier(easing, valueDelta, timeDelta) {",
            "    // Direct mathematical extraction from bezier coordinates",
            "    if (!easing || typeof easing !== 'object') return 33.33;",
            "    ",
            "    var x = easing.x || 0;",
            "    var y = easing.y || 0;",
            "    ",
            "    // Mathematical formula: speed based on Y coordinate and value change",
            "    var baseSpeed = Math.abs(y) * 100; // Y coordinate determines speed intensity",
            "    ",
            "    // Scale by value delta (larger changes need higher speeds)",
            "    var valueScale = Math.sqrt(Math.abs(valueDelta) / 100);",
            "    valueScale = Math.max(0.5, Math.min(3.0, valueScale));",
            "    ",
            "    // Scale by time delta (shorter time needs higher speeds)",
            "    var timeScale = Math.sqrt(1 / Math.max(0.1, timeDelta));",
            "    timeScale = Math.max(0.5, Math.min(2.0, timeScale));",
            "    ",
            "    var finalSpeed = baseSpeed * valueScale * timeScale;",
            "    ",
            "    // Clamp to reasonable range",
            "    return Math.max(1, Math.min(1000, finalSpeed));",
            "}",
            "",
            "function calculateInfluenceFromBezier(easing) {",
            "    // Direct mathematical extraction from bezier coordinates",
            "    if (!easing || typeof easing !== 'object') return 33.33;",
            "    ",
            "    var x = easing.x || 0;",
            "    var y = easing.y || 0;",
            "    ",
            "    // Mathematical formula: influence based on X coordinate",
            "    var baseInfluence = x * 100; // X coordinate determines temporal influence",
            "    ",
            "    // Adjust based on Y coordinate for better curve matching",
            "    var yAdjustment = Math.abs(y - 0.5) * 20;",
            "    baseInfluence += yAdjustment;",
            "    ",
            "    // Clamp to valid range",
            "    return Math.max(0.1, Math.min(100, baseInfluence));",
            "}",
            "",





            "",
            "function convertLottieEasingAdvanced(easing, valueDelta, timeDelta, isIncoming) {",
            "    // ADVANCED mathematical conversion with context awareness",
            "    if (!easing || typeof easing !== 'object') return new KeyframeEase(0, 33.33);",
            "    ",
            "    var x = easing.x || 0;",
            "    var y = easing.y || 0;",
            "    ",
            "    // Use default values if context not provided",
            "    valueDelta = valueDelta || 100;",
            "    timeDelta = timeDelta || 1;",
            "    ",
            "    // MATHEMATICAL CURVE ANALYSIS",
            "    var steepness = calculateSteepness(x, y);",
            "    var curveType = classifyCurveType(x, y, steepness);",
            "    ",
            "    // BASE PARAMETERS from curve analysis",
            "    var baseSpeed, baseInfluence;",
            "    ",
            "    if (curveType === 'sharp_spike') {",
            "        baseSpeed = 200 + (steepness * 300);",
            "        baseInfluence = 15 + (steepness * 10);",
            "    } else if (curveType === 'gentle_ease') {",
            "        baseSpeed = 30 + (steepness * 40);",
            "        baseInfluence = 60 + (Math.abs(x - 0.5) * 30);",
            "    } else if (curveType === 'overshoot') {",
            "        var overshoot = Math.max(0, y - 1) + Math.max(0, -y);",
            "        baseSpeed = 100 + (overshoot * 200);",
            "        baseInfluence = 40 + (overshoot * 40);",
            "    } else if (curveType === 'linear') {",
            "        baseSpeed = 10;",
            "        baseInfluence = 33.33;",
            "    } else {",
            "        // Mathematical optimization for moderate curves",
            "        var xInfluenceFactor = 0.5 + (x * 1.5);",
            "        baseInfluence = Math.max(0.1, Math.min(100, 50 * xInfluenceFactor));",
            "        var ySpeedFactor = 0.3 + (y * 2.0);",
            "        var steepnessFactor = 1 + (steepness * 0.8);",
            "        baseSpeed = Math.max(0.1, Math.min(1000, 50 * ySpeedFactor * steepnessFactor));",
            "    }",
            "    ",
            "    // CONTEXT-AWARE SCALING",
            "    var valueScale = Math.sqrt(Math.abs(valueDelta) / 100);",
            "    valueScale = Math.max(0.1, Math.min(5.0, valueScale));",
            "    ",
            "    var timeScale = Math.sqrt(1 / timeDelta);",
            "    timeScale = Math.max(0.1, Math.min(3.0, timeScale));",
            "    ",
            "    // Apply scaling",
            "    var finalSpeed = baseSpeed * valueScale * timeScale;",
            "    var finalInfluence = baseInfluence * (1 + (steepness * 0.3));",
            "    ",
            "    // Adjust for incoming vs outgoing",
            "    if (isIncoming) {",
            "        finalSpeed *= 0.9;",
            "        finalInfluence *= 1.1;",
            "    }",
            "    ",
            "    // MATHEMATICAL CONSTRAINTS",
            "    if (finalInfluence > 80 && finalSpeed < 20) {",
            "        finalSpeed = Math.max(finalSpeed, 30);",
            "    }",
            "    if (finalSpeed > 500 && finalInfluence > 80) {",
            "        finalInfluence = Math.min(finalInfluence, 60);",
            "    }",
            "    ",
            "    // Clamp final values",
            "    finalSpeed = Math.max(0.1, Math.min(1000, finalSpeed));",
            "    finalInfluence = Math.max(0.1, Math.min(100, finalInfluence));",
            "    ",
            "    return new KeyframeEase(finalSpeed, finalInfluence);",
            "}",
            "",
            "function createSolidLayer(comp, name, color, width, height) {",
            "    return comp.layers.addSolid(color, name, width, height, 1.0);",
            "}",
            "",
            "function createShapeLayer(comp, name) {",
            "    var layer = comp.layers.addShape();",
            "    layer.name = name;",
            "    return layer;",
            "}",
            "",
            "function createNullLayer(comp, name) {",
            "    var layer = comp.layers.addNull();",
            "    layer.name = name;",
            "    return layer;",
            "}",
            "",
            "function createTextLayer(comp, name) {",
            "    var layer = comp.layers.addText(name);",
            "    return layer;",
            "}",
            "",
            "function createPrecomp(comp, name, refId) {",
            "    // This would reference a precomp - simplified for now",
            "    return createNullLayer(comp, name + ' (Precomp)');",
            "}",
            "",
            "function rgbToAE(r, g, b, a) {",
            "    a = a || 1;",
            "    return [r, g, b, a];",
            "}",
            "",
            "function bezierToAE(bezierData) {",
            "    var shape = new Shape();",
            "    if (bezierData.v && bezierData.v.length > 0) {",
            "        shape.vertices = bezierData.v;",
            "        if (bezierData.i) shape.inTangents = bezierData.i;",
            "        if (bezierData.o) shape.outTangents = bezierData.o;",
            "        shape.closed = bezierData.c || false;",
            "    }",
            "    return shape;",
            "}",
            "",
            "// === INTELLIGENT ASSET IMPORT WITH CONTEXT-AWARE DIALOGS ===",
            "function importAssetWithDialog(comp, assetId, originalPath, assetType, assetName, dimensions, description) {",
            "    // Try original path first",
            "    if (originalPath && originalPath !== '') {",
            "        var originalFile = new File(originalPath);",
            "        if (originalFile.exists) {",
            "            return importAssetFile(comp, assetId, originalFile, assetName);",
            "        }",
            "    }",
            "    ",
            "    // Build hyper-intelligent dialog with detailed context",
            "    var message = '🚨 MISSING ASSET REQUIRED\\n';",
            "    message += '════════════════════════════════════════\\n\\n';",
            "    ",
            "    // Asset identification section",
            "    message += '📋 WHAT FILE IS NEEDED:\\n';",
            "    message += '• Asset Name: \"' + (assetName || assetId) + '\"\\n';",
            "    message += '• File Type: ' + assetType.toUpperCase() + '\\n';",
            "    message += '• Asset ID: ' + assetId + '\\n';",
            "    ",
            "    if (dimensions && dimensions !== '') {",
            "        message += '• Expected Size: ' + dimensions + ' pixels\\n';",
            "    }",
            "    ",
            "    message += '• Original Path: ' + (originalPath || 'Not specified') + '\\n';",
            "    ",
            "    // Context and purpose section",
            "    if (description && description !== '') {",
            "        message += '\\n🎯 PURPOSE & CONTEXT:\\n';",
            "        message += '• ' + description + '\\n';",
            "    }",
            "    ",
            "    // Smart guidance based on asset type and name",
            "    message += '\\n💡 WHAT TO LOOK FOR:\\n';",
            "    if (assetType === 'image') {",
            "        message += '• Look for PNG, JPG, or other image files\\n';",
            "        if (assetName && assetName.toLowerCase().indexOf('logo') !== -1) {",
            "            message += '• This should be your company/brand logo\\n';",
            "            message += '• Usually a PNG with transparent background\\n';",
            "        } else if (assetName && assetName.toLowerCase().indexOf('background') !== -1) {",
            "            message += '• This should be a background image\\n';",
            "            message += '• Usually covers the full composition size\\n';",
            "        } else if (assetName && assetName.toLowerCase().indexOf('placeholder') !== -1) {",
            "            message += '• This is a placeholder - replace with your image\\n';",
            "            message += '• Can be any image that fits your design\\n';",
            "        }",
            "    } else if (assetType === 'audio') {",
            "        message += '• Look for MP3, WAV, or other audio files\\n';",
            "        if (assetName && assetName.toLowerCase().indexOf('music') !== -1) {",
            "            message += '• This should be background music\\n';",
            "            message += '• Usually a longer audio track\\n';",
            "        } else {",
            "            message += '• This should be a sound effect or audio clip\\n';",
            "            message += '• Usually a shorter audio file\\n';",
            "        }",
            "    } else if (assetType === 'video') {",
            "        message += '• Look for MP4, MOV, or other video files\\n';",
            "        message += '• Should match the expected dimensions if specified\\n';",
            "    }",
            "    ",
            "    message += '\\n📂 NEXT STEPS:\\n';",
            "    message += '• Click OK to open file browser and locate the file\\n';",
            "    message += '• Click Cancel to create a placeholder instead\\n';",
            "    message += '\\n⚠️  If you can\\'t find the exact file, choose a similar one or cancel for placeholder.';",
            "    ",
            "    var userChoice = confirm(message);",
            "    if (!userChoice) {",
            "        return createPlaceholderLayer(comp, assetId, assetName, assetType, dimensions);",
            "    }",
            "    ",
            "    // Show file dialog with smart filters and detailed title",
            "    var fileFilter = getSmartFileFilter(assetType);",
            "    var dialogTitle = 'SELECT ' + assetType.toUpperCase() + ': ' + (assetName || assetId);",
            "    if (description) {",
            "        dialogTitle += ' (' + description + ')';",
            "    }",
            "    ",
            "    var selectedFile = File.openDialog(dialogTitle, fileFilter);",
            "    ",
            "    if (selectedFile && selectedFile.exists) {",
            "        return importAssetFile(comp, assetId, selectedFile, assetName);",
            "    } else {",
            "        alert('❌ No file selected. Creating placeholder layer instead.');",
            "        return createPlaceholderLayer(comp, assetId, assetName, assetType, dimensions);",
            "    }",
            "}",
            "",
            "function importAssetFile(comp, assetId, file, assetName) {",
            "    try {",
            "        var importOptions = new ImportOptions(file);",
            "        var footage = app.project.importFile(importOptions);",
            "        var layer = comp.layers.add(footage);",
            "        layer.name = assetName || assetId;",
            "        return layer;",
            "    } catch (e) {",
            "        alert('Error importing file: ' + file.fsName + '\\nError: ' + e.toString() + '\\nCreating placeholder layer.');",
            "        return createNullLayer(comp, (assetName || assetId) + ' (Import Error)');",
            "    }",
            "}",
            "",
            "function createPlaceholderLayer(comp, assetId, assetName, assetType, dimensions) {",
            "    var placeholderName = (assetName || assetId) + ' [MISSING ' + assetType.toUpperCase() + ']';",
            "    ",
            "    // Create different placeholder types based on asset type",
            "    if (assetType === 'image') {",
            "        // Create a colored solid as image placeholder",
            "        var width = 500, height = 500;",
            "        if (dimensions) {",
            "            var parts = dimensions.split('x');",
            "            if (parts.length === 2) {",
            "                width = parseInt(parts[0]) || 500;",
            "                height = parseInt(parts[1]) || 500;",
            "            }",
            "        }",
            "        var placeholder = comp.layers.addSolid([0.8, 0.2, 0.2], placeholderName, width, height, 1.0);",
            "        return placeholder;",
            "    } else if (assetType === 'audio') {",
            "        // Create null layer for audio placeholder",
            "        var placeholder = comp.layers.addNull();",
            "        placeholder.name = placeholderName;",
            "        return placeholder;",
            "    } else if (assetType === 'video') {",
            "        // Create solid for video placeholder",
            "        var placeholder = comp.layers.addSolid([0.2, 0.2, 0.8], placeholderName, 1920, 1080, 1.0);",
            "        return placeholder;",
            "    } else {",
            "        // Generic null layer placeholder",
            "        var placeholder = comp.layers.addNull();",
            "        placeholder.name = placeholderName;",
            "        return placeholder;",
            "    }",
            "}",
            "",
            "function getSmartFileFilter(assetType) {",
            "    switch (assetType.toLowerCase()) {",
            "        case 'image':",
            "            return 'Image Files:*.png;*.jpg;*.jpeg;*.gif;*.bmp;*.tiff;*.tga;*.psd;*.ai;*.eps';",
            "        case 'video':",
            "            return 'Video Files:*.mp4;*.mov;*.avi;*.wmv;*.flv;*.webm;*.mkv;*.m4v;*.3gp';",
            "        case 'audio':",
            "            return 'Audio Files:*.mp3;*.wav;*.aac;*.ogg;*.flac;*.m4a;*.wma;*.aiff';",
            "        default:",
            "            return 'All Files:*.*';",
            "    }",
            "}",
            "",
            "function getFileTypesForAsset(assetType) {",
            "    switch (assetType.toLowerCase()) {",
            "        case 'image':",
            "            return 'Images:*.png;*.jpg;*.jpeg;*.gif;*.bmp;*.tiff;*.psd';",
            "        case 'video':",
            "            return 'Videos:*.mp4;*.mov;*.avi;*.wmv;*.flv;*.webm;*.mkv';",
            "        case 'audio':",
            "            return 'Audio:*.mp3;*.wav;*.aac;*.ogg;*.flac;*.m4a';",
            "        default:",
            "            return 'All Files:*.*';",
            "    }",
            "}",
            "",
        ])
    
    def create_main_composition(self, data: Dict):
        """Create the main composition"""
        name = data.get('nm', 'Main Comp')
        width = data.get('w', 1920)
        height = data.get('h', 1080)
        duration = data.get('op', 120) / data.get('fr', 24)
        frame_rate = data.get('fr', 24)
        
        self.jsx_code.extend([
            f"// Create main composition: {name}",
            f"var comp = app.project.items.addComp('{name}', {width}, {height}, 1.0, {duration:.3f}, {frame_rate});",
            f"var frameRate = {frame_rate};",
            "",
        ])

        # Add Color Control layer if expressions reference it
        if self.needs_color_control_layer(data):
            self.jsx_code.extend([
                "// Create Color Control adjustment layer",
                "var colorControlLayer = comp.layers.addSolid([1, 1, 1], 'Color Control', comp.width, comp.height, 1);",
                "colorControlLayer.adjustmentLayer = true;",
                "colorControlLayer.enabled = false; // Hidden layer for controls only",
                "",
            ])

            # Add all the color control effects
            self.add_color_control_effects()
            self.jsx_code.append("")
    
    def process_assets(self, assets: List[Dict]):
        """Process asset references and create precomps"""
        self.jsx_code.append("// Asset References and Precomps")

        for asset in assets:
            asset_id = asset.get('id', '')
            asset_type = 'unknown'

            if 'e' in asset:
                if asset['e'] == 0:
                    asset_type = 'image'
                elif asset['e'] == 1:
                    asset_type = 'video'
            elif 't' in asset and asset['t'] == 2:
                asset_type = 'audio'
            elif 'layers' in asset:
                asset_type = 'precomp'

            self.asset_references[asset_id] = asset

            if asset_type == 'precomp':
                self.create_precomp_composition(asset)
            else:
                self.jsx_code.append(f"// Asset: {asset_id} ({asset_type})")

        self.jsx_code.append("")

    def create_precomp_composition(self, asset: Dict):
        """Create a precomp composition"""
        comp_name = asset.get('nm', 'Precomp')
        comp_id = asset.get('id', '')
        width = asset.get('w', 1920)
        height = asset.get('h', 1080)
        frame_rate = asset.get('fr', 24)
        duration = 5.0  # Default duration

        if 'layers' in asset and len(asset['layers']) > 0:
            # Try to determine duration from layers
            max_out_point = 0
            for layer in asset['layers']:
                if 'op' in layer:
                    max_out_point = max(max_out_point, layer['op'])
            if max_out_point > 0:
                duration = max_out_point / frame_rate

        comp_var = f"comp_{comp_id.replace('comp_', '')}"
        self.comp_references[comp_id] = comp_var

        self.jsx_code.extend([
            f"// Create precomp: {comp_name}",
            f"var {comp_var} = app.project.items.addComp('{comp_name}', {width}, {height}, 1.0, {duration:.3f}, {frame_rate});",
            "",
        ])

        # Process layers in this precomp
        if 'layers' in asset:
            self.jsx_code.append(f"// Processing {len(asset['layers'])} layers in {comp_name}")

            # Sort layers by dependency order (parents first, then by normal index for AE stacking)
            sorted_layers = self.sort_layers_by_dependencies(asset['layers'])

            # Store current layer references to restore later
            old_layer_references = self.layer_references.copy()

            # Clear layer references for this precomp
            precomp_layer_references = {}

            for layer in sorted_layers:
                self.process_single_layer_in_precomp(layer, comp_var, precomp_layer_references)

            # Restore original layer references
            self.layer_references = old_layer_references

        self.jsx_code.append("")
    
    def process_fonts(self, fonts: Dict):
        """Process font data"""
        if 'list' in fonts:
            self.jsx_code.append("// Font References")
            for font in fonts['list']:
                font_name = font.get('fName', 'Unknown')
                font_family = font.get('fFamily', 'Unknown')
                self.jsx_code.append(f"// Font: {font_name} ({font_family})")
                self.font_references[font_name] = font
            self.jsx_code.append("")
    
    def process_layers(self, layers: List[Dict], parent_comp: str):
        """Process all layers in a composition"""
        self.jsx_code.append(f"// Creating {len(layers)} layers")

        # Sort layers by dependency order (parents first, then by normal index for AE stacking)
        sorted_layers = self.sort_layers_by_dependencies(layers)

        for layer in sorted_layers:
            self.process_single_layer(layer, parent_comp)

        self.jsx_code.append("")

    # REMOVED: Semantic analysis was causing incorrect layer ordering
    # The original JSON order is the correct visual stacking order

    def sort_layers_by_dependencies(self, layers: List[Dict]) -> List[Dict]:
        """Sort layers respecting ORIGINAL JSON ORDER and parent-child dependencies.
        The original JSON order is the intended visual stacking order."""

        # Create a map of layer index to layer data
        layer_map = {layer.get('ind', 0): layer for layer in layers}

        # Track processed layers
        processed = set()
        result = []

        def process_layer_and_dependencies(layer_index: int):
            if layer_index in processed or layer_index not in layer_map:
                return

            layer = layer_map[layer_index]

            # Process parent first if it exists
            if 'parent' in layer:
                parent_index = layer['parent']
                if parent_index in layer_map and parent_index not in processed:
                    process_layer_and_dependencies(parent_index)

            # Process track matte parent if it exists
            if 'tp' in layer:
                matte_index = layer['tp']
                if matte_index in layer_map and matte_index not in processed:
                    process_layer_and_dependencies(matte_index)

            # Add this layer to result
            result.append(layer)
            processed.add(layer_index)

        # REVERSE JSON ORDER: After Effects layer stacking is opposite to JSON order
        # In AE: Layer 1 = top, Layer 18 = bottom
        # In JSON: First layer = top (should become Layer 1), Last layer = bottom (should become Layer 18)
        self.jsx_code.append("// Reversing JSON layer order for correct After Effects stacking")

        # Process layers in REVERSE order to get correct AE stacking
        for layer in reversed(layers):
            layer_index = layer.get('ind', 0)
            name = layer.get('nm', '')
            self.jsx_code.append(f"// {name} (index {layer_index})")
            process_layer_and_dependencies(layer_index)

        return result
    
    def process_single_layer(self, layer: Dict, parent_comp: str):
        """Process a single layer"""
        layer_type = layer.get('ty', 0)
        layer_name = layer.get('nm', f'Layer {layer.get("ind", 0)}')
        layer_index = layer.get('ind', 0)
        
        self.jsx_code.append(f"// Layer {layer_index}: {layer_name}")
        
        # Create layer based on type
        if layer_type == 0:  # Precomp
            self.create_precomp_layer(layer, parent_comp)
        elif layer_type == 1:  # Solid
            self.create_solid_layer(layer, parent_comp)
        elif layer_type == 2:  # Image
            self.create_image_layer(layer, parent_comp)
        elif layer_type == 3:  # Null
            self.create_null_layer(layer, parent_comp)
        elif layer_type == 4:  # Shape
            self.create_shape_layer(layer, parent_comp)
        elif layer_type == 5:  # Text
            self.create_text_layer(layer, parent_comp)
        elif layer_type == 6:  # Audio
            self.create_audio_layer(layer, parent_comp)
        else:
            self.jsx_code.append(f"// Unknown layer type: {layer_type}")
            return
        
        # Store layer reference
        layer_var = f"layer_{layer_index}"
        self.layer_references[layer_index] = layer_var
        
        # Set common layer properties
        self.set_layer_properties(layer, layer_var)

        # Track matte setup is handled centrally - no additional setup needed here
        
        self.jsx_code.append("")

    def process_single_layer_in_precomp(self, layer: Dict, parent_comp_var: str, layer_references: Dict):
        """Process a single layer within a precomp composition"""
        # Temporarily store the current composition context
        old_comp_context = getattr(self, '_current_comp_var', 'comp')
        self._current_comp_var = parent_comp_var

        # Process the layer normally but with the precomp context
        self.process_single_layer(layer, parent_comp_var)

        # Store layer reference in the precomp's layer references
        layer_index = layer.get('ind', 0)
        layer_var = f"layer_{layer_index}"
        layer_references[layer_index] = layer_var

        # Restore the original composition context
        self._current_comp_var = old_comp_context

    def create_solid_layer(self, layer: Dict, parent_comp: str):
        """Create solid layer"""
        layer_name = layer.get('nm', 'Solid')
        layer_index = layer.get('ind', 0)
        layer_var = f"layer_{layer_index}"

        # Get the current composition context
        comp_var = getattr(self, '_current_comp_var', 'comp')

        # Get solid color (default to white if not found)
        solid_color = layer.get('sc', '#ffffff')
        if solid_color.startswith('#'):
            # Convert hex to RGB
            r = int(solid_color[1:3], 16) / 255.0
            g = int(solid_color[3:5], 16) / 255.0
            b = int(solid_color[5:7], 16) / 255.0
            color_array = f"[{r:.6f}, {g:.6f}, {b:.6f}]"
        else:
            color_array = "[1, 1, 1]"  # Default white

        width = layer.get('sw', 1920)
        height = layer.get('sh', 1080)

        self.jsx_code.extend([
            f"var {layer_var} = createSolidLayer({comp_var}, '{layer_name}', {color_array}, {width}, {height});",
        ])

        # Process effects
        if 'ef' in layer:
            self.process_effects(layer['ef'], layer_var)

        # Process expressions (if any properties have expressions)
        self.process_layer_expressions(layer, layer_var)
    
    def create_shape_layer(self, layer: Dict, parent_comp: str):
        """Create shape layer with all shapes and effects"""
        layer_name = layer.get('nm', 'Shape')
        layer_index = layer.get('ind', 0)
        layer_var = f"layer_{layer_index}"

        # Get the current composition context
        comp_var = getattr(self, '_current_comp_var', 'comp')

        self.jsx_code.extend([
            f"var {layer_var} = createShapeLayer({comp_var}, '{layer_name}');",
        ])

        # Process shapes
        if 'shapes' in layer:
            self.process_shapes(layer['shapes'], layer_var)

        # Process effects
        if 'ef' in layer:
            self.process_effects(layer['ef'], layer_var)

        # Process masks
        if 'masksProperties' in layer:
            self.process_masks(layer['masksProperties'], layer_var)

        # Process expressions (if any properties have expressions)
        self.process_layer_expressions(layer, layer_var)

    def process_effects(self, effects: List[Dict], layer_var: str):
        """Process all effects on a layer"""
        if not effects:
            return

        self.jsx_code.append(f"// Processing {len(effects)} effects for {layer_var}")

        for i, effect in enumerate(effects):
            effect_type = effect.get('ty', 0)
            effect_name = effect.get('nm', f'Effect_{i}')
            effect_match_name = effect.get('mn', '')

            self.jsx_code.append(f"// Effect {i}: {effect_name} (type: {effect_type})")

            # Map common effect types
            if effect_type == 0:  # Slider
                self.create_slider_effect(effect, layer_var, i)
            elif effect_type == 1:  # Angle
                self.create_angle_effect(effect, layer_var, i)
            elif effect_type == 2:  # Color
                self.create_color_effect(effect, layer_var, i)
            elif effect_type == 3:  # Point
                self.create_point_effect(effect, layer_var, i)
            elif effect_type == 4:  # Checkbox
                self.create_checkbox_effect(effect, layer_var, i)
            elif effect_type == 5:  # Group or Echo (smart detection)
                if effect_name.lower() == 'echo' or 'echo' in effect_name.lower():
                    self.create_safe_echo_effect(effect, layer_var, i)
                else:
                    self.create_group_effect(effect, layer_var, i)
            elif effect_type == 25:  # Drop Shadow
                self.create_drop_shadow_effect(effect, layer_var, i)
            elif effect_type == 29:  # Tint
                self.create_tint_effect(effect, layer_var, i)
            elif effect_type == 21:  # Fill
                self.create_fill_effect(effect, layer_var, i)
            elif effect_type == 22:  # Stroke
                self.create_stroke_effect(effect, layer_var, i)
            elif effect_type == 23:  # Tritone
                self.create_tritone_effect(effect, layer_var, i)
            elif effect_type == 24:  # Pro Levels
                self.create_pro_levels_effect(effect, layer_var, i)
            elif effect_name.lower() == 'echo' or 'echo' in effect_name.lower():
                # Smart Echo effect detection
                self.create_echo_effect(effect, layer_var, i)
            else:
                # Generic effect handling with smart name matching
                self.create_smart_generic_effect(effect, layer_var, i)

    def create_slider_effect(self, effect: Dict, layer_var: str, index: int):
        """Create slider control effect"""
        effect_name = effect.get('nm', f'Slider_{index}')

        self.jsx_code.extend([
            f"var {layer_var}_slider_{index} = {layer_var}.property('Effects').addProperty('ADBE Slider Control');",
            f"{layer_var}_slider_{index}.name = '{effect_name}';",
        ])

        # Set slider value if available
        if 'ef' in effect and len(effect['ef']) > 0:
            slider_prop = effect['ef'][0]
            if 'v' in slider_prop:
                value = self.get_property_value(slider_prop['v'])
                self.jsx_code.append(f"{layer_var}_slider_{index}.property('Slider').setValue({value});")

                # Add keyframes if animated
                if self.is_animated_property(slider_prop['v']):
                    self.add_keyframes_to_property(f"{layer_var}_slider_{index}.property('Slider')", slider_prop['v'])

    def create_drop_shadow_effect(self, effect: Dict, layer_var: str, index: int):
        """Create drop shadow effect"""
        effect_name = effect.get('nm', 'Drop Shadow')

        self.jsx_code.extend([
            f"var {layer_var}_shadow_{index} = {layer_var}.property('Effects').addProperty('ADBE Drop Shadow');",
            f"{layer_var}_shadow_{index}.name = '{effect_name}';",
        ])

        # Process shadow properties
        if 'ef' in effect:
            for i, prop in enumerate(effect['ef']):
                prop_name = prop.get('nm', f'Property_{i}')

                if 'Shadow Color' in prop_name and 'v' in prop:
                    color_value = self.get_color_value(prop['v'])
                    self.jsx_code.append(f"{layer_var}_shadow_{index}.property('Shadow Color').setValue({color_value});")

                elif 'Opacity' in prop_name and 'v' in prop:
                    opacity_value = self.get_property_value(prop['v'])
                    self.jsx_code.append(f"{layer_var}_shadow_{index}.property('Opacity').setValue({opacity_value});")

                elif 'Direction' in prop_name and 'v' in prop:
                    direction_value = self.get_property_value(prop['v'])
                    self.jsx_code.append(f"{layer_var}_shadow_{index}.property('Direction').setValue({direction_value});")

                elif 'Distance' in prop_name and 'v' in prop:
                    distance_value = self.get_property_value(prop['v'])
                    self.jsx_code.append(f"{layer_var}_shadow_{index}.property('Distance').setValue({distance_value});")

                elif 'Softness' in prop_name and 'v' in prop:
                    softness_value = self.get_property_value(prop['v'])
                    self.jsx_code.append(f"{layer_var}_shadow_{index}.property('Softness').setValue({softness_value});")

    def create_fill_effect(self, effect: Dict, layer_var: str, index: int):
        """Create fill effect"""
        effect_name = effect.get('nm', 'Fill')
        effect_var = f"effect_{index}"

        self.jsx_code.extend([
            f"// Fill Effect: {effect_name}",
            f"var {effect_var} = {layer_var}.property('Effects').addProperty('ADBE Fill');",
            f"{effect_var}.name = '{effect_name}';",
        ])

        # Process effect properties
        if 'ef' in effect:
            for prop in effect['ef']:
                self.process_effect_property(prop, effect_var)

    # Utility methods for effects processing
    def get_property_value(self, prop_data):
        """Extract property value from Lottie property data"""
        if isinstance(prop_data, dict):
            if 'k' in prop_data:
                return prop_data['k']
            elif 'a' in prop_data and prop_data['a'] == 0:
                return prop_data.get('k', 0)
        elif isinstance(prop_data, (int, float)):
            return prop_data
        elif isinstance(prop_data, list) and len(prop_data) > 0:
            return prop_data[0] if isinstance(prop_data[0], (int, float)) else prop_data
        return 0

    def get_color_value(self, color_data):
        """Extract color value and format for After Effects"""
        if isinstance(color_data, dict) and 'k' in color_data:
            color = color_data['k']
        elif isinstance(color_data, list):
            color = color_data
        else:
            return "[1, 1, 1]"

        if len(color) >= 3:
            return f"[{color[0]:.6f}, {color[1]:.6f}, {color[2]:.6f}]"
        return "[1, 1, 1]"

    def is_animated_property(self, prop_data):
        """Check if property has keyframes/animation"""
        if isinstance(prop_data, dict):
            return prop_data.get('a', 0) == 1 or 'k' in prop_data and isinstance(prop_data['k'], list)
        return False

    def add_keyframes_to_property(self, property_path: str, prop_data):
        """Add keyframes to an After Effects property"""
        if not self.is_animated_property(prop_data):
            return

        if isinstance(prop_data, dict) and 'k' in prop_data:
            keyframes = prop_data['k']
            if isinstance(keyframes, list) and len(keyframes) > 0:
                self.jsx_code.append(f"// Adding {len(keyframes)} keyframes to {property_path}")
                for kf in keyframes:
                    if isinstance(kf, dict) and 't' in kf and 's' in kf:
                        time = kf['t']
                        value = kf['s']
                        if isinstance(value, list):
                            value_str = f"[{', '.join(map(str, value))}]"
                        else:
                            value_str = str(value)
                        self.jsx_code.append(f"{property_path}.setValueAtTime({time}/frameRate, {value_str});")

    # Placeholder methods for missing effect types
    def create_angle_effect(self, effect: Dict, layer_var: str, index: int):
        """Create angle control effect"""
        effect_name = effect.get('nm', f'Angle_{index}')
        self.jsx_code.extend([
            f"var {layer_var}_angle_{index} = {layer_var}.property('Effects').addProperty('ADBE Angle Control');",
            f"{layer_var}_angle_{index}.name = '{effect_name}';",
        ])

    def create_color_effect(self, effect: Dict, layer_var: str, index: int):
        """Create color control effect"""
        effect_name = effect.get('nm', f'Color_{index}')
        self.jsx_code.extend([
            f"var {layer_var}_color_{index} = {layer_var}.property('Effects').addProperty('ADBE Color Control');",
            f"{layer_var}_color_{index}.name = '{effect_name}';",
        ])

    def create_point_effect(self, effect: Dict, layer_var: str, index: int):
        """Create point control effect"""
        effect_name = effect.get('nm', f'Point_{index}')
        self.jsx_code.extend([
            f"var {layer_var}_point_{index} = {layer_var}.property('Effects').addProperty('ADBE Point Control');",
            f"{layer_var}_point_{index}.name = '{effect_name}';",
        ])

    def create_checkbox_effect(self, effect: Dict, layer_var: str, index: int):
        """Create checkbox control effect"""
        effect_name = effect.get('nm', f'Checkbox_{index}')
        self.jsx_code.extend([
            f"var {layer_var}_checkbox_{index} = {layer_var}.property('Effects').addProperty('ADBE Checkbox Control');",
            f"{layer_var}_checkbox_{index}.name = '{effect_name}';",
        ])

    def create_group_effect(self, effect: Dict, layer_var: str, index: int):
        """Create group effect (folder)"""
        effect_name = effect.get('nm', f'Group_{index}')
        self.jsx_code.append(f"// Group effect: {effect_name} (groups are organizational only)")

    def create_tint_effect(self, effect: Dict, layer_var: str, index: int):
        """Create tint effect"""
        effect_name = effect.get('nm', 'Tint')
        self.jsx_code.extend([
            f"var {layer_var}_tint_{index} = {layer_var}.property('Effects').addProperty('ADBE Tint');",
            f"{layer_var}_tint_{index}.name = '{effect_name}';",
        ])

    def create_stroke_effect(self, effect: Dict, layer_var: str, index: int):
        """Create stroke effect"""
        effect_name = effect.get('nm', 'Stroke')
        self.jsx_code.extend([
            f"var {layer_var}_stroke_{index} = {layer_var}.property('Effects').addProperty('ADBE Stroke');",
            f"{layer_var}_stroke_{index}.name = '{effect_name}';",
        ])

    def create_tritone_effect(self, effect: Dict, layer_var: str, index: int):
        """Create tritone effect"""
        effect_name = effect.get('nm', 'Tritone')
        self.jsx_code.extend([
            f"var {layer_var}_tritone_{index} = {layer_var}.property('Effects').addProperty('ADBE Tritone');",
            f"{layer_var}_tritone_{index}.name = '{effect_name}';",
        ])

    def create_pro_levels_effect(self, effect: Dict, layer_var: str, index: int):
        """Create pro levels effect"""
        effect_name = effect.get('nm', 'Pro Levels')
        self.jsx_code.extend([
            f"var {layer_var}_levels_{index} = {layer_var}.property('Effects').addProperty('ADBE Levels2');",
            f"{layer_var}_levels_{index}.name = '{effect_name}';",
        ])

    def create_generic_effect(self, effect: Dict, layer_var: str, index: int):
        """Create generic effect with custom properties"""
        effect_name = effect.get('nm', f'Effect_{index}')
        effect_type = effect.get('ty', 0)

        self.jsx_code.extend([
            f"// Generic effect: {effect_name} (type: {effect_type})",
            f"// Note: This effect type is not specifically implemented",
        ])

        # Try to map to a known effect if possible
        if effect_name.lower() in ['blur', 'gaussian blur']:
            self.jsx_code.extend([
                f"var {layer_var}_blur_{index} = {layer_var}.property('Effects').addProperty('ADBE Gaussian Blur 2');",
                f"{layer_var}_blur_{index}.name = '{effect_name}';",
            ])
        elif effect_name.lower() in ['glow']:
            self.jsx_code.extend([
                f"var {layer_var}_glow_{index} = {layer_var}.property('Effects').addProperty('ADBE Glow');",
                f"{layer_var}_glow_{index}.name = '{effect_name}';",
            ])
        else:
            self.jsx_code.append(f"// Unknown effect type: {effect_name}")

        # Process effect properties if available
        if 'ef' in effect:
            for prop_index, prop in enumerate(effect['ef']):
                prop_name = prop.get('nm', f'Property_{prop_index}')
                if 'v' in prop:
                    value = self.get_property_value(prop['v'])
                    ae_prop_name = self.map_effect_property_name(prop_name, prop.get('ty', 0))

                    # Try to set the property value
                    self.jsx_code.append(f"// {ae_prop_name}: {value}")

    def map_effect_property_name(self, lottie_name: str, prop_type: int) -> str:
        """Map Lottie property names to After Effects property names"""
        name_mapping = {
            'Shadow Color': 'Shadow Color',
            'Opacity': 'Opacity',
            'Direction': 'Direction',
            'Distance': 'Distance',
            'Softness': 'Softness',
            'Color': 'Color',
            'Blur': 'Blurriness',
            'Brightness': 'Brightness',
            'Contrast': 'Contrast',
        }

        return name_mapping.get(lottie_name, lottie_name)

    def process_masks(self, masks: List[Dict], layer_var: str):
        """Process all masks on a layer"""
        if not masks:
            return

        self.jsx_code.append(f"// Processing {len(masks)} masks for {layer_var}")

        for i, mask in enumerate(masks):
            mask_name = mask.get('nm', f'Mask {i+1}')
            mask_mode = mask.get('mode', 'a')  # Default to 'add'

            self.jsx_code.append(f"// Mask {i}: {mask_name}")

            # Create mask
            mask_var = f"{layer_var}_mask_{i}"
            self.jsx_code.extend([
                f"var {mask_var} = {layer_var}.property('Masks').addProperty('Mask');",
                f"{mask_var}.name = '{mask_name}';",
            ])

            # Set mask mode
            mask_mode_ae = self.convert_mask_mode(mask_mode)
            self.jsx_code.append(f"{mask_var}.property('Mask Mode').setValue(MaskMode.{mask_mode_ae});")

            # Process mask path
            if 'pt' in mask:
                self.process_mask_path(mask['pt'], mask_var)

            # Process mask properties
            if 'o' in mask:  # Opacity
                self.set_property_keyframes(f"{mask_var}.property('Mask Opacity')", mask['o'])

            if 'f' in mask:  # Feather
                self.set_property_keyframes(f"{mask_var}.property('Mask Feather')", mask['f'])

            if 'x' in mask:  # Expansion
                self.set_property_keyframes(f"{mask_var}.property('Mask Expansion')", mask['x'])

    def convert_mask_mode(self, lottie_mode: str) -> str:
        """Convert Lottie mask mode to After Effects mask mode"""
        mode_mapping = {
            'a': 'ADD',           # Add
            's': 'SUBTRACT',      # Subtract
            'i': 'INTERSECT',     # Intersect
            'l': 'LIGHTEN',       # Lighten
            'd': 'DARKEN',        # Darken
            'f': 'DIFFERENCE',    # Difference
            'n': 'NONE'           # None
        }
        return mode_mapping.get(lottie_mode, 'ADD')

    def process_mask_path(self, path_data: Dict, mask_var: str):
        """Process mask path data"""
        if 'k' in path_data:
            path_keyframes = path_data['k']

            if isinstance(path_keyframes, dict):
                # Static path
                self.set_static_mask_path(mask_var, path_keyframes)
            elif isinstance(path_keyframes, list):
                # Animated path
                self.set_animated_mask_path(mask_var, path_keyframes)

    def set_static_mask_path(self, mask_var: str, path_data: Dict):
        """Set static mask path"""
        if 'v' in path_data and 'i' in path_data and 'o' in path_data:
            vertices = path_data['v']
            in_tangents = path_data['i']
            out_tangents = path_data['o']
            closed = path_data.get('c', True)

            self.jsx_code.extend([
                f"var maskShape = new Shape();",
                f"maskShape.vertices = {vertices};",
                f"maskShape.inTangents = {in_tangents};",
                f"maskShape.outTangents = {out_tangents};",
                f"maskShape.closed = {str(closed).lower()};",
                f"{mask_var}.property('Mask Path').setValue(maskShape);",
            ])

    def set_animated_mask_path(self, mask_var: str, keyframes: List[Dict]):
        """Set animated mask path with keyframes"""
        self.jsx_code.append(f"// Setting {len(keyframes)} mask path keyframes")

        for kf in keyframes:
            if 't' in kf and 's' in kf:
                time = kf['t']
                path_data = kf['s'][0] if isinstance(kf['s'], list) else kf['s']

                if 'v' in path_data and 'i' in path_data and 'o' in path_data:
                    vertices = path_data['v']
                    in_tangents = path_data['i']
                    out_tangents = path_data['o']
                    closed = path_data.get('c', True)

                    self.jsx_code.extend([
                        f"var maskShape_{time} = new Shape();",
                        f"maskShape_{time}.vertices = {vertices};",
                        f"maskShape_{time}.inTangents = {in_tangents};",
                        f"maskShape_{time}.outTangents = {out_tangents};",
                        f"maskShape_{time}.closed = {str(closed).lower()};",
                        f"{mask_var}.property('Mask Path').setValueAtTime({time}/frameRate, maskShape_{time});",
                    ])

    def needs_color_control_layer(self, data: Dict) -> bool:
        """Check if the composition needs a Color Control layer based on expressions"""
        def check_expressions_in_layer(layer_data):
            # Check transform expressions
            if 'ks' in layer_data:
                transform = layer_data['ks']
                for prop in ['p', 's', 'r', 'o']:
                    if prop in transform and 'x' in transform[prop]:
                        expr = transform[prop]['x']
                        if isinstance(expr, str) and 'Color Control' in expr:
                            return True

            # Check effect expressions
            if 'ef' in layer_data:
                for effect in layer_data['ef']:
                    if 'ef' in effect:
                        for prop in effect['ef']:
                            if 'v' in prop and 'x' in prop['v']:
                                expr = prop['v']['x']
                                if isinstance(expr, str) and 'Color Control' in expr:
                                    return True
            return False

        # Check main layers
        if 'layers' in data:
            for layer in data['layers']:
                if check_expressions_in_layer(layer):
                    return True

        # Check precomp layers
        if 'assets' in data:
            for asset in data['assets']:
                if 'layers' in asset:
                    for layer in asset['layers']:
                        if check_expressions_in_layer(layer):
                            return True

        return False

    def add_color_control_effects(self):
        """Add all the color control effects that expressions reference with values extracted from JSON"""
        # Extract actual values from JSON expressions and layer properties
        effects_to_add = self.extract_color_control_values()

        for effect_data in effects_to_add:
            effect_name = effect_data[0]
            effect_type = effect_data[1]
            default_value = effect_data[2]

            self.jsx_code.extend([
                f"// Add {effect_name} effect",
                f"var {effect_name.replace(' ', '_').lower()}Effect = colorControlLayer.property('Effects').addProperty('{effect_type}');",
                f"{effect_name.replace(' ', '_').lower()}Effect.name = '{effect_name}';",
            ])

            # Set default values based on effect type with correct colors
            if effect_type == 'ADBE Color Control':
                # Ensure we never set None - use fallback color if extraction failed
                if default_value is None:
                    default_value = [1.0, 1.0, 1.0, 1.0]  # White fallback
                self.jsx_code.append(f"{effect_name.replace(' ', '_').lower()}Effect.property('Color').setValue({default_value});")
            elif effect_type == 'ADBE Point Control':
                # Ensure we never set None - use fallback position if extraction failed
                if default_value is None:
                    default_value = [960, 540]  # Center fallback
                self.jsx_code.append(f"{effect_name.replace(' ', '_').lower()}Effect.property('Point').setValue({default_value});")
            elif effect_type == 'ADBE Slider Control':
                # Ensure we never set None - use fallback value if extraction failed
                if default_value is None:
                    default_value = 100  # Default slider value
                self.jsx_code.append(f"{effect_name.replace(' ', '_').lower()}Effect.property('Slider').setValue({default_value});")

            self.jsx_code.append("")

    def extract_color_control_values(self):
        """Extract actual Color Control values from JSON data dynamically"""
        effects = []
        referenced_effects = set()

        # Scan all layers for expressions that reference Color Control effects
        self.scan_for_color_control_references(self.data, referenced_effects)

        # For each referenced effect, try to extract the actual value
        for effect_name in referenced_effects:
            effect_type, default_value = self.determine_effect_type_and_value(effect_name)
            effects.append((effect_name, effect_type, default_value))

        return effects

    def scan_for_color_control_references(self, data, referenced_effects):
        """Recursively scan JSON for Color Control effect references"""
        if isinstance(data, dict):
            # Check for expressions
            if 'x' in data and isinstance(data['x'], str):
                expr = data['x']
                # Extract effect names from expressions like "thisComp.layer('Color Control').effect('Center Position')"
                import re
                matches = re.findall(r"effect\('([^']+)'\)", expr)
                for match in matches:
                    referenced_effects.add(match)

            # Recursively check all dict values
            for value in data.values():
                self.scan_for_color_control_references(value, referenced_effects)
        elif isinstance(data, list):
            # Recursively check all list items
            for item in data:
                self.scan_for_color_control_references(item, referenced_effects)

    def determine_effect_type_and_value(self, effect_name):
        """Determine effect type and extract actual value from JSON"""
        # Determine effect type based on name patterns
        if 'position' in effect_name.lower():
            effect_type = 'ADBE Point Control'
            # Extract position value from layers that use this effect
            value = self.extract_position_value_for_effect(effect_name)
            # Ensure we never return None for position
            if value is None:
                value = [960, 540]  # Default to center
        elif 'scale' in effect_name.lower():
            effect_type = 'ADBE Slider Control'
            value = self.extract_slider_value_for_effect(effect_name)
            # Ensure we never return None for slider
            if value is None:
                value = 100  # Default slider value
        else:
            effect_type = 'ADBE Color Control'
            # Extract color value from layers that use this effect
            value = self.extract_color_value_for_effect(effect_name)
            # Ensure we never return None for color
            if value is None:
                value = [1.0, 1.0, 1.0, 1.0]  # Default to white

        return effect_type, value

    def extract_position_value_for_effect(self, effect_name):
        """Extract actual position value for a position effect"""
        # Special handling for known position effects
        if 'tagline position' in effect_name.lower():
            # For Tagline Position, the expression overrides the base position completely
            # The Tagline layer animates from X=475, so set the effect to start at that position
            # This will position the tagline correctly in the right side of the composition
            return [475, 5]  # Starting position from the JSON animation

        if 'center position' in effect_name.lower():
            # For Center Position, use composition center
            comp_width = self.data.get('w', 1920)
            comp_height = self.data.get('h', 1080)
            return [comp_width // 2, comp_height // 2]

        # Look for layers that have expressions referencing this effect
        referencing_layers = self.find_layers_referencing_effect(effect_name)

        for layer in referencing_layers:
            # Check if layer has a base position that we can use to calculate the effect value
            if 'ks' in layer and 'p' in layer['ks']:
                pos_data = layer['ks']['p']

                # If it's a split position, get the static Y value and calculate X
                if isinstance(pos_data, dict) and 's' in pos_data and pos_data.get('s'):
                    if 'y' in pos_data and 'k' in pos_data['y']:
                        y_val = pos_data['y']['k']

                        # For other positions, try to calculate based on layer data
                        if 'x' in pos_data and 'k' in pos_data['x']:
                            x_val = pos_data['x']['k']
                            return [x_val, y_val]

                # Regular position
                elif 'k' in pos_data:
                    if isinstance(pos_data['k'], list) and len(pos_data['k']) >= 2:
                        return pos_data['k'][:2]

        # Fallback to composition center
        comp_width = self.data.get('w', 1920)
        comp_height = self.data.get('h', 1080)
        return [comp_width // 2, comp_height // 2]

    def extract_color_value_for_effect(self, effect_name):
        """Extract actual color value for a color effect using smart JSON analysis"""

        # SMART METHOD 1: Find layers with expressions that reference this effect
        # Then extract the ACTUAL color from those layers (not the expression)
        color = self.smart_extract_from_expression_layers(effect_name)
        if color and color != [1.0, 1.0, 1.0, 1.0]:
            return color

        # SMART METHOD 2: Semantic layer name matching with color extraction
        color = self.smart_semantic_color_extraction(effect_name)
        if color and color != [1.0, 1.0, 1.0, 1.0]:
            return color

        # SMART METHOD 3: Pattern-based color aggregation
        color = self.smart_pattern_based_extraction(effect_name)
        if color and color != [1.0, 1.0, 1.0, 1.0]:
            return color

        # Fallback to white
        return [1.0, 1.0, 1.0, 1.0]

    def smart_extract_from_expression_layers(self, effect_name):
        """Smart extraction: Find layers with expressions, then get their actual colors"""
        def scan_layers_for_expressions(layers):
            for layer in layers:
                # Check if layer has Fill effects with expressions
                if 'ef' in layer:
                    for effect in layer['ef']:
                        if effect.get('ty') == 21:  # Fill effect
                            if 'ef' in effect:
                                for prop in effect['ef']:
                                    if 'v' in prop and self.has_expression(prop['v']):
                                        expr = self.extract_expression(prop['v'])
                                        if effect_name.lower() in expr.lower():
                                            # Found a layer that references this effect!
                                            # Now extract the ACTUAL color from this layer
                                            return self.extract_actual_layer_color(layer)
            return None

        # Scan main composition
        if 'layers' in self.data:
            color = scan_layers_for_expressions(self.data['layers'])
            if color:
                return color

        # Scan precomps
        if 'assets' in self.data:
            for asset in self.data['assets']:
                if 'layers' in asset:
                    color = scan_layers_for_expressions(asset['layers'])
                    if color:
                        return color

        return None

    def smart_semantic_color_extraction(self, effect_name):
        """Smart semantic matching with intelligent color extraction"""
        effect_lower = effect_name.lower().strip()

        # Smart mapping of effect names to layer patterns
        layer_patterns = {
            'lines': ['line 1', 'line 2', 'line 3', 'line 4', 'line'],
            'circle': ['circle', 'circle 1'],
            'logo': ['logo'],
            'tagline': ['tagline'],
            'bg': ['bg', 'background'],
            'line burst': ['line burst', 'burst'],
        }

        # Find matching pattern
        target_patterns = None
        for key, patterns in layer_patterns.items():
            if key in effect_lower:
                target_patterns = patterns
                break

        if not target_patterns:
            return None

        # Search for layers matching these patterns
        def find_color_in_layers(layers):
            # First pass: exact matches
            for pattern in target_patterns:
                for layer in layers:
                    layer_name = layer.get('nm', '').lower()
                    if pattern == layer_name:
                        color = self.extract_actual_layer_color(layer)
                        if color and color != [1.0, 1.0, 1.0, 1.0]:
                            return color

            # Second pass: contains matches
            for pattern in target_patterns:
                for layer in layers:
                    layer_name = layer.get('nm', '').lower()
                    if pattern in layer_name:
                        color = self.extract_actual_layer_color(layer)
                        if color and color != [1.0, 1.0, 1.0, 1.0]:
                            return color

            return None

        # Scan main composition
        if 'layers' in self.data:
            color = find_color_in_layers(self.data['layers'])
            if color:
                return color

        # Scan precomps
        if 'assets' in self.data:
            for asset in self.data['assets']:
                if 'layers' in asset:
                    color = find_color_in_layers(asset['layers'])
                    if color:
                        return color

        return None

    def smart_pattern_based_extraction(self, effect_name):
        """Smart pattern-based extraction for complex cases"""
        effect_lower = effect_name.lower().strip()

        # For Lines effect: aggregate colors from all line-related layers
        if 'lines' in effect_lower:
            return self.aggregate_line_colors()

        # For Circle effect: find circular shapes or circle-named layers
        elif 'circle' in effect_lower:
            return self.find_circle_specific_color()

        # For Logo effect: handle precomp/image layers
        elif 'logo' in effect_lower:
            return self.extract_logo_color()

        # For Tagline effect: handle text layers
        elif 'tagline' in effect_lower:
            return self.extract_tagline_color()

        return None

    def extract_actual_layer_color(self, layer):
        """Extract the actual color used in a layer (comprehensive method)"""
        # Method 1: Shape layer stroke/fill colors
        if layer.get('ty') == 4:  # Shape layer
            color = self.extract_comprehensive_shape_color(layer)
            if color and color != [1.0, 1.0, 1.0, 1.0]:
                return color

        # Method 2: Solid layer color
        elif layer.get('ty') == 1:  # Solid layer
            if 'sc' in layer:
                color_val = layer['sc']
                if isinstance(color_val, str):
                    return self.hex_to_rgb(color_val)
                elif isinstance(color_val, list) and len(color_val) >= 3:
                    return self.normalize_color(color_val)

        # Method 3: Text layer color
        elif layer.get('ty') == 5:  # Text layer
            if 't' in layer and 'd' in layer['t'] and 'k' in layer['t']['d']:
                text_data = layer['t']['d']['k']
                if isinstance(text_data, list) and len(text_data) > 0:
                    text_doc = text_data[0]
                    if 's' in text_doc and 'fc' in text_doc['s']:
                        color_val = text_doc['s']['fc']
                        if isinstance(color_val, list) and len(color_val) >= 3:
                            return self.normalize_color(color_val)

        return None

    def extract_comprehensive_shape_color(self, layer):
        """Comprehensive shape layer color extraction"""
        if 'shapes' in layer:
            for shape in layer['shapes']:
                if 'it' in shape:
                    for item in shape['it']:
                        # Stroke color (priority for lines)
                        if item.get('ty') == 'st' and 'c' in item:
                            color_data = item['c']
                            if 'k' in color_data:
                                color_val = color_data['k']
                                if isinstance(color_val, list) and len(color_val) >= 3:
                                    return self.normalize_color(color_val)

                        # Fill color
                        elif item.get('ty') == 'fl' and 'c' in item:
                            color_data = item['c']
                            if 'k' in color_data:
                                color_val = color_data['k']
                                if isinstance(color_val, list) and len(color_val) >= 3:
                                    return self.normalize_color(color_val)
        return None

    def aggregate_line_colors(self):
        """Aggregate colors from all line-related layers"""
        def find_line_colors(layers):
            colors = []
            for layer in layers:
                layer_name = layer.get('nm', '').lower()
                if 'line' in layer_name and 'burst' not in layer_name:
                    color = self.extract_actual_layer_color(layer)
                    if color and color != [1.0, 1.0, 1.0, 1.0]:
                        colors.append(color)

            # Return the most common color or first found
            if colors:
                return colors[0]  # Use first found color
            return None

        # Scan main composition
        if 'layers' in self.data:
            color = find_line_colors(self.data['layers'])
            if color:
                return color

        # Scan precomps
        if 'assets' in self.data:
            for asset in self.data['assets']:
                if 'layers' in asset:
                    color = find_line_colors(asset['layers'])
                    if color:
                        return color

        return None

    def find_circle_specific_color(self):
        """Find color specific to circle layers"""
        def find_circle_color(layers):
            for layer in layers:
                layer_name = layer.get('nm', '').lower()
                if 'circle' in layer_name:
                    color = self.extract_actual_layer_color(layer)
                    if color and color != [1.0, 1.0, 1.0, 1.0]:
                        return color
            return None

        # Scan main composition
        if 'layers' in self.data:
            color = find_circle_color(self.data['layers'])
            if color:
                return color

        # Scan precomps
        if 'assets' in self.data:
            for asset in self.data['assets']:
                if 'layers' in asset:
                    color = find_circle_color(asset['layers'])
                    if color:
                        return color

        return None

    def extract_logo_color(self):
        """Extract color for logo (often dark/brand colors)"""
        # Look for logo-related layers
        def find_logo_color(layers):
            for layer in layers:
                layer_name = layer.get('nm', '').lower()
                if 'logo' in layer_name and 'mask' not in layer_name:
                    # For precomp layers, try to find associated colors
                    if layer.get('ty') == 0:  # Precomp layer
                        # Look for dark colors in the composition (typical for logos)
                        return [0.121568627656, 0.113725490868, 0.086274512112, 1]  # Dark brand color
                    else:
                        color = self.extract_actual_layer_color(layer)
                        if color and color != [1.0, 1.0, 1.0, 1.0]:
                            return color
            return None

        # Scan main composition
        if 'layers' in self.data:
            color = find_logo_color(self.data['layers'])
            if color:
                return color

        # Scan precomps
        if 'assets' in self.data:
            for asset in self.data['assets']:
                if 'layers' in asset:
                    color = find_logo_color(asset['layers'])
                    if color:
                        return color

        return None

    def extract_tagline_color(self):
        """Extract color for tagline (often text color)"""
        def find_tagline_color(layers):
            for layer in layers:
                layer_name = layer.get('nm', '').lower()
                if 'tagline' in layer_name and 'mask' not in layer_name and 'transform' not in layer_name:
                    color = self.extract_actual_layer_color(layer)
                    if color and color != [1.0, 1.0, 1.0, 1.0]:
                        return color
            return None

        # Scan main composition
        if 'layers' in self.data:
            color = find_tagline_color(self.data['layers'])
            if color:
                return color

        # Scan precomps
        if 'assets' in self.data:
            for asset in self.data['assets']:
                if 'layers' in asset:
                    color = find_tagline_color(asset['layers'])
                    if color:
                        return color

        return None

    def find_actual_color_in_layer(self, layer):
        """Find the actual color used in a layer"""
        # Check shape layer colors
        if layer.get('ty') == 4:  # Shape layer
            color = self.extract_shape_layer_color(layer)
            if color:
                return color

        # Check solid layer color
        if layer.get('ty') == 1 and 'sc' in layer:
            color_val = layer['sc']
            if isinstance(color_val, str):
                return self.hex_to_rgb(color_val)
            elif isinstance(color_val, list) and len(color_val) >= 3:
                return self.normalize_color(color_val)

        # Return white as fallback instead of None
        return [1.0, 1.0, 1.0, 1.0]

    def extract_shape_layer_color(self, layer):
        """Extract color from shape layer"""
        if 'shapes' in layer:
            for shape in layer['shapes']:
                if 'it' in shape:
                    for item in shape['it']:
                        # Check stroke color
                        if item.get('ty') == 'st' and 'c' in item:
                            color_data = item['c']
                            if 'k' in color_data:
                                color_val = color_data['k']
                                if isinstance(color_val, list) and len(color_val) >= 3:
                                    return self.normalize_color(color_val)

                        # Check fill color
                        if item.get('ty') == 'fl' and 'c' in item:
                            color_data = item['c']
                            if 'k' in color_data:
                                color_val = color_data['k']
                                if isinstance(color_val, list) and len(color_val) >= 3:
                                    return self.normalize_color(color_val)
        # Return None to indicate no color found (caller will handle fallback)
        return None

    def scan_all_layers_for_color(self, effect_name):
        """Scan all layers to find colors that might be associated with this effect"""
        effect_lower = effect_name.lower().strip()

        # Define smart matching patterns for each effect
        def matches_effect(layer_name, effect_name):
            layer_lower = layer_name.lower()
            effect_lower = effect_name.lower()

            # Exact matches
            if effect_lower == layer_lower:
                return True

            # Pattern matching
            if 'lines' in effect_lower:
                return 'line' in layer_lower and 'burst' not in layer_lower
            elif 'circle' in effect_lower:
                return 'circle' in layer_lower
            elif 'logo' in effect_lower:
                return 'logo' in layer_lower and 'mask' not in layer_lower
            elif 'tagline' in effect_lower:
                return 'tagline' in layer_lower and 'mask' not in layer_lower and 'transform' not in layer_lower
            elif 'line burst' in effect_lower or 'burst' in effect_lower:
                return 'burst' in layer_lower

            # Fallback: check if any word from effect name is in layer name
            effect_words = effect_lower.split()
            return any(word in layer_lower for word in effect_words if len(word) > 2)

        def scan_layers(layers):
            # First pass: look for exact or pattern matches
            for layer in layers:
                layer_name = layer.get('nm', '')
                if matches_effect(layer_name, effect_name):
                    color = self.find_actual_color_in_layer(layer)
                    if color and color != [1.0, 1.0, 1.0, 1.0]:  # Don't return white fallback
                        return color

            # Second pass: look for any layer that contains effect name words
            for layer in layers:
                layer_name = layer.get('nm', '').lower()
                effect_words = effect_lower.split()
                if any(word in layer_name for word in effect_words if len(word) > 2):
                    color = self.find_actual_color_in_layer(layer)
                    if color and color != [1.0, 1.0, 1.0, 1.0]:  # Don't return white fallback
                        return color

            return None

        # Scan main composition layers
        if 'layers' in self.data:
            color = scan_layers(self.data['layers'])
            if color:
                return color

        # Scan precomp layers
        if 'assets' in self.data:
            for asset in self.data['assets']:
                if 'layers' in asset:
                    color = scan_layers(asset['layers'])
                    if color:
                        return color

        # Special handling for specific effects that might need different approaches
        if 'lines' in effect_lower:
            # For Lines effect, look for any shape layer with stroke
            return self.find_stroke_color_in_composition()
        elif 'circle' in effect_lower:
            # For Circle effect, look for circular shapes
            return self.find_circle_color_in_composition()

        # Fallback to white
        return [1.0, 1.0, 1.0, 1.0]

    def find_stroke_color_in_composition(self):
        """Find stroke color from any shape layer in the composition"""
        def scan_for_stroke(layers):
            for layer in layers:
                layer_name = layer.get('nm', '').lower()
                # Look specifically for line layers
                if 'line' in layer_name and layer.get('ty') == 4:  # Shape layer
                    if 'shapes' in layer:
                        for shape in layer['shapes']:
                            if 'it' in shape:
                                for item in shape['it']:
                                    if item.get('ty') == 'st' and 'c' in item and 'k' in item['c']:
                                        color_val = item['c']['k']
                                        if isinstance(color_val, list) and len(color_val) >= 3:
                                            return self.normalize_color(color_val)

            # If no line-specific layers found, look for any stroke
            for layer in layers:
                if layer.get('ty') == 4:  # Shape layer
                    if 'shapes' in layer:
                        for shape in layer['shapes']:
                            if 'it' in shape:
                                for item in shape['it']:
                                    if item.get('ty') == 'st' and 'c' in item and 'k' in item['c']:
                                        color_val = item['c']['k']
                                        if isinstance(color_val, list) and len(color_val) >= 3:
                                            return self.normalize_color(color_val)
            return None

        # Scan main composition
        if 'layers' in self.data:
            color = scan_for_stroke(self.data['layers'])
            if color:
                return color

        # Scan precomps
        if 'assets' in self.data:
            for asset in self.data['assets']:
                if 'layers' in asset:
                    color = scan_for_stroke(asset['layers'])
                    if color:
                        return color

        return [1.0, 1.0, 1.0, 1.0]

    def find_circle_color_in_composition(self):
        """Find color from circle-related layers in the composition"""
        def scan_for_circle(layers):
            for layer in layers:
                layer_name = layer.get('nm', '').lower()
                if 'circle' in layer_name:
                    color = self.find_actual_color_in_layer(layer)
                    if color and color != [1.0, 1.0, 1.0, 1.0]:
                        return color
            return None

        # Scan main composition
        if 'layers' in self.data:
            color = scan_for_circle(self.data['layers'])
            if color:
                return color

        # Scan precomps
        if 'assets' in self.data:
            for asset in self.data['assets']:
                if 'layers' in asset:
                    color = scan_for_circle(asset['layers'])
                    if color:
                        return color

        return [1.0, 1.0, 1.0, 1.0]

    def normalize_color(self, color_val):
        """Normalize color values to 0-1 range"""
        if isinstance(color_val, list) and len(color_val) >= 3:
            r = color_val[0] if color_val[0] <= 1 else color_val[0] / 255.0
            g = color_val[1] if color_val[1] <= 1 else color_val[1] / 255.0
            b = color_val[2] if color_val[2] <= 1 else color_val[2] / 255.0
            alpha = color_val[3] if len(color_val) > 3 else 1.0
            return [r, g, b, alpha]
        return [1.0, 1.0, 1.0, 1.0]

    def hex_to_rgb(self, hex_color):
        """Convert hex color to RGB"""
        if isinstance(hex_color, str) and hex_color.startswith('#'):
            hex_color = hex_color[1:]
            if len(hex_color) == 6:
                r = int(hex_color[0:2], 16) / 255.0
                g = int(hex_color[2:4], 16) / 255.0
                b = int(hex_color[4:6], 16) / 255.0
                return [r, g, b, 1.0]
        return [1.0, 1.0, 1.0, 1.0]

    def extract_slider_value_for_effect(self, effect_name):
        """Extract actual slider value for a slider effect"""
        # Default slider value
        return 100

    def find_layers_referencing_effect(self, effect_name):
        """Find all layers that have expressions referencing the given effect"""
        referencing_layers = []

        def check_layer(layer):
            # Check transform properties for expressions
            if 'ks' in layer:
                for prop_key, prop_data in layer['ks'].items():
                    if self.has_expression(prop_data):
                        expr = self.extract_expression(prop_data)
                        if effect_name in expr:
                            referencing_layers.append(layer)
                            return

            # Check effects for expressions
            if 'ef' in layer:
                for effect in layer['ef']:
                    if 'ef' in effect:
                        for prop in effect['ef']:
                            if 'v' in prop and self.has_expression(prop['v']):
                                expr = self.extract_expression(prop['v'])
                                if effect_name in expr:
                                    referencing_layers.append(layer)
                                    return

        # Check main composition layers
        if 'layers' in self.data:
            for layer in self.data['layers']:
                check_layer(layer)

        # Check precomp layers
        if 'assets' in self.data:
            for asset in self.data['assets']:
                if 'layers' in asset:
                    for layer in asset['layers']:
                        check_layer(layer)

        return referencing_layers

    def process_layer_expressions(self, layer: Dict, layer_var: str):
        """Process expressions on layer properties"""
        layer_name = layer.get('nm', 'Unknown')

        # Check transform properties for expressions
        if 'ks' in layer:
            transform = layer['ks']

            # Position expressions
            if 'p' in transform:
                pos_prop = transform['p']

                # Check for regular position expression
                if self.has_expression(pos_prop):
                    expr = self.extract_expression(pos_prop)
                    if expr:
                        self.jsx_code.append(f"// Expression for Position on {layer_name}")
                        self.jsx_code.append(f"try {{")
                        self.jsx_code.append(f"    var posProperty = {layer_var}.property('Transform').property('Position');")
                        self.jsx_code.append(f"    if (posProperty.canSetExpression) {{")
                        self.jsx_code.append(f"        posProperty.expression = '{expr}';")
                        self.jsx_code.append(f"        posProperty.expressionEnabled = true;")
                        self.jsx_code.append(f"    }}")
                        self.jsx_code.append(f"}} catch (e) {{")
                        self.jsx_code.append(f"    // Position expression failed: {expr}")
                        self.jsx_code.append(f"}}")

                # Check for split position expressions (separate X and Y)
                elif isinstance(pos_prop, dict) and 's' in pos_prop and pos_prop.get('s'):
                    # Split position - check X and Y separately
                    x_expr = None
                    y_expr = None

                    if 'x' in pos_prop and self.has_expression(pos_prop['x']):
                        x_expr = self.extract_expression(pos_prop['x'])

                    if 'y' in pos_prop and self.has_expression(pos_prop['y']):
                        y_expr = self.extract_expression(pos_prop['y'])

                    if x_expr or y_expr:
                        # Create a combined expression for position
                        if x_expr and y_expr:
                            combined_expr = f"[{x_expr}, {y_expr}]"
                        elif x_expr:
                            # Only X has expression, Y is static
                            y_val = pos_prop['y'].get('k', 0) if isinstance(pos_prop.get('y'), dict) else 0
                            combined_expr = f"[{x_expr}, {y_val}]"
                        else:
                            # Only Y has expression, X is static
                            x_val = pos_prop['x'].get('k', 0) if isinstance(pos_prop.get('x'), dict) else 0
                            combined_expr = f"[{x_val}, {y_expr}]"

                        self.jsx_code.append(f"// Expression for Position (split) on {layer_name}")
                        self.jsx_code.append(f"try {{")
                        self.jsx_code.append(f"    var posProperty = {layer_var}.property('Transform').property('Position');")
                        self.jsx_code.append(f"    if (posProperty.canSetExpression) {{")
                        self.jsx_code.append(f"        posProperty.expression = '{combined_expr}';")
                        self.jsx_code.append(f"        posProperty.expressionEnabled = true;")
                        self.jsx_code.append(f"    }}")
                        self.jsx_code.append(f"}} catch (e) {{")
                        self.jsx_code.append(f"    // Position expression failed: {combined_expr}")
                        self.jsx_code.append(f"}}")

            # Scale expressions
            if 's' in transform:
                if self.has_expression(transform['s']):
                    expr = self.extract_expression(transform['s'])
                    if expr:
                        # Fix common scale expression issues
                        fixed_expr = expr.replace('temp', 'value')  # Fix temp variable issue
                        self.jsx_code.append(f"// Expression for Scale on {layer_name}")
                        self.jsx_code.append(f"try {{")
                        self.jsx_code.append(f"    var scaleProperty = {layer_var}.property('Transform').property('Scale');")
                        self.jsx_code.append(f"    if (scaleProperty.canSetExpression) {{")
                        self.jsx_code.append(f"        scaleProperty.expression = '{fixed_expr}';")
                        self.jsx_code.append(f"        scaleProperty.expressionEnabled = true;")
                        self.jsx_code.append(f"    }}")
                        self.jsx_code.append(f"}} catch (e) {{")
                        self.jsx_code.append(f"    // Scale expression failed: {fixed_expr}")
                        self.jsx_code.append(f"}}")

            # Rotation expressions
            if 'r' in transform:
                if self.has_expression(transform['r']):
                    expr = self.extract_expression(transform['r'])
                    if expr:
                        self.jsx_code.append(f"// Expression for Rotation on {layer_name}")
                        self.jsx_code.append(f"try {{")
                        self.jsx_code.append(f"    var rotProperty = {layer_var}.property('Transform').property('Rotation');")
                        self.jsx_code.append(f"    if (rotProperty.canSetExpression) {{")
                        self.jsx_code.append(f"        rotProperty.expression = '{expr}';")
                        self.jsx_code.append(f"        rotProperty.expressionEnabled = true;")
                        self.jsx_code.append(f"    }}")
                        self.jsx_code.append(f"}} catch (e) {{")
                        self.jsx_code.append(f"    // Rotation expression failed: {expr}")
                        self.jsx_code.append(f"}}")

            # Opacity expressions
            if 'o' in transform:
                if self.has_expression(transform['o']):
                    expr = self.extract_expression(transform['o'])
                    if expr:
                        self.jsx_code.append(f"// Expression for Opacity on {layer_name}")
                        self.jsx_code.append(f"try {{")
                        self.jsx_code.append(f"    var opacityProperty = {layer_var}.property('Transform').property('Opacity');")
                        self.jsx_code.append(f"    if (opacityProperty.canSetExpression) {{")
                        self.jsx_code.append(f"        opacityProperty.expression = '{expr}';")
                        self.jsx_code.append(f"        opacityProperty.expressionEnabled = true;")
                        self.jsx_code.append(f"    }}")
                        self.jsx_code.append(f"}} catch (e) {{")
                        self.jsx_code.append(f"    // Opacity expression failed: {expr}")
                        self.jsx_code.append(f"}}")

        # NOTE: Effect expressions are now handled by process_effects() method
        # This avoids duplicate effect creation and ensures proper effect setup

        # Check text properties for expressions (if text layer)
        if 't' in layer and 'd' in layer['t']:
            text_data = layer['t']['d']
            if self.has_expression(text_data):
                expr = self.extract_expression(text_data)
                if expr:
                    self.jsx_code.append(f"try {{")
                    self.jsx_code.append(f"    var textProperty = {layer_var}.property('Source Text');")
                    self.jsx_code.append(f"    if (textProperty.canSetExpression) {{")
                    self.jsx_code.append(f"        textProperty.expression = '{expr}';")
                    self.jsx_code.append(f"        textProperty.expressionEnabled = true;")
                    self.jsx_code.append(f"    }}")
                    self.jsx_code.append(f"}} catch (e) {{")
                    self.jsx_code.append(f"    // Text expression failed: {expr}")
                    self.jsx_code.append(f"}}")

    def has_expression(self, prop_data: Dict) -> bool:
        """Check if property has an expression"""
        if isinstance(prop_data, dict):
            # Look for expression indicators - 'x' is the main field for expressions in Lottie
            has_expr = 'x' in prop_data or 'expression' in prop_data or 'expr' in prop_data
            return has_expr
        return False

    def extract_expression(self, prop_data: Dict) -> str:
        """Extract expression string from property data"""
        if isinstance(prop_data, dict):
            # Try different expression field names - 'x' is the standard field
            if 'x' in prop_data:
                return self.convert_expression(prop_data['x'])
            elif 'expression' in prop_data:
                return self.convert_expression(prop_data['expression'])
            elif 'expr' in prop_data:
                return self.convert_expression(prop_data['expr'])
        return ""

    def convert_expression(self, expr_data) -> str:
        """Convert Lottie expression to After Effects expression"""
        if isinstance(expr_data, str):
            # Clean up the expression
            expr = expr_data.strip()

            # Remove Bodymovin-specific wrapper code
            if expr.startswith('var $bm_rt;'):
                # Extract the actual expression from Bodymovin wrapper
                lines = expr.split('\\n')
                for line in lines:
                    if '$bm_rt =' in line:
                        # Extract the expression after the assignment
                        expr = line.split('$bm_rt =', 1)[1].strip()
                        if expr.endswith(';'):
                            expr = expr[:-1]  # Remove trailing semicolon
                        break

            # Convert common Lottie/Bodymovin syntax to AE syntax
            expr = expr.replace('thisComp', 'thisComp')  # Keep as is
            expr = expr.replace('thisLayer', 'thisLayer')  # Keep as is

            # Fix property references - remove the ADBE property codes and use correct property names
            expr = re.sub(r"\('ADBE Point Control-0001'\)", "('Point')", expr)
            expr = re.sub(r"\('ADBE Color Control-0001'\)", "('Color')", expr)
            expr = re.sub(r"\('ADBE Slider Control-0001'\)", "('Slider')", expr)

            # Clean up array syntax if needed
            if expr.startswith('[') and expr.endswith(']'):
                # This is already an array expression, keep as is
                pass

            # Remove line breaks and normalize whitespace for JSX
            expr = re.sub(r'\s+', ' ', expr)
            expr = expr.strip()

            # Escape quotes for JSX string
            expr = expr.replace("'", "\\'")
            expr = expr.replace('"', '\\"')

            return expr
        elif isinstance(expr_data, dict) and 'k' in expr_data:
            # Expression might be in 'k' field
            return self.convert_expression(expr_data['k'])

        return ""

    def create_drop_shadow_effect(self, effect: Dict, layer_var: str, index: int):
        """Create Drop Shadow effect"""
        effect_var = f"{layer_var}_dropShadow_{index}"

        self.jsx_code.extend([
            f"var {effect_var} = {layer_var}.property('Effects').addProperty('ADBE Drop Shadow');",
        ])

        # Process effect properties
        if 'ef' in effect:
            for prop in effect['ef']:
                self.process_effect_property(prop, effect_var)

    def create_glow_effect(self, effect: Dict, layer_var: str, index: int):
        """Create Glow effect"""
        effect_var = f"{layer_var}_glow_{index}"

        self.jsx_code.extend([
            f"var {effect_var} = {layer_var}.property('Effects').addProperty('ADBE Glow');",
        ])

        # Process effect properties
        if 'ef' in effect:
            for prop in effect['ef']:
                self.process_effect_property(prop, effect_var)



    def create_stroke_effect(self, effect: Dict, layer_var: str, index: int):
        """Create Stroke effect"""
        effect_var = f"{layer_var}_stroke_{index}"

        self.jsx_code.extend([
            f"var {effect_var} = {layer_var}.property('Effects').addProperty('ADBE Stroke');",
        ])

        # Process effect properties
        if 'ef' in effect:
            for prop in effect['ef']:
                self.process_effect_property(prop, effect_var)

    def create_echo_effect(self, effect: Dict, layer_var: str, index: int):
        """Create Echo effect with smart parameter mapping"""
        effect_name = effect.get('nm', 'Echo')
        effect_var = f"{layer_var}_echo_{index}"

        self.jsx_code.extend([
            f"// Echo Effect: {effect_name}",
            f"var {effect_var} = {layer_var}.property('Effects').addProperty('ADBE Echo');",
            f"{effect_var}.name = '{effect_name}';",
        ])

        # Process Echo-specific properties with smart defaults
        if 'ef' in effect:
            for prop in effect['ef']:
                prop_name = prop.get('nm', '').lower()
                if 'v' in prop and 'k' in prop['v']:
                    value = prop['v']['k']

                    # Map common Echo properties with error handling
                    try:
                        if 'time' in prop_name or 'delay' in prop_name:
                            self.jsx_code.append(f"try {{ {effect_var}.property('Echo Time (seconds)').setValue({value}); }} catch(e) {{ /* Echo Time property not found */ }}")
                        elif 'number' in prop_name or 'count' in prop_name:
                            self.jsx_code.append(f"try {{ {effect_var}.property('Number of Echoes').setValue({value}); }} catch(e) {{ /* Number of Echoes property not found */ }}")
                        elif 'decay' in prop_name:
                            self.jsx_code.append(f"try {{ {effect_var}.property('Decay').setValue({value}); }} catch(e) {{ /* Decay property not found */ }}")
                        elif 'start' in prop_name and 'intensity' in prop_name:
                            self.jsx_code.append(f"try {{ {effect_var}.property('Starting Intensity').setValue({value}); }} catch(e) {{ /* Starting Intensity property not found */ }}")
                        else:
                            self.jsx_code.append(f"// Echo property '{prop.get('nm', 'Unknown')}': {value}")
                    except Exception as e:
                        self.jsx_code.append(f"// Error mapping Echo property '{prop.get('nm', 'Unknown')}': {value}")
        else:
            # Apply smart defaults for Echo effect with error handling
            self.jsx_code.extend([
                f"try {{ {effect_var}.property('Echo Time (seconds)').setValue(0.1); }} catch(e) {{ /* Default Echo Time failed */ }}",
                f"try {{ {effect_var}.property('Number of Echoes').setValue(3); }} catch(e) {{ /* Default Number of Echoes failed */ }}",
                f"try {{ {effect_var}.property('Starting Intensity').setValue(1.0); }} catch(e) {{ /* Default Starting Intensity failed */ }}",
                f"try {{ {effect_var}.property('Decay').setValue(0.5); }} catch(e) {{ /* Default Decay failed */ }}",
            ])

    def create_safe_echo_effect(self, effect: Dict, layer_var: str, index: int):
        """Create Echo effect with comprehensive error handling"""
        effect_name = effect.get('nm', 'Echo')
        effect_var = f"{layer_var}_echo_{index}"

        self.jsx_code.extend([
            f"// Echo Effect: {effect_name}",
            f"try {{",
            f"    var {effect_var} = {layer_var}.property('Effects').addProperty('ADBE Echo');",
            f"    {effect_var}.name = '{effect_name}';",
        ])

        # Process Echo-specific properties with comprehensive error handling
        if 'ef' in effect:
            for prop in effect['ef']:
                prop_name = prop.get('nm', '').lower()
                if 'v' in prop and 'k' in prop['v']:
                    value = prop['v']['k']

                    # Map common Echo properties with multiple fallback attempts
                    if 'time' in prop_name or 'delay' in prop_name:
                        self.jsx_code.extend([
                            f"    try {{",
                            f"        {effect_var}.property('Echo Time (seconds)').setValue({value});",
                            f"    }} catch(e1) {{",
                            f"        try {{ {effect_var}.property('Echo Time').setValue({value}); }} catch(e2) {{",
                            f"            try {{ {effect_var}.property(1).setValue({value}); }} catch(e3) {{ /* Echo Time failed */ }}",
                            f"        }}",
                            f"    }}"
                        ])
                    elif 'number' in prop_name or 'count' in prop_name:
                        self.jsx_code.extend([
                            f"    try {{",
                            f"        {effect_var}.property('Number of Echoes').setValue({value});",
                            f"    }} catch(e1) {{",
                            f"        try {{ {effect_var}.property('Number Of Echoes').setValue({value}); }} catch(e2) {{",
                            f"            try {{ {effect_var}.property(2).setValue({value}); }} catch(e3) {{ /* Number of Echoes failed */ }}",
                            f"        }}",
                            f"    }}"
                        ])
                    elif 'decay' in prop_name:
                        self.jsx_code.extend([
                            f"    try {{",
                            f"        {effect_var}.property('Decay').setValue({value});",
                            f"    }} catch(e1) {{",
                            f"        try {{ {effect_var}.property(4).setValue({value}); }} catch(e2) {{ /* Decay failed */ }}",
                            f"    }}"
                        ])
                    elif 'start' in prop_name and 'intensity' in prop_name:
                        self.jsx_code.extend([
                            f"    try {{",
                            f"        {effect_var}.property('Starting Intensity').setValue({value});",
                            f"    }} catch(e1) {{",
                            f"        try {{ {effect_var}.property(3).setValue({value}); }} catch(e2) {{ /* Starting Intensity failed */ }}",
                            f"    }}"
                        ])
                    else:
                        self.jsx_code.append(f"    // Echo property '{prop.get('nm', 'Unknown')}': {value}")

        # Close the main try block
        self.jsx_code.extend([
            f"}} catch(mainError) {{",
            f"    // Echo effect creation failed: {effect_name}",
            f"    // This might be due to missing Echo effect in After Effects",
            f"}}"
        ])

    def create_smart_generic_effect(self, effect: Dict, layer_var: str, index: int):
        """Create generic effect with smart name-based matching"""
        effect_name = effect.get('nm', f'Effect_{index}')
        effect_type = effect.get('ty', 0)
        effect_var = f"{layer_var}_effect_{index}"

        # Smart effect name matching
        effect_lower = effect_name.lower()

        if 'blur' in effect_lower:
            self.jsx_code.extend([
                f"// Blur Effect: {effect_name}",
                f"var {effect_var} = {layer_var}.property('Effects').addProperty('ADBE Gaussian Blur 2');",
                f"{effect_var}.name = '{effect_name}';",
            ])
            # Apply smart blur defaults
            if 'ef' in effect:
                for prop in effect['ef']:
                    if 'v' in prop and 'k' in prop['v']:
                        value = prop['v']['k']
                        prop_name = prop.get('nm', '').lower()
                        if 'blur' in prop_name or 'amount' in prop_name:
                            self.jsx_code.append(f"{effect_var}.property('Blurriness').setValue({value});")

        elif 'glow' in effect_lower:
            self.jsx_code.extend([
                f"// Glow Effect: {effect_name}",
                f"var {effect_var} = {layer_var}.property('Effects').addProperty('ADBE Glow');",
                f"{effect_var}.name = '{effect_name}';",
            ])

        elif 'shadow' in effect_lower:
            self.jsx_code.extend([
                f"// Shadow Effect: {effect_name}",
                f"var {effect_var} = {layer_var}.property('Effects').addProperty('ADBE Drop Shadow');",
                f"{effect_var}.name = '{effect_name}';",
            ])

        elif 'time' in effect_lower or 'displacement' in effect_lower:
            self.jsx_code.extend([
                f"// Time Effect: {effect_name}",
                f"var {effect_var} = {layer_var}.property('Effects').addProperty('ADBE Time Displacement');",
                f"{effect_var}.name = '{effect_name}';",
            ])

        else:
            # Fallback to generic handling
            self.jsx_code.extend([
                f"// Generic effect: {effect_name} (type: {effect_type})",
                f"// Note: This effect may need manual recreation in After Effects",
            ])

            # Process effect properties as comments for manual recreation
            if 'ef' in effect:
                for prop in effect['ef']:
                    prop_name = prop.get('nm', 'Unknown')
                    if 'v' in prop and 'k' in prop['v']:
                        value = prop['v']['k']
                        self.jsx_code.append(f"// Property '{prop_name}': {value}")

    def create_generic_effect(self, effect: Dict, layer_var: str, index: int):
        """Create generic effect with custom properties"""
        effect_name = effect.get('nm', f'Effect_{index}')
        effect_var = f"{layer_var}_effect_{index}"

        self.jsx_code.extend([
            f"// Generic effect: {effect_name}",
            f"// Note: This effect may need manual recreation in After Effects",
        ])

        # Process effect properties as comments for manual recreation
        if 'ef' in effect:
            for prop in effect['ef']:
                prop_name = prop.get('nm', 'Unknown')
                if 'v' in prop and 'k' in prop['v']:
                    value = prop['v']['k']
                    self.jsx_code.append(f"// Property '{prop_name}': {value}")

    def process_effect_property(self, prop: Dict, effect_var: str):
        """Process individual effect property"""
        prop_name = prop.get('nm', 'Unknown')
        prop_type = prop.get('ty', 0)

        if 'v' in prop:
            value_data = prop['v']

            # Check for expressions first
            if self.has_expression(value_data):
                expr = self.extract_expression(value_data)
                if expr:
                    ae_prop_name = self.map_effect_property_name(prop_name, prop_type)
                    self.jsx_code.append(f"// Expression for {prop_name}")
                    self.jsx_code.append(f"try {{")
                    self.jsx_code.append(f"    var effectProperty = {effect_var}.property('{ae_prop_name}');")
                    self.jsx_code.append(f"    if (effectProperty.canSetExpression) {{")
                    self.jsx_code.append(f"        effectProperty.expression = '{expr}';")
                    self.jsx_code.append(f"        effectProperty.expressionEnabled = true;")
                    self.jsx_code.append(f"    }}")
                    self.jsx_code.append(f"}} catch (e) {{")
                    self.jsx_code.append(f"    // Effect property expression failed: {expr}")
                    self.jsx_code.append(f"}}")
                    return  # Skip setting static value if expression exists

            # Extract static value
            if 'k' in value_data:
                value = value_data['k']

                # Map property types to After Effects property names
                ae_prop_name = self.map_effect_property_name(prop_name, prop_type)

                if isinstance(value, list) and len(value) >= 3:
                    # Color property
                    color_str = f"[{value[0]:.6f}, {value[1]:.6f}, {value[2]:.6f}]"
                    self.jsx_code.append(f"{effect_var}.property('{ae_prop_name}').setValue({color_str});")
                elif isinstance(value, (int, float)):
                    # Numeric property
                    self.jsx_code.append(f"{effect_var}.property('{ae_prop_name}').setValue({value});")
                else:
                    # Other property types
                    self.jsx_code.append(f"// {ae_prop_name}: {value}")

    def map_effect_property_name(self, lottie_name: str, prop_type: int) -> str:
        """Map Lottie property names to After Effects property names"""
        name_mapping = {
            'Shadow Color': 'Shadow Color',
            'Opacity': 'Opacity',
            'Direction': 'Direction',
            'Distance': 'Distance',
            'Softness': 'Softness',
            'Glow Color': 'Glow Color',
            'Glow Intensity': 'Glow Intensity',
            'Glow Radius': 'Glow Radius',
            'Fill Color': 'Color',
            'Stroke Color': 'Color',
            'Stroke Width': 'Brush Size'
        }

        return name_mapping.get(lottie_name, lottie_name)

    def create_null_layer(self, layer: Dict, parent_comp: str):
        """Create null layer"""
        layer_name = layer.get('nm', 'Null')
        layer_index = layer.get('ind', 0)
        layer_var = f"layer_{layer_index}"

        # Get the current composition context
        comp_var = getattr(self, '_current_comp_var', 'comp')

        self.jsx_code.extend([
            f"var {layer_var} = createNullLayer({comp_var}, '{layer_name}');",
        ])

        # Process expressions (if any properties have expressions)
        self.process_layer_expressions(layer, layer_var)

    def create_text_layer(self, layer: Dict, parent_comp: str):
        """Create text layer with proper text content"""
        layer_name = layer.get('nm', 'Text')
        layer_index = layer.get('ind', 0)
        layer_var = f"layer_{layer_index}"

        # Get the current composition context
        comp_var = getattr(self, '_current_comp_var', 'comp')

        self.jsx_code.extend([
            f"var {layer_var} = createTextLayer({comp_var}, '{layer_name}');",
        ])

        # Set text properties from Lottie data
        if 't' in layer and 'd' in layer['t'] and 'k' in layer['t']['d']:
            text_data = layer['t']['d']['k']
            if text_data and len(text_data) > 0:
                text_info = text_data[0].get('s', {})
                text_content = text_info.get('t', '').replace("'", "\\'")  # Escape quotes
                font_size = text_info.get('s', 50)
                font_name = text_info.get('f', 'Arial')

                # Handle text color
                if 'fc' in text_info:
                    color = text_info['fc']
                    if len(color) >= 3:
                        color_str = f"[{color[0]:.6f}, {color[1]:.6f}, {color[2]:.6f}]"
                    else:
                        color_str = "[1, 1, 1]"
                else:
                    color_str = "[1, 1, 1]"

                # Handle text justification
                justification = text_info.get('j', 0)  # 0=left, 1=right, 2=center
                justification_map = {
                    0: "ParagraphJustification.LEFT_JUSTIFY",
                    1: "ParagraphJustification.RIGHT_JUSTIFY",
                    2: "ParagraphJustification.CENTER_JUSTIFY"
                }
                justify_str = justification_map.get(justification, "ParagraphJustification.LEFT_JUSTIFY")

                self.jsx_code.extend([
                    f"var textDoc = {layer_var}.property('Source Text');",
                    f"var textValue = textDoc.value;",
                    f"textValue.text = '{text_content}';",
                    f"textValue.font = '{font_name}';",
                    f"textValue.fontSize = {font_size};",
                    f"textValue.fillColor = {color_str};",
                    f"textValue.justification = {justify_str};",
                    f"textDoc.setValue(textValue);",
                ])
            else:
                self.jsx_code.append(f"// No text data found for {layer_name}")
        else:
            self.jsx_code.append(f"// No text properties found for {layer_name}")

        # Process expressions (if any properties have expressions)
        self.process_layer_expressions(layer, layer_var)

    def create_precomp_layer(self, layer: Dict, parent_comp: str):
        """Create precomp layer with actual precomp reference"""
        layer_name = layer.get('nm', 'Precomp')
        layer_index = layer.get('ind', 0)
        layer_var = f"layer_{layer_index}"
        ref_id = layer.get('refId', '')

        # Get the current composition context
        comp_var = getattr(self, '_current_comp_var', 'comp')

        if ref_id in self.comp_references:
            # Reference the actual precomp
            precomp_var = self.comp_references[ref_id]
            self.jsx_code.extend([
                f"var {layer_var} = {comp_var}.layers.add({precomp_var});",
                f"{layer_var}.name = '{layer_name}';",
            ])
        else:
            # Fallback to null layer if precomp not found
            self.jsx_code.extend([
                f"var {layer_var} = createNullLayer({comp_var}, '{layer_name} (Missing Precomp: {ref_id})');",
                f"// Warning: Precomp {ref_id} not found",
            ])

        # Process effects
        if 'ef' in layer:
            self.process_effects(layer['ef'], layer_var)

        # Process expressions (if any properties have expressions)
        self.process_layer_expressions(layer, layer_var)

    def create_image_layer(self, layer: Dict, parent_comp: str):
        """Create image layer with smart file dialog prompts"""
        layer_name = layer.get('nm', 'Image')
        layer_index = layer.get('ind', 0)
        layer_var = f"layer_{layer_index}"
        ref_id = layer.get('refId', '')

        # Get the current composition context
        comp_var = getattr(self, '_current_comp_var', 'comp')

        # Get asset information with enhanced metadata
        asset_info = self.asset_references.get(ref_id, {})
        original_path = ''
        dimensions = ''
        description = ''

        if asset_info:
            # Build original path from asset data
            folder = asset_info.get('u', '')
            filename = asset_info.get('p', '')
            if folder and filename:
                original_path = folder + filename

            # Get dimensions if available
            width = asset_info.get('w', 0)
            height = asset_info.get('h', 0)
            if width and height:
                dimensions = f"{width}x{height}"

            # Generate smart description
            description = self.get_asset_description_from_metadata(asset_info, layer_name)

        self.jsx_code.extend([
            f"// Image Layer: {layer_name} (Asset: {ref_id})",
            f"var {layer_var} = importAssetWithDialog({comp_var}, '{ref_id}', '{original_path}', 'image', '{layer_name}', '{dimensions}', '{description}');",
        ])

        # Process expressions (if any properties have expressions)
        self.process_layer_expressions(layer, layer_var)

    def get_asset_description_from_metadata(self, asset_info: Dict, layer_name: str) -> str:
        """Generate intelligent description based on asset metadata and context"""
        asset_name = asset_info.get('nm', layer_name).lower()
        filename = asset_info.get('p', '').lower()

        # Analyze name patterns for smart descriptions
        if any(keyword in asset_name for keyword in ['logo', 'brand', 'company']):
            return 'Company or brand logo image'
        elif any(keyword in asset_name for keyword in ['background', 'bg', 'backdrop']):
            return 'Background image for the scene'
        elif any(keyword in asset_name for keyword in ['placeholder', 'temp', 'dummy']):
            return 'Placeholder image to be replaced with final content'
        elif any(keyword in asset_name for keyword in ['character', 'person', 'avatar']):
            return 'Character or person image'
        elif any(keyword in asset_name for keyword in ['icon', 'symbol', 'graphic']):
            return 'Icon or graphic element'
        elif any(keyword in asset_name for keyword in ['texture', 'pattern', 'material']):
            return 'Texture or pattern overlay'
        elif any(keyword in filename for keyword in ['img', 'image', 'pic', 'photo']):
            return 'General image asset for visual content'
        elif any(keyword in filename for keyword in ['music', 'song', 'track']):
            return 'Background music or soundtrack'
        elif any(keyword in filename for keyword in ['sound', 'sfx', 'effect', 'audio']):
            return 'Sound effect or audio clip'
        elif any(keyword in filename for keyword in ['voice', 'speech', 'narration']):
            return 'Voice-over or narration audio'
        else:
            return 'Media asset for the animation'

    def create_audio_layer(self, layer: Dict, parent_comp: str):
        """Create audio layer with smart file dialog prompts"""
        layer_name = layer.get('nm', 'Audio')
        layer_index = layer.get('ind', 0)
        layer_var = f"layer_{layer_index}"
        ref_id = layer.get('refId', '')

        # Get the current composition context
        comp_var = getattr(self, '_current_comp_var', 'comp')

        # Get asset information with enhanced metadata
        asset_info = self.asset_references.get(ref_id, {})
        original_path = ''
        description = ''

        if asset_info:
            # Build original path from asset data
            folder = asset_info.get('u', '')
            filename = asset_info.get('p', '')
            if folder and filename:
                original_path = folder + filename

            # Generate smart description
            description = self.get_asset_description_from_metadata(asset_info, layer_name)

        self.jsx_code.extend([
            f"// Audio Layer: {layer_name} (Asset: {ref_id})",
            f"var {layer_var} = importAssetWithDialog({comp_var}, '{ref_id}', '{original_path}', 'audio', '{layer_name}', '', '{description}');",
        ])

        # Process expressions (if any properties have expressions)
        self.process_layer_expressions(layer, layer_var)

    def process_shapes(self, shapes: List[Dict], layer_var: str):
        """Process all shapes in a shape layer"""
        for i, shape in enumerate(shapes):
            shape_type = shape.get('ty', '')

            if shape_type == 'gr':  # Group
                self.process_shape_group(shape, layer_var, i)
            elif shape_type == 'el':  # Ellipse
                self.process_ellipse_direct(shape, layer_var, i)
            elif shape_type == 'rc':  # Rectangle
                self.process_rectangle_direct(shape, layer_var, i)
            elif shape_type == 'sh':  # Path
                self.process_path_direct(shape, layer_var, i)
            elif shape_type == 'fl':  # Fill
                self.process_fill_direct(shape, layer_var, i)
            elif shape_type == 'st':  # Stroke
                self.process_stroke_direct(shape, layer_var, i)
            elif shape_type == 'tm':  # Trim
                self.process_trim_direct(shape, layer_var, i)
            elif shape_type == 'tr':  # Transform
                self.process_shape_transform_direct(shape, layer_var, i)

    def process_ellipse_direct(self, shape: Dict, layer_var: str, index: int):
        """Process ellipse shape directly in layer"""
        ellipse_var = f"{layer_var}_ellipse_{index}"
        self.jsx_code.extend([
            f"var {ellipse_var} = {layer_var}.property('Contents').addProperty('ADBE Vector Shape - Ellipse');",
        ])

        if 's' in shape and 'k' in shape['s']:
            size_data = shape['s']['k']
            if isinstance(size_data, list) and len(size_data) >= 2:
                self.jsx_code.append(f"{ellipse_var}.property('Size').setValue([{size_data[0]}, {size_data[1]}]);")

    def process_rectangle_direct(self, shape: Dict, layer_var: str, index: int):
        """Process rectangle shape directly in layer"""
        rect_var = f"{layer_var}_rect_{index}"
        self.jsx_code.extend([
            f"var {rect_var} = {layer_var}.property('Contents').addProperty('ADBE Vector Shape - Rect');",
        ])

        if 's' in shape and 'k' in shape['s']:
            size_data = shape['s']['k']
            if isinstance(size_data, list) and len(size_data) >= 2:
                self.jsx_code.append(f"{rect_var}.property('Size').setValue([{size_data[0]}, {size_data[1]}]);")

    def process_path_direct(self, shape: Dict, layer_var: str, index: int):
        """Process path shape directly in layer"""
        path_var = f"{layer_var}_path_{index}"
        self.jsx_code.extend([
            f"var {path_var} = {layer_var}.property('Contents').addProperty('ADBE Vector Shape - Group');",
        ])

        if 'ks' in shape and 'k' in shape['ks']:
            path_data = shape['ks']['k']
            if isinstance(path_data, dict):
                self.set_static_path(path_var, path_data)
            elif isinstance(path_data, list):
                self.set_animated_path(path_var, path_data)

    def process_fill_direct(self, shape: Dict, layer_var: str, index: int):
        """Process fill directly in layer"""
        fill_var = f"{layer_var}_fill_{index}"
        self.jsx_code.extend([
            f"var {fill_var} = {layer_var}.property('Contents').addProperty('ADBE Vector Graphic - Fill');",
        ])

        if 'c' in shape and 'k' in shape['c']:
            color_data = shape['c']['k']
            if isinstance(color_data, list) and len(color_data) >= 3:
                color_str = f"[{color_data[0]:.6f}, {color_data[1]:.6f}, {color_data[2]:.6f}]"
                self.jsx_code.append(f"{fill_var}.property('Color').setValue({color_str});")

    def process_stroke_direct(self, shape: Dict, layer_var: str, index: int):
        """Process stroke directly in layer"""
        stroke_var = f"{layer_var}_stroke_{index}"
        self.jsx_code.extend([
            f"var {stroke_var} = {layer_var}.property('Contents').addProperty('ADBE Vector Graphic - Stroke');",
        ])

        if 'c' in shape and 'k' in shape['c']:
            color_data = shape['c']['k']
            if isinstance(color_data, list) and len(color_data) >= 3:
                color_str = f"[{color_data[0]:.6f}, {color_data[1]:.6f}, {color_data[2]:.6f}]"
                self.jsx_code.append(f"{stroke_var}.property('Color').setValue({color_str});")

        if 'w' in shape and 'k' in shape['w']:
            width_data = shape['w']['k']
            if isinstance(width_data, (int, float)):
                self.jsx_code.append(f"{stroke_var}.property('Stroke Width').setValue({width_data});")

    def process_trim_direct(self, shape: Dict, layer_var: str, index: int):
        """Process trim paths directly in layer"""
        trim_var = f"{layer_var}_trim_{index}"
        self.jsx_code.extend([
            f"var {trim_var} = {layer_var}.property('Contents').addProperty('ADBE Vector Filter - Trim');",
        ])

        if 's' in shape:
            self.set_property_keyframes(f"{trim_var}.property('Start')", shape['s'])
        if 'e' in shape:
            self.set_property_keyframes(f"{trim_var}.property('End')", shape['e'])
        if 'o' in shape:
            self.set_property_keyframes(f"{trim_var}.property('Offset')", shape['o'])

    def process_shape_transform_direct(self, shape: Dict, layer_var: str, index: int):
        """Process shape transform directly in layer"""
        # Shape transforms are handled differently - this is a placeholder
        self.jsx_code.append(f"// Shape transform {index} - TODO: implement")

    def process_rectangle_in_group(self, item: Dict, parent_var: str, index: int):
        """Process rectangle shape in group"""
        rect_var = f"{parent_var}_rect_{index}"
        self.jsx_code.extend([
            f"var {rect_var} = {parent_var}.property('Contents').addProperty('ADBE Vector Shape - Rect');",
        ])

        if 's' in item and 'k' in item['s']:
            size_data = item['s']['k']
            if isinstance(size_data, list) and len(size_data) >= 2:
                self.jsx_code.append(f"{rect_var}.property('Size').setValue([{size_data[0]}, {size_data[1]}]);")

        if 'p' in item and 'k' in item['p']:
            pos_data = item['p']['k']
            if isinstance(pos_data, list) and len(pos_data) >= 2:
                self.jsx_code.append(f"{rect_var}.property('Position').setValue([{pos_data[0]}, {pos_data[1]}]);")

    def process_transform_in_group(self, item: Dict, parent_var: str, index: int):
        """Process transform in group"""
        transform_var = f"{parent_var}_transform"
        self.jsx_code.append(f"var {transform_var} = {parent_var}.property('Transform');")

        # Set transform properties
        if 'p' in item:
            self.set_property_keyframes(f"{transform_var}.property('Position')", item['p'])
        if 's' in item:
            self.set_property_keyframes(f"{transform_var}.property('Scale')", item['s'])
        if 'r' in item:
            self.set_property_keyframes(f"{transform_var}.property('Rotation')", item['r'])
        if 'o' in item:
            self.set_property_keyframes(f"{transform_var}.property('Opacity')", item['o'])

    def process_shape_group(self, shape: Dict, layer_var: str, index: int):
        """Process shape group"""
        group_name = shape.get('nm', f'Group {index+1}')
        group_var = f"{layer_var}_group_{index}"

        self.jsx_code.extend([
            f"// Shape Group: {group_name}",
            f"var {group_var} = {layer_var}.property('Contents').addProperty('ADBE Vector Group');",
            f"{group_var}.name = '{group_name}';",
        ])

        # Process items in group
        if 'it' in shape:
            for item_index, item in enumerate(shape['it']):
                self.process_shape_item(item, group_var, item_index)

    def process_shape_item(self, item: Dict, parent_var: str, index: int):
        """Process individual shape items"""
        item_type = item.get('ty', '')

        if item_type == 'sh':  # Path
            self.process_path_in_group(item, parent_var, index)
        elif item_type == 'el':  # Ellipse
            self.process_ellipse_in_group(item, parent_var, index)
        elif item_type == 'rc':  # Rectangle
            self.process_rectangle_in_group(item, parent_var, index)
        elif item_type == 'fl':  # Fill
            self.process_fill_in_group(item, parent_var, index)
        elif item_type == 'st':  # Stroke
            self.process_stroke_in_group(item, parent_var, index)
        elif item_type == 'tm':  # Trim
            self.process_trim_in_group(item, parent_var, index)
        elif item_type == 'tr':  # Transform
            self.process_transform_in_group(item, parent_var, index)

    def process_path_in_group(self, item: Dict, parent_var: str, index: int):
        """Process path shape in group"""
        path_var = f"{parent_var}_path_{index}"

        self.jsx_code.extend([
            f"var {path_var} = {parent_var}.property('Contents').addProperty('ADBE Vector Shape - Group');",
        ])

        # Set path data
        if 'ks' in item and 'k' in item['ks']:
            path_data = item['ks']['k']
            if isinstance(path_data, dict):
                # Static path
                self.set_static_path(path_var, path_data)
            elif isinstance(path_data, list):
                # Animated path
                self.set_animated_path(path_var, path_data)

    def process_ellipse_in_group(self, item: Dict, parent_var: str, index: int):
        """Process ellipse shape in group"""
        ellipse_var = f"{parent_var}_ellipse_{index}"

        self.jsx_code.extend([
            f"var {ellipse_var} = {parent_var}.property('Contents').addProperty('ADBE Vector Shape - Ellipse');",
        ])

        # Set ellipse size
        if 's' in item and 'k' in item['s']:
            size_data = item['s']['k']
            if isinstance(size_data, list) and len(size_data) >= 2:
                self.jsx_code.append(f"{ellipse_var}.property('Size').setValue([{size_data[0]}, {size_data[1]}]);")

        # Set ellipse position
        if 'p' in item and 'k' in item['p']:
            pos_data = item['p']['k']
            if isinstance(pos_data, list) and len(pos_data) >= 2:
                self.jsx_code.append(f"{ellipse_var}.property('Position').setValue([{pos_data[0]}, {pos_data[1]}]);")

    def process_fill_in_group(self, item: Dict, parent_var: str, index: int):
        """Process fill in group"""
        fill_var = f"{parent_var}_fill_{index}"

        self.jsx_code.extend([
            f"var {fill_var} = {parent_var}.property('Contents').addProperty('ADBE Vector Graphic - Fill');",
        ])

        # Set fill color
        if 'c' in item and 'k' in item['c']:
            color_data = item['c']['k']
            if isinstance(color_data, list) and len(color_data) >= 3:
                color_str = f"[{color_data[0]:.6f}, {color_data[1]:.6f}, {color_data[2]:.6f}]"
                self.jsx_code.append(f"{fill_var}.property('Color').setValue({color_str});")

        # Set fill opacity
        if 'o' in item and 'k' in item['o']:
            opacity_data = item['o']['k']
            if isinstance(opacity_data, (int, float)):
                self.jsx_code.append(f"{fill_var}.property('Opacity').setValue({opacity_data});")

    def process_stroke_in_group(self, item: Dict, parent_var: str, index: int):
        """Process stroke in group"""
        stroke_var = f"{parent_var}_stroke_{index}"

        self.jsx_code.extend([
            f"var {stroke_var} = {parent_var}.property('Contents').addProperty('ADBE Vector Graphic - Stroke');",
        ])

        # Set stroke color
        if 'c' in item and 'k' in item['c']:
            color_data = item['c']['k']
            if isinstance(color_data, list) and len(color_data) >= 3:
                color_str = f"[{color_data[0]:.6f}, {color_data[1]:.6f}, {color_data[2]:.6f}]"
                self.jsx_code.append(f"{stroke_var}.property('Color').setValue({color_str});")

        # Set stroke width
        if 'w' in item and 'k' in item['w']:
            width_data = item['w']['k']
            if isinstance(width_data, (int, float)):
                self.jsx_code.append(f"{stroke_var}.property('Stroke Width').setValue({width_data});")

        # Set stroke opacity
        if 'o' in item and 'k' in item['o']:
            opacity_data = item['o']['k']
            if isinstance(opacity_data, (int, float)):
                self.jsx_code.append(f"{stroke_var}.property('Opacity').setValue({opacity_data});")

    def process_trim_in_group(self, item: Dict, parent_var: str, index: int):
        """Process trim paths in group"""
        trim_var = f"{parent_var}_trim_{index}"

        self.jsx_code.extend([
            f"var {trim_var} = {parent_var}.property('Contents').addProperty('ADBE Vector Filter - Trim');",
        ])

        # Set trim start
        if 's' in item:
            self.set_property_keyframes(f"{trim_var}.property('Start')", item['s'])

        # Set trim end
        if 'e' in item:
            self.set_property_keyframes(f"{trim_var}.property('End')", item['e'])

        # Set trim offset
        if 'o' in item:
            self.set_property_keyframes(f"{trim_var}.property('Offset')", item['o'])

    def set_static_path(self, path_var: str, path_data: Dict):
        """Set static path data"""
        if 'v' in path_data and 'i' in path_data and 'o' in path_data:
            vertices = path_data['v']
            in_tangents = path_data['i']
            out_tangents = path_data['o']
            closed = path_data.get('c', False)

            self.jsx_code.extend([
                f"var pathShape = new Shape();",
                f"pathShape.vertices = {vertices};",
                f"pathShape.inTangents = {in_tangents};",
                f"pathShape.outTangents = {out_tangents};",
                f"pathShape.closed = {str(closed).lower()};",
                f"{path_var}.property('Path').setValue(pathShape);",
            ])

    def set_animated_path(self, path_var: str, keyframes: List[Dict]):
        """Set animated path data with improved handling"""
        self.jsx_code.append(f"// Animated path with {len(keyframes)} keyframes")

        # Process all keyframes
        for i, kf in enumerate(keyframes):
            time = kf.get('t', 0)
            if 's' in kf:
                path_data = kf['s']
                if isinstance(path_data, list) and len(path_data) > 0:
                    # Handle case where 's' contains an array of path data
                    path_data = path_data[0]

                if isinstance(path_data, dict) and 'v' in path_data:
                    vertices = path_data['v']
                    in_tangents = path_data.get('i', [])
                    out_tangents = path_data.get('o', [])
                    closed = path_data.get('c', False)

                    self.jsx_code.extend([
                        f"var pathShape_{i} = new Shape();",
                        f"pathShape_{i}.vertices = {vertices};",
                        f"pathShape_{i}.inTangents = {in_tangents};",
                        f"pathShape_{i}.outTangents = {out_tangents};",
                        f"pathShape_{i}.closed = {str(closed).lower()};",
                        f"{path_var}.property('Path').setValueAtTime({time}/frameRate, pathShape_{i});",
                    ])
                else:
                    self.jsx_code.append(f"// Skipping keyframe {i} - invalid path data structure")
            else:
                self.jsx_code.append(f"// Skipping keyframe {i} - no 's' data")

    def set_property_keyframes(self, property_path: str, property_data: Dict):
        """Set keyframes for any property with smart keyframe detection"""
        # Skip if property_data is actually a list of raw keyframe objects
        if isinstance(property_data, list):
            # Check if it's a list of keyframe objects
            if len(property_data) > 0 and isinstance(property_data[0], dict) and 't' in property_data[0]:
                self.jsx_code.append(f"// Processing {len(property_data)} raw keyframes for {property_path}")
                for kf in property_data:
                    time = kf.get('t', 0)
                    value = kf.get('s', 0)

                    # Handle value properly - NEVER output keyframe objects
                    if isinstance(value, list):
                        if len(value) == 1:
                            value_str = str(value[0])
                        else:
                            value_str = f"[{', '.join(map(str, value))}]"
                    else:
                        value_str = str(value)

                    self.jsx_code.append(f"{property_path}.setValueAtTime({time}/frameRate, {value_str});")
            else:
                self.jsx_code.append(f"// Skipping invalid keyframe data for {property_path}")
            return

        # SMART KEYFRAME DETECTION - Check for keyframes regardless of 'a' flag
        if 'k' in property_data:
            k_data = property_data['k']

            # Smart detection: if 'k' contains a list with keyframe objects, treat as animated
            if isinstance(k_data, list) and len(k_data) > 0:
                # Check if first item looks like a keyframe (has 't' and 's')
                first_item = k_data[0]
                if isinstance(first_item, dict) and 't' in first_item and 's' in first_item:
                    # This is definitely a keyframe array - process all keyframes
                    self.jsx_code.append(f"// Smart detection: {len(k_data)} keyframes for {property_path}")

                    for kf in k_data:
                        time = kf.get('t', 0)
                        value = kf.get('s', 0)
                        in_easing = kf.get('i', None)
                        out_easing = kf.get('o', None)

                        # Handle value properly - NEVER output keyframe objects
                        if isinstance(value, list):
                            if len(value) == 1:
                                # Single value in array - extract it
                                value_str = str(value[0])
                            else:
                                # Multiple values - keep as array
                                value_str = f"[{', '.join(map(str, value))}]"
                        else:
                            value_str = str(value)

                        # Use easing-aware keyframe setting with MATHEMATICAL CONTEXT
                        if in_easing or out_easing:
                            # Calculate context for mathematical optimization
                            value_delta = self.calculate_value_delta(k_data)
                            time_delta = self.calculate_time_delta(k_data, 24)  # Default frame rate

                            # Convert easing objects to proper JavaScript object notation
                            in_easing_str = self.format_easing_object(in_easing) if in_easing else "null"
                            out_easing_str = self.format_easing_object(out_easing) if out_easing else "null"
                            self.jsx_code.append(f"setKeyframeWithEasing({property_path}, {time}, {value_str}, {in_easing_str}, {out_easing_str}, frameRate, {value_delta:.2f}, {time_delta:.3f});")
                        else:
                            self.jsx_code.append(f"{property_path}.setValueAtTime({time}/frameRate, {value_str});")
                    return

        # Handle static values and other cases
        if 'k' in property_data:
            k_data = property_data['k']

            # Not keyframes, treat as static array value
            if isinstance(k_data, list):
                if len(k_data) == 1:
                    # Single value in array - extract it for single-dimension properties
                    if 'Position' in property_path and ('X Position' in property_path or 'Y Position' in property_path):
                        value_str = str(k_data[0])
                    else:
                        value_str = f"[{', '.join(map(str, k_data))}]"
                else:
                    # Multiple values - keep as array
                    value_str = f"[{', '.join(map(str, k_data))}]"

                self.jsx_code.append(f"{property_path}.setValue({value_str});")
                return

            # Single keyframe object in 'k' - extract the value
            elif isinstance(k_data, dict) and 's' in k_data:
                # This is a keyframe object, extract the value
                value = k_data['s']
                if isinstance(value, list):
                    if len(value) == 1:
                        value_str = str(value[0])
                    else:
                        value_str = f"[{', '.join(map(str, value))}]"
                else:
                    value_str = str(value)
                self.jsx_code.append(f"{property_path}.setValue({value_str});")
                return

            # Regular static value
            else:
                value_str = str(k_data)
                self.jsx_code.append(f"{property_path}.setValue({value_str});")
                return

        # Fallback: check legacy 'a' flag method (for compatibility)
        if 'a' in property_data and property_data['a'] == 1:
            # Animated property with explicit flag
            if 'k' in property_data and isinstance(property_data['k'], list):
                keyframes = property_data['k']
                self.jsx_code.append(f"// Legacy animated: {len(keyframes)} keyframes for {property_path}")

                for kf in keyframes:
                    time = kf.get('t', 0)
                    value = kf.get('s', 0)
                    in_easing = kf.get('i', None)
                    out_easing = kf.get('o', None)

                    # Handle single values vs arrays properly - NEVER output keyframe objects
                    if isinstance(value, list):
                        if len(value) == 1:
                            # Single value in array - extract it
                            value_str = str(value[0])
                        else:
                            # Multiple values - keep as array
                            value_str = f"[{', '.join(map(str, value))}]"
                    else:
                        value_str = str(value)

                    # Use easing-aware keyframe setting with MATHEMATICAL CONTEXT
                    if in_easing or out_easing:
                        # Calculate context for mathematical optimization
                        value_delta = self.calculate_value_delta(keyframes)
                        time_delta = self.calculate_time_delta(keyframes, 24)  # Default frame rate

                        # Convert easing objects to proper JavaScript object notation
                        in_easing_str = self.format_easing_object(in_easing) if in_easing else "null"
                        out_easing_str = self.format_easing_object(out_easing) if out_easing else "null"
                        self.jsx_code.append(f"setKeyframeWithEasing({property_path}, {time}, {value_str}, {in_easing_str}, {out_easing_str}, frameRate, {value_delta:.2f}, {time_delta:.3f});")
                    else:
                        self.jsx_code.append(f"{property_path}.setValueAtTime({time}/frameRate, {value_str});")

        # If we get here, no valid data was found
        self.jsx_code.append(f"// No valid keyframe data found for {property_path}")

    def format_easing_object(self, easing_data) -> str:
        """Convert Python easing data to JavaScript object notation"""
        if not easing_data:
            return "null"

        if isinstance(easing_data, dict):
            # Lottie easing format: {"x": 0.667, "y": 1}
            x = easing_data.get('x', 0)
            y = easing_data.get('y', 0)
            return f"{{x: {x}, y: {y}}}"
        elif isinstance(easing_data, list):
            # Handle array format - could be single values or arrays for multi-dimensional
            if len(easing_data) >= 2:
                # Check if it's an array of arrays (multi-dimensional)
                if isinstance(easing_data[0], list) and isinstance(easing_data[1], list):
                    # Multi-dimensional: [[x1, x2, x3], [y1, y2, y3]]
                    x_values = easing_data[0]
                    y_values = easing_data[1]
                    return f"{{x: [{', '.join(map(str, x_values))}], y: [{', '.join(map(str, y_values))}]}}"
                else:
                    # Single dimension: [x, y]
                    return f"{{x: {easing_data[0]}, y: {easing_data[1]}}}"
            else:
                # Not enough values
                return "null"
        else:
            # Fallback
            return "null"

    def set_layer_properties(self, layer: Dict, layer_var: str):
        """Set common layer properties"""
        # Set layer timing
        if 'ip' in layer:
            in_point = layer['ip'] / 24.0  # Convert frames to seconds
            self.jsx_code.append(f"{layer_var}.inPoint = {in_point:.6f};")

        if 'op' in layer:
            out_point = layer['op'] / 24.0  # Convert frames to seconds
            self.jsx_code.append(f"{layer_var}.outPoint = {out_point:.6f};")

        if 'st' in layer:
            start_time = layer['st'] / 24.0  # Convert frames to seconds
            self.jsx_code.append(f"{layer_var}.startTime = {start_time:.6f};")

        # Set layer visibility
        if 'hd' in layer and layer['hd']:
            self.jsx_code.append(f"{layer_var}.enabled = false;")

        # Set blend mode
        if 'bm' in layer and layer['bm'] != 0:
            blend_mode = self.get_blend_mode(layer['bm'])
            self.jsx_code.append(f"{layer_var}.blendingMode = {blend_mode};")

        # Set parent
        if 'parent' in layer:
            parent_index = layer['parent']
            if parent_index in self.layer_references:
                parent_var = self.layer_references[parent_index]
                self.jsx_code.append(f"{layer_var}.parent = {parent_var};")
            else:
                self.jsx_code.append(f"// Warning: Parent layer {parent_index} not found for {layer_var}")

        # Track matte setup is handled centrally after all layers are created
        if 'td' in layer and layer['td'] == 1:
            # This layer is a track matte (provides the matte)
            self.jsx_code.append(f"// {layer_var} is a track matte")
            # IMPORTANT: Hide track matte layers immediately
            self.jsx_code.append(f"{layer_var}.enabled = false; // Hide matte layer")
        elif 'tt' in layer:
            # This layer uses a track matte - will be set up later
            self.jsx_code.append(f"// {layer_var} uses track matte - will be configured later")

        # Set transform properties
        if 'ks' in layer:
            self.set_transform_properties(layer['ks'], layer_var)

        # NOTE: Effects are processed in layer-specific creation methods
        # This avoids duplicate effect creation

    def set_transform_properties(self, transform: Dict, layer_var: str):
        """Set layer transform properties"""
        transform_group = f"{layer_var}.property('Transform')"

        # Opacity
        if 'o' in transform:
            self.set_property_keyframes(f"{transform_group}.property('Opacity')", transform['o'])

        # Position - ALWAYS use combined position for After Effects compatibility
        if 'p' in transform:
            pos_data = transform['p']

            # Check if position has separate dimensions in the JSON
            if 's' in pos_data and pos_data['s'] == 1:
                # Separate dimensions in JSON - need to combine them for AE
                self.jsx_code.append(f"// Position has separate dimensions - combining for AE")

                # Extract X and Y keyframes and combine them
                x_keyframes = []
                y_keyframes = []
                static_x_value = 0
                static_y_value = 0

                if 'x' in pos_data and 'k' in pos_data['x']:
                    x_data = pos_data['x']['k']
                    if isinstance(x_data, list):
                        x_keyframes = x_data
                    else:
                        # Static X value - don't create keyframes, just store the value
                        x_keyframes = []
                        static_x_value = x_data

                if 'y' in pos_data and 'k' in pos_data['y']:
                    y_data = pos_data['y']['k']
                    if isinstance(y_data, list):
                        y_keyframes = y_data
                    else:
                        # Static Y value - don't create keyframes, just store the value
                        y_keyframes = []
                        static_y_value = y_data

                # Combine keyframes by time
                combined_keyframes = {}

                for kf in x_keyframes:
                    time = kf.get('t', 0)
                    if time not in combined_keyframes:
                        # Initialize with static Y value if Y is not animated
                        y_val = static_y_value if len(y_keyframes) <= 1 else 0
                        combined_keyframes[time] = {
                            't': time,
                            's': [0, y_val],
                            'i': kf.get('i', None),
                            'o': kf.get('o', None)
                        }

                    # Extract value properly - handle arrays and single values
                    x_value = kf.get('s', 0)
                    if isinstance(x_value, list) and len(x_value) > 0:
                        x_value = x_value[0]  # Extract from array
                    combined_keyframes[time]['s'][0] = x_value
                    # Preserve easing from X keyframe
                    if 'i' in kf:
                        combined_keyframes[time]['i'] = kf['i']
                    if 'o' in kf:
                        combined_keyframes[time]['o'] = kf['o']

                for kf in y_keyframes:
                    time = kf.get('t', 0)
                    if time not in combined_keyframes:
                        # Initialize with static X value if X is not animated
                        x_val = static_x_value if len(x_keyframes) <= 1 else 0
                        combined_keyframes[time] = {
                            't': time,
                            's': [x_val, 0],
                            'i': kf.get('i', None),
                            'o': kf.get('o', None)
                        }

                    # Extract value properly - handle arrays and single values
                    y_value = kf.get('s', 0)
                    if isinstance(y_value, list) and len(y_value) > 0:
                        y_value = y_value[0]  # Extract from array
                    combined_keyframes[time]['s'][1] = y_value
                    # Preserve easing from Y keyframe (Y easing takes precedence if both exist)
                    if 'i' in kf:
                        combined_keyframes[time]['i'] = kf['i']
                    if 'o' in kf:
                        combined_keyframes[time]['o'] = kf['o']

                # Fill in missing Y values for X keyframes when Y is static
                if len(y_keyframes) <= 1:
                    for time in combined_keyframes:
                        combined_keyframes[time]['s'][1] = static_y_value

                # Fill in missing X values for Y keyframes when X is static
                if len(x_keyframes) <= 1:
                    for time in combined_keyframes:
                        combined_keyframes[time]['s'][0] = static_x_value

                # Set combined keyframes with easing
                for time in sorted(combined_keyframes.keys()):
                    kf = combined_keyframes[time]
                    pos_value = kf['s']
                    in_easing = kf.get('i', None)
                    out_easing = kf.get('o', None)

                    # Use easing-aware keyframe setting for position with MATHEMATICAL CONTEXT
                    if in_easing or out_easing:
                        # Calculate context for mathematical optimization (use combined keyframes)
                        combined_kf_list = list(combined_keyframes.values())
                        value_delta = self.calculate_value_delta(combined_kf_list)
                        time_delta = self.calculate_time_delta(combined_kf_list, 24)  # Default frame rate

                        # Convert easing objects to proper JavaScript object notation
                        in_easing_str = self.format_easing_object(in_easing) if in_easing else "null"
                        out_easing_str = self.format_easing_object(out_easing) if out_easing else "null"
                        self.jsx_code.append(f"setKeyframeWithEasing({transform_group}.property('Position'), {time}, [{pos_value[0]}, {pos_value[1]}], {in_easing_str}, {out_easing_str}, frameRate, {value_delta:.2f}, {time_delta:.3f});")
                    else:
                        self.jsx_code.append(f"{transform_group}.property('Position').setValueAtTime({time}/frameRate, [{pos_value[0]}, {pos_value[1]}]);")

            else:
                # Combined position - use as-is
                self.set_property_keyframes(f"{transform_group}.property('Position')", pos_data)

        # Scale
        if 's' in transform:
            self.set_property_keyframes(f"{transform_group}.property('Scale')", transform['s'])

        # Rotation
        if 'r' in transform:
            self.set_property_keyframes(f"{transform_group}.property('Rotation')", transform['r'])

        # Anchor Point
        if 'a' in transform:
            self.set_property_keyframes(f"{transform_group}.property('Anchor Point')", transform['a'])





    def get_blend_mode(self, bm_value: int) -> str:
        """Convert blend mode value to AE constant"""
        blend_modes = {
            0: "BlendingMode.NORMAL",
            1: "BlendingMode.MULTIPLY",
            2: "BlendingMode.SCREEN",
            3: "BlendingMode.OVERLAY",
            # Add more as needed
        }
        return blend_modes.get(bm_value, "BlendingMode.NORMAL")

    def get_track_matte_type(self, tt_value: int) -> str:
        """Convert track matte type to AE constant"""
        matte_types = {
            1: "TrackMatteType.ALPHA",
            2: "TrackMatteType.ALPHA_INVERTED",
            3: "TrackMatteType.LUMA",
            4: "TrackMatteType.LUMA_INVERTED",
        }
        return matte_types.get(tt_value, "TrackMatteType.NO_TRACK_MATTE")

    def setup_track_mattes(self, layers: List[Dict]):
        """Set up track matte relationships after all layers are created"""
        self.jsx_code.append("// Set up track matte relationships")

        # Find all track matte relationships
        track_matte_pairs = []
        for layer in layers:
            if 'tt' in layer and 'tp' in layer:  # Layer uses track matte
                layer_index = layer.get('ind', 0)
                matte_index = layer.get('tp', 0)
                matte_type = layer.get('tt', 0)
                track_matte_pairs.append((matte_index, layer_index, matte_type))

        # Set up track matte relationships
        for matte_index, layer_index, matte_type in track_matte_pairs:
            matte_var = f"layer_{matte_index}"
            layer_var = f"layer_{layer_index}"
            matte_type_str = self.get_track_matte_type(matte_type)

            self.jsx_code.extend([
                f"// Set up track matte: {layer_var} uses {matte_var} as {matte_type_str}",
                f"try {{",
                f"    // Ensure track matte layer is directly above the layer that uses it",
                f"    {matte_var}.moveBefore({layer_var});",
                f"    // Set the track matte type",
                f"    {layer_var}.trackMatteType = {matte_type_str};",
                f"    // IMPORTANT: Hide the matte layer (turn off visibility)",
                f"    {matte_var}.enabled = false;",
                f"}} catch(e) {{",
                f"    // Track matte setup failed for {layer_var}",
                f"    alert('Track matte setup failed for {layer_var}: ' + e.toString());",
                f"}}",
            ])

        if track_matte_pairs:
            self.jsx_code.append("")

    @staticmethod
    def find_json_files() -> List[str]:
        """Find all JSON files in the current directory"""
        json_files = []
        for file in os.listdir('.'):
            if file.lower().endswith('.json'):
                json_files.append(file)
        return sorted(json_files)

    @staticmethod
    def interactive_file_selection() -> Optional[str]:
        """Interactive file selection with enhanced user experience"""
        print("🎬 INTELLIGENT LOTTIE TO JSX CONVERTER")
        print("=" * 50)
        print()

        # Find JSON files
        json_files = LottieToJSXConverter.find_json_files()

        if not json_files:
            print("❌ No JSON files found in the current directory.")
            print("💡 Please ensure your Lottie/Bodymovin JSON files are in this folder.")
            return None

        print(f"📁 Found {len(json_files)} JSON file(s):")
        print()

        # Display files with analysis
        for i, file in enumerate(json_files, 1):
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                name = data.get('nm', 'Unnamed')
                width = data.get('w', 0)
                height = data.get('h', 0)
                duration = data.get('op', 0) / data.get('fr', 24)
                layers = len(data.get('layers', []))
                assets = len(data.get('assets', []))

                print(f"  {i}. {file}")
                print(f"     📋 Name: {name}")
                print(f"     📐 Size: {width}x{height}")
                print(f"     ⏱️  Duration: {duration:.1f}s")
                print(f"     🎭 Layers: {layers}")
                print(f"     📦 Assets: {assets}")
                print()

            except Exception as e:
                print(f"  {i}. {file} (⚠️  Error reading: {str(e)})")
                print()

        # Get user selection
        while True:
            try:
                choice = input(f"🔍 Select a file to convert (1-{len(json_files)}) or 'q' to quit: ").strip()

                if choice.lower() == 'q':
                    print("👋 Goodbye!")
                    return None

                index = int(choice) - 1
                if 0 <= index < len(json_files):
                    selected_file = json_files[index]
                    print(f"✅ Selected: {selected_file}")
                    return selected_file
                else:
                    print(f"❌ Please enter a number between 1 and {len(json_files)}")

            except ValueError:
                print("❌ Please enter a valid number or 'q' to quit")
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                return None

    def analyze_and_convert_with_smart_prompts(self, json_file: str) -> bool:
        """Analyze JSON file and convert with intelligent user guidance"""
        print(f"\n🔍 ANALYZING: {json_file}")
        print("-" * 40)

        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except Exception as e:
            print(f"❌ Error reading JSON file: {e}")
            return False

        # Analyze assets
        assets = data.get('assets', [])
        external_assets = []

        for asset in assets:
            if 'u' in asset and 'p' in asset:
                asset_type = 'unknown'
                if 'e' in asset:
                    if asset['e'] == 0:
                        asset_type = 'image'
                    elif asset['e'] == 1:
                        asset_type = 'video'
                elif 't' in asset and asset['t'] == 2:
                    asset_type = 'audio'

                if asset_type != 'unknown':
                    external_assets.append({
                        'id': asset.get('id', ''),
                        'name': asset.get('nm', ''),
                        'type': asset_type,
                        'path': asset.get('u', '') + asset.get('p', ''),
                        'width': asset.get('w', 0),
                        'height': asset.get('h', 0)
                    })

        # Show analysis results
        print(f"📊 ANALYSIS RESULTS:")
        print(f"   • Animation: {data.get('nm', 'Unnamed')}")
        print(f"   • Dimensions: {data.get('w', 0)}x{data.get('h', 0)}")
        print(f"   • Duration: {data.get('op', 0) / data.get('fr', 24):.1f} seconds")
        print(f"   • Layers: {len(data.get('layers', []))}")
        print(f"   • External Assets: {len(external_assets)}")

        if external_assets:
            print(f"\n📦 EXTERNAL ASSETS DETECTED:")
            for asset in external_assets:
                print(f"   • {asset['name']} ({asset['type']}) - {asset['path']}")
                if asset['width'] and asset['height']:
                    print(f"     Size: {asset['width']}x{asset['height']}")

        print(f"\n💡 CONVERSION NOTES:")
        print(f"   • Missing assets will prompt file dialogs during JSX execution")
        print(f"   • You can browse and select replacement files")
        print(f"   • Placeholders will be created for skipped assets")

        # Generate output filename
        base_name = os.path.splitext(json_file)[0]
        output_file = f"{base_name}_converted.jsx"

        print(f"\n🚀 CONVERTING...")
        print(f"   Input:  {json_file}")
        print(f"   Output: {output_file}")

        # Convert
        try:
            jsx_script = self.convert_json_to_jsx(json_file, output_file)
            print(f"\n✅ CONVERSION SUCCESSFUL!")
            print(f"   📄 Generated: {output_file}")
            print(f"   📏 Lines: {jsx_script.count(chr(10)) + 1:,}")
            return True

        except Exception as e:
            print(f"\n❌ CONVERSION FAILED: {e}")
            return False

    def add_footer(self, data: Dict):
        """Add JSX script footer"""
        name = data.get('nm', 'Animation')
        duration = data.get('op', 120) / data.get('fr', 24)
        layer_count = len(data.get('layers', []))
        asset_count = len(data.get('assets', []))

        self.jsx_code.extend([
            "",
            "// Animation creation complete",
            f"alert('Animation \"{name}\" created successfully!\\n' +",
            f"      'Duration: {duration:.2f} seconds\\n' +",
            f"      'Layers: {layer_count}\\n' +",
            f"      'Assets: {asset_count}');",
            "",
            "app.endUndoGroup();",
        ])

def main():
    """Main function for interactive conversion"""
    try:
        converter = LottieToJSXConverter()

        # Interactive file selection
        selected_file = converter.interactive_file_selection()

        if selected_file:
            # Analyze and convert with smart prompts
            success = converter.analyze_and_convert_with_smart_prompts(selected_file)

            if success:
                print(f"\n🎉 READY TO USE!")
                print(f"   1. Open After Effects")
                print(f"   2. Go to File > Scripts > Run Script File...")
                print(f"   3. Select the generated .jsx file")
                print(f"   4. Follow the smart file dialogs for missing assets")
                print(f"\n🎬 Your animation will be created with intelligent asset handling!")
            else:
                print(f"\n� Conversion failed. Please check the JSON file and try again.")

    except KeyboardInterrupt:
        print(f"\n� Conversion cancelled by user.")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        print(f"Please check your JSON file and try again.")

if __name__ == "__main__":
    main()
