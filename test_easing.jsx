// Simple test to verify KeyframeEase functionality
app.beginUndoGroup("Test Easing");

// Create test composition
var comp = app.project.items.addComp('Test Easing', 1920, 1080, 1.0, 5, 30);

// Create test layer
var layer = comp.layers.addSolid([1, 0, 0], 'Test Layer', 100, 100, 1.0);

// Set initial position keyframe
layer.property('Transform').property('Position').setValueAtTime(0, [100, 100]);

// Set second position keyframe
layer.property('Transform').property('Position').setValueAtTime(1, [500, 500]);

// Test basic KeyframeEase creation
try {
    var testEase = new KeyframeEase(25, 50);
    alert('KeyframeEase created successfully: speed=' + testEase.speed + ', influence=' + testEase.influence);
    
    // Try to apply easing to keyframe
    var keyIndex = layer.property('Transform').property('Position').nearestKeyIndex(1);
    layer.property('Transform').property('Position').setTemporalEaseAtKey(keyIndex, [testEase, testEase], [testEase, testEase]);
    
    alert('Easing applied successfully to keyframe ' + keyIndex);
    
} catch(e) {
    alert('ERROR: ' + e.toString());
}

app.endUndoGroup();
