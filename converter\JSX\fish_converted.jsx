// 08 - Converted from Lottie JSON
// Original Bodymovin version: 5.12.1
// Generated by <PERSON><PERSON> to JSX Converter

// Disable undo for performance
app.beginUndoGroup('Create Animation');

// Helper Functions
function setKeyframes(property, keyframes, frameRate) {
    if (!keyframes || !keyframes.length) return;
    
    for (var i = 0; i < keyframes.length; i++) {
        var kf = keyframes[i];
        var time = kf.t / frameRate;
        
        property.setValueAtTime(time, kf.s);
        
        // Set easing if available
        if (kf.i && kf.o) {
            var keyIndex = property.nearestKeyIndex(time);
            try {
                // Convert Lottie easing to After Effects easing
                var inEase = convertLottieEasing(kf.i);
                var outEase = convertLottieEasing(kf.o);
                property.setTemporalEaseAtKey(keyIndex, inEase, outEase);
            } catch(e) {
                // Ignore easing errors
            }
        }
    }
}

function convertLottieEasing(easing) {
    // MATHEMATICALLY PERFECT Lottie easing to After Effects conversion
    // Uses advanced bezier curve analysis and optimization algorithms
    if (!easing || typeof easing !== 'object') return new KeyframeEase(0, 33.33);
    
    var x = easing.x || 0;
    var y = easing.y || 0;
    
    // MATHEMATICAL CURVE ANALYSIS
    // Analyze bezier curve characteristics for intelligent conversion
    var steepness = calculateSteepness(x, y);
    var curveType = classifyCurveType(x, y, steepness);
    
    // INTELLIGENT PARAMETER SELECTION based on curve analysis
    var speed, influence;
    
    if (curveType === 'sharp_spike') {
        // High speed, low influence for sharp transitions
        speed = 200 + (steepness * 300);
        influence = 15 + (steepness * 10);
    } else if (curveType === 'gentle_ease') {
        // Moderate speed, high influence for smooth transitions
        speed = 30 + (steepness * 40);
        influence = 60 + (Math.abs(x - 0.5) * 30); // Symmetry factor
    } else if (curveType === 'overshoot') {
        // Variable speed based on overshoot amount
        var overshoot = Math.max(0, y - 1) + Math.max(0, -y);
        speed = 100 + (overshoot * 200);
        influence = 40 + (overshoot * 40);
    } else if (curveType === 'linear') {
        // Minimal easing for linear curves
        speed = 10;
        influence = 33.33;
    } else {
        // MATHEMATICAL OPTIMIZATION for moderate curves
        // X coordinate influences temporal extent (influence)
        var xInfluenceFactor = 0.5 + (x * 1.5); // Map 0-1 to 0.5-2.0
        influence = Math.max(0.1, Math.min(100, 50 * xInfluenceFactor));
        
        // Y coordinate influences velocity (speed)
        var ySpeedFactor = 0.3 + (y * 2.0); // Map 0-1 to 0.3-2.3
        var steepnessFactor = 1 + (steepness * 0.8);
        speed = Math.max(0.1, Math.min(1000, 50 * ySpeedFactor * steepnessFactor));
    }
    
    // MATHEMATICAL CONSTRAINTS - ensure parameters work well together
    if (influence > 80 && speed < 20) {
        speed = Math.max(speed, 30); // High influence needs minimum speed
    }
    if (speed > 500 && influence > 80) {
        influence = Math.min(influence, 60); // Very high speed needs limited influence
    }
    
    return new KeyframeEase(speed, influence);
}

function calculateSteepness(x, y) {
    // Calculate curve steepness from control points
    // Higher values indicate steeper curves
    var slope = Math.abs(y / Math.max(0.001, x)); // Avoid division by zero
    return Math.min(1.0, slope / 5.0); // Normalize to 0-1
}

function classifyCurveType(x, y, steepness) {
    // Classify curve type based on mathematical analysis
    if (y > 1.3 || y < -0.3) return 'overshoot';
    if (steepness > 0.8) return 'sharp_spike';
    if (steepness < 0.2 && Math.abs(x - 0.5) < 0.3) return 'gentle_ease';
    if (steepness < 0.1) return 'linear';
    return 'moderate_ease';
}

function setKeyframeWithEasing(property, time, value, inEasing, outEasing, frameRate, valueDelta, timeDelta) {
    // SIMPLE MATHEMATICAL CONVERSION - Direct bezier to speed/influence mapping
    property.setValueAtTime(time / frameRate, value);
    
    if (inEasing && outEasing) {
        try {
            var keyIndex = property.nearestKeyIndex(time / frameRate);
            
            if (keyIndex >= 1 && keyIndex <= property.numKeys) {
                // MATHEMATICAL EXTRACTION from JSON bezier values
                var inSpeed = calculateSpeedFromBezier(inEasing, valueDelta, timeDelta);
                var outSpeed = calculateSpeedFromBezier(outEasing, valueDelta, timeDelta);
                var inInfluence = calculateInfluenceFromBezier(inEasing);
                var outInfluence = calculateInfluenceFromBezier(outEasing);
                
                var inKeyEase = new KeyframeEase(inSpeed, inInfluence);
                var outKeyEase = new KeyframeEase(outSpeed, outInfluence);
                
                var propType = property.propertyValueType;
                
                if (propType == PropertyValueType.ThreeD_SPATIAL || propType == PropertyValueType.TwoD_SPATIAL) {
                    property.setTemporalEaseAtKey(keyIndex, [inKeyEase], [outKeyEase]);
                } else if (propType == PropertyValueType.ThreeD) {
                    property.setTemporalEaseAtKey(keyIndex, [inKeyEase, inKeyEase, inKeyEase], [outKeyEase, outKeyEase, outKeyEase]);
                } else if (propType == PropertyValueType.TwoD) {
                    property.setTemporalEaseAtKey(keyIndex, [inKeyEase, inKeyEase], [outKeyEase, outKeyEase]);
                } else {
                    property.setTemporalEaseAtKey(keyIndex, [inKeyEase], [outKeyEase]);
                }
            }
        } catch(e) {
            // Silently handle easing errors
        }
    }
}

// SIMPLE MATHEMATICAL CONVERSION FUNCTIONS
function calculateSpeedFromBezier(easing, valueDelta, timeDelta) {
    // Direct mathematical extraction from bezier coordinates
    if (!easing || typeof easing !== 'object') return 33.33;
    
    var x = easing.x || 0;
    var y = easing.y || 0;
    
    // Mathematical formula: speed based on Y coordinate and value change
    var baseSpeed = Math.abs(y) * 100; // Y coordinate determines speed intensity
    
    // Scale by value delta (larger changes need higher speeds)
    var valueScale = Math.sqrt(Math.abs(valueDelta) / 100);
    valueScale = Math.max(0.5, Math.min(3.0, valueScale));
    
    // Scale by time delta (shorter time needs higher speeds)
    var timeScale = Math.sqrt(1 / Math.max(0.1, timeDelta));
    timeScale = Math.max(0.5, Math.min(2.0, timeScale));
    
    var finalSpeed = baseSpeed * valueScale * timeScale;
    
    // Clamp to reasonable range
    return Math.max(1, Math.min(1000, finalSpeed));
}

function calculateInfluenceFromBezier(easing) {
    // Direct mathematical extraction from bezier coordinates
    if (!easing || typeof easing !== 'object') return 33.33;
    
    var x = easing.x || 0;
    var y = easing.y || 0;
    
    // Mathematical formula: influence based on X coordinate
    var baseInfluence = x * 100; // X coordinate determines temporal influence
    
    // Adjust based on Y coordinate for better curve matching
    var yAdjustment = Math.abs(y - 0.5) * 20;
    baseInfluence += yAdjustment;
    
    // Clamp to valid range
    return Math.max(0.1, Math.min(100, baseInfluence));
}


function convertLottieEasingAdvanced(easing, valueDelta, timeDelta, isIncoming) {
    // ADVANCED mathematical conversion with context awareness
    if (!easing || typeof easing !== 'object') return new KeyframeEase(0, 33.33);
    
    var x = easing.x || 0;
    var y = easing.y || 0;
    
    // Use default values if context not provided
    valueDelta = valueDelta || 100;
    timeDelta = timeDelta || 1;
    
    // MATHEMATICAL CURVE ANALYSIS
    var steepness = calculateSteepness(x, y);
    var curveType = classifyCurveType(x, y, steepness);
    
    // BASE PARAMETERS from curve analysis
    var baseSpeed, baseInfluence;
    
    if (curveType === 'sharp_spike') {
        baseSpeed = 200 + (steepness * 300);
        baseInfluence = 15 + (steepness * 10);
    } else if (curveType === 'gentle_ease') {
        baseSpeed = 30 + (steepness * 40);
        baseInfluence = 60 + (Math.abs(x - 0.5) * 30);
    } else if (curveType === 'overshoot') {
        var overshoot = Math.max(0, y - 1) + Math.max(0, -y);
        baseSpeed = 100 + (overshoot * 200);
        baseInfluence = 40 + (overshoot * 40);
    } else if (curveType === 'linear') {
        baseSpeed = 10;
        baseInfluence = 33.33;
    } else {
        // Mathematical optimization for moderate curves
        var xInfluenceFactor = 0.5 + (x * 1.5);
        baseInfluence = Math.max(0.1, Math.min(100, 50 * xInfluenceFactor));
        var ySpeedFactor = 0.3 + (y * 2.0);
        var steepnessFactor = 1 + (steepness * 0.8);
        baseSpeed = Math.max(0.1, Math.min(1000, 50 * ySpeedFactor * steepnessFactor));
    }
    
    // CONTEXT-AWARE SCALING
    var valueScale = Math.sqrt(Math.abs(valueDelta) / 100);
    valueScale = Math.max(0.1, Math.min(5.0, valueScale));
    
    var timeScale = Math.sqrt(1 / timeDelta);
    timeScale = Math.max(0.1, Math.min(3.0, timeScale));
    
    // Apply scaling
    var finalSpeed = baseSpeed * valueScale * timeScale;
    var finalInfluence = baseInfluence * (1 + (steepness * 0.3));
    
    // Adjust for incoming vs outgoing
    if (isIncoming) {
        finalSpeed *= 0.9;
        finalInfluence *= 1.1;
    }
    
    // MATHEMATICAL CONSTRAINTS
    if (finalInfluence > 80 && finalSpeed < 20) {
        finalSpeed = Math.max(finalSpeed, 30);
    }
    if (finalSpeed > 500 && finalInfluence > 80) {
        finalInfluence = Math.min(finalInfluence, 60);
    }
    
    // Clamp final values
    finalSpeed = Math.max(0.1, Math.min(1000, finalSpeed));
    finalInfluence = Math.max(0.1, Math.min(100, finalInfluence));
    
    return new KeyframeEase(finalSpeed, finalInfluence);
}

function createSolidLayer(comp, name, color, width, height) {
    return comp.layers.addSolid(color, name, width, height, 1.0);
}

function createShapeLayer(comp, name) {
    var layer = comp.layers.addShape();
    layer.name = name;
    return layer;
}

function createNullLayer(comp, name) {
    var layer = comp.layers.addNull();
    layer.name = name;
    return layer;
}

function createTextLayer(comp, name) {
    var layer = comp.layers.addText(name);
    return layer;
}

function createPrecomp(comp, name, refId) {
    // This would reference a precomp - simplified for now
    return createNullLayer(comp, name + ' (Precomp)');
}

function rgbToAE(r, g, b, a) {
    a = a || 1;
    return [r, g, b, a];
}

function bezierToAE(bezierData) {
    var shape = new Shape();
    if (bezierData.v && bezierData.v.length > 0) {
        shape.vertices = bezierData.v;
        if (bezierData.i) shape.inTangents = bezierData.i;
        if (bezierData.o) shape.outTangents = bezierData.o;
        shape.closed = bezierData.c || false;
    }
    return shape;
}

// === INTELLIGENT ASSET IMPORT WITH CONTEXT-AWARE DIALOGS ===
function importAssetWithDialog(comp, assetId, originalPath, assetType, assetName, dimensions, description) {
    // Try original path first
    if (originalPath && originalPath !== '') {
        var originalFile = new File(originalPath);
        if (originalFile.exists) {
            return importAssetFile(comp, assetId, originalFile, assetName);
        }
    }
    
    // Build hyper-intelligent dialog with detailed context
    var message = '🚨 MISSING ASSET REQUIRED\n';
    message += '════════════════════════════════════════\n\n';
    
    // Asset identification section
    message += '📋 WHAT FILE IS NEEDED:\n';
    message += '• Asset Name: "' + (assetName || assetId) + '"\n';
    message += '• File Type: ' + assetType.toUpperCase() + '\n';
    message += '• Asset ID: ' + assetId + '\n';
    
    if (dimensions && dimensions !== '') {
        message += '• Expected Size: ' + dimensions + ' pixels\n';
    }
    
    message += '• Original Path: ' + (originalPath || 'Not specified') + '\n';
    
    // Context and purpose section
    if (description && description !== '') {
        message += '\n🎯 PURPOSE & CONTEXT:\n';
        message += '• ' + description + '\n';
    }
    
    // Smart guidance based on asset type and name
    message += '\n💡 WHAT TO LOOK FOR:\n';
    if (assetType === 'image') {
        message += '• Look for PNG, JPG, or other image files\n';
        if (assetName && assetName.toLowerCase().indexOf('logo') !== -1) {
            message += '• This should be your company/brand logo\n';
            message += '• Usually a PNG with transparent background\n';
        } else if (assetName && assetName.toLowerCase().indexOf('background') !== -1) {
            message += '• This should be a background image\n';
            message += '• Usually covers the full composition size\n';
        } else if (assetName && assetName.toLowerCase().indexOf('placeholder') !== -1) {
            message += '• This is a placeholder - replace with your image\n';
            message += '• Can be any image that fits your design\n';
        }
    } else if (assetType === 'audio') {
        message += '• Look for MP3, WAV, or other audio files\n';
        if (assetName && assetName.toLowerCase().indexOf('music') !== -1) {
            message += '• This should be background music\n';
            message += '• Usually a longer audio track\n';
        } else {
            message += '• This should be a sound effect or audio clip\n';
            message += '• Usually a shorter audio file\n';
        }
    } else if (assetType === 'video') {
        message += '• Look for MP4, MOV, or other video files\n';
        message += '• Should match the expected dimensions if specified\n';
    }
    
    message += '\n📂 NEXT STEPS:\n';
    message += '• Click OK to open file browser and locate the file\n';
    message += '• Click Cancel to create a placeholder instead\n';
    message += '\n⚠️  If you can\'t find the exact file, choose a similar one or cancel for placeholder.';
    
    var userChoice = confirm(message);
    if (!userChoice) {
        return createPlaceholderLayer(comp, assetId, assetName, assetType, dimensions);
    }
    
    // Show file dialog with smart filters and detailed title
    var fileFilter = getSmartFileFilter(assetType);
    var dialogTitle = 'SELECT ' + assetType.toUpperCase() + ': ' + (assetName || assetId);
    if (description) {
        dialogTitle += ' (' + description + ')';
    }
    
    var selectedFile = File.openDialog(dialogTitle, fileFilter);
    
    if (selectedFile && selectedFile.exists) {
        return importAssetFile(comp, assetId, selectedFile, assetName);
    } else {
        alert('❌ No file selected. Creating placeholder layer instead.');
        return createPlaceholderLayer(comp, assetId, assetName, assetType, dimensions);
    }
}

function importAssetFile(comp, assetId, file, assetName) {
    try {
        var importOptions = new ImportOptions(file);
        var footage = app.project.importFile(importOptions);
        var layer = comp.layers.add(footage);
        layer.name = assetName || assetId;
        return layer;
    } catch (e) {
        alert('Error importing file: ' + file.fsName + '\nError: ' + e.toString() + '\nCreating placeholder layer.');
        return createNullLayer(comp, (assetName || assetId) + ' (Import Error)');
    }
}

function createPlaceholderLayer(comp, assetId, assetName, assetType, dimensions) {
    var placeholderName = (assetName || assetId) + ' [MISSING ' + assetType.toUpperCase() + ']';
    
    // Create different placeholder types based on asset type
    if (assetType === 'image') {
        // Create a colored solid as image placeholder
        var width = 500, height = 500;
        if (dimensions) {
            var parts = dimensions.split('x');
            if (parts.length === 2) {
                width = parseInt(parts[0]) || 500;
                height = parseInt(parts[1]) || 500;
            }
        }
        var placeholder = comp.layers.addSolid([0.8, 0.2, 0.2], placeholderName, width, height, 1.0);
        return placeholder;
    } else if (assetType === 'audio') {
        // Create null layer for audio placeholder
        var placeholder = comp.layers.addNull();
        placeholder.name = placeholderName;
        return placeholder;
    } else if (assetType === 'video') {
        // Create solid for video placeholder
        var placeholder = comp.layers.addSolid([0.2, 0.2, 0.8], placeholderName, 1920, 1080, 1.0);
        return placeholder;
    } else {
        // Generic null layer placeholder
        var placeholder = comp.layers.addNull();
        placeholder.name = placeholderName;
        return placeholder;
    }
}

function getSmartFileFilter(assetType) {
    switch (assetType.toLowerCase()) {
        case 'image':
            return 'Image Files:*.png;*.jpg;*.jpeg;*.gif;*.bmp;*.tiff;*.tga;*.psd;*.ai;*.eps';
        case 'video':
            return 'Video Files:*.mp4;*.mov;*.avi;*.wmv;*.flv;*.webm;*.mkv;*.m4v;*.3gp';
        case 'audio':
            return 'Audio Files:*.mp3;*.wav;*.aac;*.ogg;*.flac;*.m4a;*.wma;*.aiff';
        default:
            return 'All Files:*.*';
    }
}

function getFileTypesForAsset(assetType) {
    switch (assetType.toLowerCase()) {
        case 'image':
            return 'Images:*.png;*.jpg;*.jpeg;*.gif;*.bmp;*.tiff;*.psd';
        case 'video':
            return 'Videos:*.mp4;*.mov;*.avi;*.wmv;*.flv;*.webm;*.mkv';
        case 'audio':
            return 'Audio:*.mp3;*.wav;*.aac;*.ogg;*.flac;*.m4a';
        default:
            return 'All Files:*.*';
    }
}

// Create main composition: 08
var comp = app.project.items.addComp('08', 1080, 1080, 1.0, 6.000, 25);
var frameRate = 25;

// Asset References and Precomps

// Creating 12 layers
// Reversing JSON layer order for correct After Effects stacking
// Bg (index 12)
// Water Dot (index 11)
// Water Dot 5 (index 10)
// Water Dot 2 (index 9)
// Water Dot 3 (index 8)
// Water Dot 4 (index 7)
// Fether (index 6)
// Fether (index 5)
// Fether (index 4)
// Fether (index 3)
// Body (index 2)
// Eyes (index 1)
// Layer 12: Bg
var layer_12 = createShapeLayer(comp, 'Bg');
// Shape Group: Group 1
var layer_12_group_0 = layer_12.property('Contents').addProperty('ADBE Vector Group');
layer_12_group_0.name = 'Group 1';
var layer_12_group_0_path_0 = layer_12_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-539.819, 540], [539.819, 540], [539.819, -540], [-539.819, -540]];
pathShape.inTangents = [[0, 0], [0, 0], [0, 0], [0, 0]];
pathShape.outTangents = [[0, 0], [0, 0], [0, 0], [0, 0]];
pathShape.closed = true;
layer_12_group_0_path_0.property('Path').setValue(pathShape);
var layer_12_group_0_fill_1 = layer_12_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_12_group_0_fill_1.property('Color').setValue([0.156863, 0.431373, 0.552941]);
layer_12_group_0_fill_1.property('Opacity').setValue(100);
var layer_12_group_0_transform = layer_12_group_0.property('Transform');
layer_12_group_0_transform.property('Position').setValue([540.069, 540.25]);
layer_12_group_0_transform.property('Scale').setValue([100, 100]);
layer_12_group_0_transform.property('Rotation').setValue(0);
layer_12_group_0_transform.property('Opacity').setValue(100);
layer_12.inPoint = 0.000000;
layer_12.outPoint = 6.250000;
layer_12.startTime = 0.000000;
layer_12.property('Transform').property('Opacity').setValue(100);
layer_12.property('Transform').property('Position').setValue([540.181, 540, 0]);
layer_12.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_12.property('Transform').property('Rotation').setValue(0);
layer_12.property('Transform').property('Anchor Point').setValue([540.069, 540.25, 0]);

// Layer 2: Body
var layer_2 = createShapeLayer(comp, 'Body');
// Shape Group: Group 1
var layer_2_group_0 = layer_2.property('Contents').addProperty('ADBE Vector Group');
layer_2_group_0.name = 'Group 1';
var layer_2_group_0_path_0 = layer_2_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-36.419, 25.396], [36.419, -1.124], [-12.761, -18.71]];
pathShape.inTangents = [[2.841, -18.456], [-23.116, 12.057], [19.564, -6.686]];
pathShape.outTangents = [[24.042, -5.226], [-9.696, -9.105], [-14.408, 4.925]];
pathShape.closed = true;
layer_2_group_0_path_0.property('Path').setValue(pathShape);
var layer_2_group_0_fill_1 = layer_2_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_2_group_0_fill_1.property('Color').setValue([0.356863, 0.282353, 0.529412]);
layer_2_group_0_fill_1.property('Opacity').setValue(100);
var layer_2_group_0_transform = layer_2_group_0.property('Transform');
layer_2_group_0_transform.property('Position').setValue([167.172, 232.867]);
layer_2_group_0_transform.property('Scale').setValue([100, 100]);
layer_2_group_0_transform.property('Rotation').setValue(0);
layer_2_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_2_group_1 = layer_2.property('Contents').addProperty('ADBE Vector Group');
layer_2_group_1.name = 'Group 2';
var layer_2_group_1_path_0 = layer_2_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[3.023, 22.897], [30.71, -92.632], [20.454, -93.944], [11.628, -94.419]];
pathShape.inTangents = [[-4.767, 71.522], [-6.69, 14.766], [3.497, 0.356], [2.869, 0.013]];
pathShape.outTangents = [[3.866, -58.01], [-3.342, -0.518], [-3.013, -0.304], [-42.338, 95.022]];
pathShape.closed = true;
layer_2_group_1_path_0.property('Path').setValue(pathShape);
var layer_2_group_1_fill_1 = layer_2_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_2_group_1_fill_1.property('Color').setValue([0.356863, 0.282353, 0.529412]);
layer_2_group_1_fill_1.property('Opacity').setValue(100);
var layer_2_group_1_transform = layer_2_group_1.property('Transform');
layer_2_group_1_transform.property('Position').setValue([106.861, 95.302]);
layer_2_group_1_transform.property('Scale').setValue([100, 100]);
layer_2_group_1_transform.property('Rotation').setValue(0);
layer_2_group_1_transform.property('Opacity').setValue(100);
// Shape Group: Group 3
var layer_2_group_2 = layer_2.property('Contents').addProperty('ADBE Vector Group');
layer_2_group_2.name = 'Group 3';
var layer_2_group_2_path_0 = layer_2_group_2.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[10.54, -72.743], [-8.21, 29.049], [27.687, -57.245], [28.879, -62.488]];
pathShape.inTangents = [[6.552, 3.113], [-10.5, 43.694], [0, 0], [0, 0]];
pathShape.outTangents = [[-39.418, 78.83], [10.7, -44.528], [0, 0], [-5.685, -3.693]];
pathShape.closed = true;
layer_2_group_2_path_0.property('Path').setValue(pathShape);
var layer_2_group_2_fill_1 = layer_2_group_2.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_2_group_2_fill_1.property('Color').setValue([0.356863, 0.282353, 0.529412]);
layer_2_group_2_fill_1.property('Opacity').setValue(100);
var layer_2_group_2_transform = layer_2_group_2.property('Transform');
layer_2_group_2_transform.property('Position').setValue([174.702, 89.842]);
layer_2_group_2_transform.property('Scale').setValue([100, 100]);
layer_2_group_2_transform.property('Rotation').setValue(0);
layer_2_group_2_transform.property('Opacity').setValue(100);
// Shape Group: Group 4
var layer_2_group_3 = layer_2.property('Contents').addProperty('ADBE Vector Group');
layer_2_group_3.name = 'Group 4';
var layer_2_group_3_path_0 = layer_2_group_3.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[17.115, 77.31], [19.514, 74.829], [-7.611, -26.187], [19.593, -74.992], [17.035, -77.31], [-10.902, -27.224], [-14.397, 22.619]];
pathShape.inTangents = [[-17.028, -16.469], [0, 0], [-11.983, 38.032], [-0.182, 0.199], [0, 0], [9.198, -29.19], [-3.105, -15.861]];
pathShape.outTangents = [[0, 0], [-29.981, -28.997], [8.973, -28.477], [0, 0], [-0.187, 0.205], [-5.423, 17.213], [3.88, 19.822]];
pathShape.closed = true;
layer_2_group_3_path_0.property('Path').setValue(pathShape);
var layer_2_group_3_fill_1 = layer_2_group_3.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_2_group_3_fill_1.property('Color').setValue([0.356863, 0.282353, 0.529412]);
layer_2_group_3_fill_1.property('Opacity').setValue(100);
var layer_2_group_3_transform = layer_2_group_3.property('Transform');
layer_2_group_3_transform.property('Position').setValue([212.725, 130.839]);
layer_2_group_3_transform.property('Scale').setValue([100, 100]);
layer_2_group_3_transform.property('Rotation').setValue(0);
layer_2_group_3_transform.property('Opacity').setValue(100);
// Shape Group: Group 5
var layer_2_group_4 = layer_2.property('Contents').addProperty('ADBE Vector Group');
layer_2_group_4.name = 'Group 5';
var layer_2_group_4_path_0 = layer_2_group_4.property('Contents').addProperty('ADBE Vector Shape - Group');
// Animated path with 9 keyframes
var pathShape_0 = new Shape();
pathShape_0.vertices = [[-87.012, 122.331], [-9.707, 125.534], [63.131, 99.016], [120.595, 55.564], [89.028, 27.758], [133.87, 34.46], [135.623, -4.001], [63.119, -105.376], [44.781, -115.629], [-2.891, -130.059], [-13.146, -131.371], [-21.972, -131.846]];
pathShape_0.inTangents = [[-53.198, -32.66], [-31.943, 6.944], [-23.116, 12.056], [-13.783, 16.51], [0.179, 0.332], [-9.489, 0.008], [4.588, 13.105], [57.968, 37.69], [6.552, 3.113], [18.045, 2.786], [3.496, 0.356], [2.868, 0.014]];
pathShape_0.outTangents = [[16.31, 10.01], [24.041, -5.225], [23.123, -12.061], [-21.535, -9.534], [16.637, 5.868], [5.268, -12.399], [0, 0], [-5.684, -3.693], [-13.82, -6.566], [-3.341, -0.518], [-3.013, -0.303], [-113.962, -0.632]];
pathShape_0.closed = true;
layer_2_group_4_path_0.property('Path').setValueAtTime(25/frameRate, pathShape_0);
var pathShape_1 = new Shape();
pathShape_1.vertices = [[-87.012, 122.331], [-9.707, 125.534], [63.131, 99.016], [128.618, 43.316], [89.028, 27.758], [132.132, 37.548], [135.623, -4.001], [63.119, -105.376], [44.781, -115.629], [-2.891, -130.059], [-13.146, -131.371], [-21.972, -131.846]];
pathShape_1.inTangents = [[-53.198, -32.66], [-31.943, 6.944], [-23.116, 12.056], [-13.783, 16.51], [0.179, 0.332], [-9.489, 0.008], [4.588, 13.105], [57.968, 37.69], [6.552, 3.113], [18.045, 2.786], [3.496, 0.356], [2.868, 0.014]];
pathShape_1.outTangents = [[16.31, 10.01], [24.041, -5.225], [23.123, -12.061], [-21.535, -9.534], [16.637, 5.868], [5.268, -12.399], [0, 0], [-5.684, -3.693], [-13.82, -6.566], [-3.341, -0.518], [-3.013, -0.303], [-113.962, -0.632]];
pathShape_1.closed = true;
layer_2_group_4_path_0.property('Path').setValueAtTime(35/frameRate, pathShape_1);
var pathShape_2 = new Shape();
pathShape_2.vertices = [[-87.012, 122.331], [-9.707, 125.534], [63.131, 99.016], [120.595, 55.564], [89.028, 27.758], [133.87, 34.46], [135.623, -4.001], [63.119, -105.376], [44.781, -115.629], [-2.891, -130.059], [-13.146, -131.371], [-21.972, -131.846]];
pathShape_2.inTangents = [[-53.198, -32.66], [-31.943, 6.944], [-23.116, 12.056], [-13.783, 16.51], [0.179, 0.332], [-9.489, 0.008], [4.588, 13.105], [57.968, 37.69], [6.552, 3.113], [18.045, 2.786], [3.496, 0.356], [2.868, 0.014]];
pathShape_2.outTangents = [[16.31, 10.01], [24.041, -5.225], [23.123, -12.061], [-21.535, -9.534], [16.637, 5.868], [5.268, -12.399], [0, 0], [-5.684, -3.693], [-13.82, -6.566], [-3.341, -0.518], [-3.013, -0.303], [-113.962, -0.632]];
pathShape_2.closed = true;
layer_2_group_4_path_0.property('Path').setValueAtTime(45/frameRate, pathShape_2);
var pathShape_3 = new Shape();
pathShape_3.vertices = [[-87.012, 122.331], [-9.707, 125.534], [63.131, 99.016], [120.595, 55.564], [89.028, 27.758], [133.87, 34.46], [135.623, -4.001], [63.119, -105.376], [44.781, -115.629], [-2.891, -130.059], [-13.146, -131.371], [-21.972, -131.846]];
pathShape_3.inTangents = [[-53.198, -32.66], [-31.943, 6.944], [-23.116, 12.056], [-13.783, 16.51], [0.179, 0.332], [-9.489, 0.008], [4.588, 13.105], [57.968, 37.69], [6.552, 3.113], [18.045, 2.786], [3.496, 0.356], [2.868, 0.014]];
pathShape_3.outTangents = [[16.31, 10.01], [24.041, -5.225], [23.123, -12.061], [-21.535, -9.534], [16.637, 5.868], [5.268, -12.399], [0, 0], [-5.684, -3.693], [-13.82, -6.566], [-3.341, -0.518], [-3.013, -0.303], [-113.962, -0.632]];
pathShape_3.closed = true;
layer_2_group_4_path_0.property('Path').setValueAtTime(75/frameRate, pathShape_3);
var pathShape_4 = new Shape();
pathShape_4.vertices = [[-87.012, 122.331], [-9.707, 125.534], [63.131, 99.016], [128.618, 43.316], [89.028, 27.758], [132.132, 37.548], [135.623, -4.001], [63.119, -105.376], [44.781, -115.629], [-2.891, -130.059], [-13.146, -131.371], [-21.972, -131.846]];
pathShape_4.inTangents = [[-53.198, -32.66], [-31.943, 6.944], [-23.116, 12.056], [-13.783, 16.51], [0.179, 0.332], [-9.489, 0.008], [4.588, 13.105], [57.968, 37.69], [6.552, 3.113], [18.045, 2.786], [3.496, 0.356], [2.868, 0.014]];
pathShape_4.outTangents = [[16.31, 10.01], [24.041, -5.225], [23.123, -12.061], [-21.535, -9.534], [16.637, 5.868], [5.268, -12.399], [0, 0], [-5.684, -3.693], [-13.82, -6.566], [-3.341, -0.518], [-3.013, -0.303], [-113.962, -0.632]];
pathShape_4.closed = true;
layer_2_group_4_path_0.property('Path').setValueAtTime(85/frameRate, pathShape_4);
var pathShape_5 = new Shape();
pathShape_5.vertices = [[-87.012, 122.331], [-9.707, 125.534], [63.131, 99.016], [120.595, 55.564], [89.028, 27.758], [133.87, 34.46], [135.623, -4.001], [63.119, -105.376], [44.781, -115.629], [-2.891, -130.059], [-13.146, -131.371], [-21.972, -131.846]];
pathShape_5.inTangents = [[-53.198, -32.66], [-31.943, 6.944], [-23.116, 12.056], [-13.783, 16.51], [0.179, 0.332], [-9.489, 0.008], [4.588, 13.105], [57.968, 37.69], [6.552, 3.113], [18.045, 2.786], [3.496, 0.356], [2.868, 0.014]];
pathShape_5.outTangents = [[16.31, 10.01], [24.041, -5.225], [23.123, -12.061], [-21.535, -9.534], [16.637, 5.868], [5.268, -12.399], [0, 0], [-5.684, -3.693], [-13.82, -6.566], [-3.341, -0.518], [-3.013, -0.303], [-113.962, -0.632]];
pathShape_5.closed = true;
layer_2_group_4_path_0.property('Path').setValueAtTime(95/frameRate, pathShape_5);
var pathShape_6 = new Shape();
pathShape_6.vertices = [[-87.012, 122.331], [-9.707, 125.534], [63.131, 99.016], [120.595, 55.564], [89.028, 27.758], [133.87, 34.46], [135.623, -4.001], [63.119, -105.376], [44.781, -115.629], [-2.891, -130.059], [-13.146, -131.371], [-21.972, -131.846]];
pathShape_6.inTangents = [[-53.198, -32.66], [-31.943, 6.944], [-23.116, 12.056], [-13.783, 16.51], [0.179, 0.332], [-9.489, 0.008], [4.588, 13.105], [57.968, 37.69], [6.552, 3.113], [18.045, 2.786], [3.496, 0.356], [2.868, 0.014]];
pathShape_6.outTangents = [[16.31, 10.01], [24.041, -5.225], [23.123, -12.061], [-21.535, -9.534], [16.637, 5.868], [5.268, -12.399], [0, 0], [-5.684, -3.693], [-13.82, -6.566], [-3.341, -0.518], [-3.013, -0.303], [-113.962, -0.632]];
pathShape_6.closed = true;
layer_2_group_4_path_0.property('Path').setValueAtTime(125/frameRate, pathShape_6);
var pathShape_7 = new Shape();
pathShape_7.vertices = [[-87.012, 122.331], [-9.707, 125.534], [63.131, 99.016], [128.618, 43.316], [89.028, 27.758], [132.132, 37.548], [135.623, -4.001], [63.119, -105.376], [44.781, -115.629], [-2.891, -130.059], [-13.146, -131.371], [-21.972, -131.846]];
pathShape_7.inTangents = [[-53.198, -32.66], [-31.943, 6.944], [-23.116, 12.056], [-13.783, 16.51], [0.179, 0.332], [-9.489, 0.008], [4.588, 13.105], [57.968, 37.69], [6.552, 3.113], [18.045, 2.786], [3.496, 0.356], [2.868, 0.014]];
pathShape_7.outTangents = [[16.31, 10.01], [24.041, -5.225], [23.123, -12.061], [-21.535, -9.534], [16.637, 5.868], [5.268, -12.399], [0, 0], [-5.684, -3.693], [-13.82, -6.566], [-3.341, -0.518], [-3.013, -0.303], [-113.962, -0.632]];
pathShape_7.closed = true;
layer_2_group_4_path_0.property('Path').setValueAtTime(135/frameRate, pathShape_7);
var pathShape_8 = new Shape();
pathShape_8.vertices = [[-87.012, 122.331], [-9.707, 125.534], [63.131, 99.016], [120.595, 55.564], [89.028, 27.758], [133.87, 34.46], [135.623, -4.001], [63.119, -105.376], [44.781, -115.629], [-2.891, -130.059], [-13.146, -131.371], [-21.972, -131.846]];
pathShape_8.inTangents = [[-53.198, -32.66], [-31.943, 6.944], [-23.116, 12.056], [-13.783, 16.51], [0.179, 0.332], [-9.489, 0.008], [4.588, 13.105], [57.968, 37.69], [6.552, 3.113], [18.045, 2.786], [3.496, 0.356], [2.868, 0.014]];
pathShape_8.outTangents = [[16.31, 10.01], [24.041, -5.225], [23.123, -12.061], [-21.535, -9.534], [16.637, 5.868], [5.268, -12.399], [0, 0], [-5.684, -3.693], [-13.82, -6.566], [-3.341, -0.518], [-3.013, -0.303], [-113.962, -0.632]];
pathShape_8.closed = true;
layer_2_group_4_path_0.property('Path').setValueAtTime(145/frameRate, pathShape_8);
var layer_2_group_4_fill_1 = layer_2_group_4.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_2_group_4_fill_1.property('Color').setValue([0.494118, 0.466667, 0.800000]);
layer_2_group_4_fill_1.property('Opacity').setValue(100);
var layer_2_group_4_transform = layer_2_group_4.property('Transform');
layer_2_group_4_transform.property('Position').setValue([140.46, 132.729]);
layer_2_group_4_transform.property('Scale').setValue([100, 100]);
layer_2_group_4_transform.property('Rotation').setValue(0);
layer_2_group_4_transform.property('Opacity').setValue(100);
layer_2.inPoint = 0.000000;
layer_2.outPoint = 6.250000;
layer_2.startTime = 0.000000;
layer_2.property('Transform').property('Opacity').setValue(100);
// Smart detection: 150 keyframes for layer_2.property('Transform').property('Position')
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 0, [247.758, 572.691, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 1, [251.754, 571.095, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 2, [256.467, 569.551, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 3, [261.702, 567.986, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 4, [267.26, 566.354, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 5, [272.965, 564.621, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 6, [278.656, 562.771, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 7, [284.186, 560.802, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 8, [289.426, 558.728, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 9, [294.259, 556.58, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 10, [298.587, 554.405, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 11, [302.327, 552.263, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 12, [305.409, 550.235, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 13, [308.124, 548.317, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 14, [310.944, 546.465, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 15, [313.935, 544.731, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 16, [317.14, 543.153, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 17, [320.582, 541.752, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 18, [324.263, 540.54, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 19, [328.166, 539.509, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 20, [332.251, 538.642, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 21, [336.46, 537.904, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 22, [340.714, 537.247, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 23, [344.913, 536.609, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 24, [348.936, 535.913, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 25, [352.708, 535.053, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 26, [356.663, 533.878, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 27, [360.941, 532.426, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 28, [365.56, 530.769, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 29, [370.516, 528.977, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 30, [375.785, 527.12, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 31, [381.323, 525.264, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 32, [387.061, 523.477, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 33, [392.911, 521.824, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 34, [398.765, 520.367, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 35, [404.492, 519.17, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 36, [409.939, 518.293, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 37, [414.934, 517.796, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 38, [419.194, 517.498, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 39, [422.678, 517.138, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 40, [425.611, 516.743, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 41, [428.199, 516.343, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 42, [430.626, 515.962, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 43, [433.06, 515.624, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 44, [435.646, 515.348, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 45, [438.512, 515.153, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 46, [441.764, 515.052, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 47, [445.488, 515.06, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 48, [449.753, 515.185, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 49, [454.606, 515.435, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 50, [460.029, 515.784, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 51, [465.625, 515.992, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 52, [471.187, 516.007, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 53, [476.605, 515.835, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 54, [481.799, 515.495, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 55, [486.718, 515.014, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 56, [491.342, 514.431, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 57, [495.678, 513.795, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 58, [499.768, 513.167, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 59, [503.678, 512.617, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 60, [507.508, 512.224, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 61, [511.387, 512.082, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 62, [515.472, 512.291, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 63, [519.736, 513.159, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 64, [523.99, 514.79, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 65, [528.252, 516.907, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 66, [532.536, 519.266, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 67, [536.852, 521.658, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 68, [541.205, 523.91, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 69, [545.596, 525.885, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 70, [550.021, 527.482, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 71, [554.472, 528.634, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 72, [558.939, 529.312, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 73, [563.404, 529.521, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 74, [567.847, 529.301, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 75, [572.255, 528.752, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 76, [576.704, 528.172, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 77, [581.21, 527.72, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 78, [585.769, 527.487, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 79, [590.374, 527.536, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 80, [595.015, 527.902, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 81, [599.682, 528.589, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 82, [604.362, 529.573, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 83, [609.039, 530.8, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 84, [613.695, 532.189, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 85, [618.312, 533.628, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 86, [622.869, 534.976, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 87, [627.341, 536.065, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 88, [631.641, 537.002, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 89, [635.695, 538.085, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 90, [639.533, 539.286, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 91, [643.188, 540.58, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 92, [646.698, 541.939, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 93, [650.105, 543.338, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 94, [653.454, 544.75, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 95, [656.796, 546.151, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 96, [660.185, 547.516, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 97, [663.677, 548.82, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 98, [667.336, 550.041, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 99, [671.228, 551.155, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 100, [675.426, 552.137, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 101, [679.949, 552.969, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 102, [684.646, 553.69, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 103, [689.371, 554.343, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 104, [694.009, 554.964, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 105, [698.469, 555.587, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 106, [702.692, 556.237, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 107, [706.643, 556.935, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 108, [710.318, 557.699, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 109, [713.737, 558.538, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 110, [716.951, 559.459, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 111, [720.038, 560.461, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 112, [723.103, 561.54, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 113, [726.31, 562.633, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 114, [729.769, 563.746, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 115, [733.499, 564.975, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 116, [737.502, 566.392, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 117, [741.762, 568.041, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 118, [746.25, 569.94, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 119, [750.919, 572.078, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 120, [755.706, 574.417, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 121, [760.532, 576.894, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 122, [765.304, 579.416, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 123, [769.91, 581.865, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 124, [774.223, 584.093, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 125, [778.174, 585.963, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 126, [782.245, 587.593, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 127, [786.552, 588.921, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 128, [791.086, 589.859, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 129, [795.829, 590.354, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 130, [800.753, 590.385, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 131, [805.818, 589.967, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 132, [810.974, 589.145, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 133, [816.162, 588.002, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 134, [821.313, 586.651, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 135, [826.347, 585.242, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 136, [831.173, 583.956, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 137, [835.694, 583.009, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 138, [839.673, 582.387, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 139, [843.053, 581.86, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 140, [846.045, 581.43, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 141, [848.836, 581.09, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 142, [851.585, 580.819, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 143, [854.424, 580.582, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 144, [857.455, 580.335, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 145, [860.753, 580.02, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 146, [864.367, 579.566, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 147, [868.317, 578.893, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 148, [872.595, 577.908, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 149, [877.182, 576.498, 0], {x: 1, y: 1}, {x: 0, y: 0}, frameRate, 425.68, 0.042);
layer_2.property('Transform').property('Scale').setValue([139, 139, 100]);
// Smart detection: 3 keyframes for layer_2.property('Transform').property('Rotation')
setKeyframeWithEasing(layer_2.property('Transform').property('Rotation'), 0, 0, {x: [0.667], y: [1]}, {x: [0.333], y: [0]}, frameRate, 12.00, 3.104);
setKeyframeWithEasing(layer_2.property('Transform').property('Rotation'), 75, -12, {x: [0.667], y: [1]}, {x: [0.333], y: [0]}, frameRate, 12.00, 3.104);
layer_2.property('Transform').property('Rotation').setValueAtTime(149/frameRate, 0);
layer_2.property('Transform').property('Anchor Point').setValue([140.461, 132.728, 0]);

// Layer 11: Water Dot
var layer_11 = createShapeLayer(comp, 'Water Dot');
// Shape Group: Group 3
var layer_11_group_0 = layer_11.property('Contents').addProperty('ADBE Vector Group');
layer_11_group_0.name = 'Group 3';
var layer_11_group_0_path_0 = layer_11_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[8.304, 0], [0, 8.304], [-8.304, 0], [0, -8.304]];
pathShape.inTangents = [[0, -4.586], [4.586, 0], [0, 4.586], [-4.587, 0]];
pathShape.outTangents = [[0, 4.586], [-4.587, 0], [0, -4.586], [4.586, 0]];
pathShape.closed = true;
layer_11_group_0_path_0.property('Path').setValue(pathShape);
var layer_11_group_0_fill_1 = layer_11_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_11_group_0_fill_1.property('Color').setValue([1.000000, 1.000000, 1.000000]);
layer_11_group_0_fill_1.property('Opacity').setValue(100);
var layer_11_group_0_transform = layer_11_group_0.property('Transform');
layer_11_group_0_transform.property('Position').setValue([28.279, 74.042]);
layer_11_group_0_transform.property('Scale').setValue([100, 100]);
layer_11_group_0_transform.property('Rotation').setValue(0);
layer_11_group_0_transform.property('Opacity').setValue(100);
layer_11.inPoint = 0.000000;
layer_11.outPoint = 6.250000;
layer_11.startTime = 0.000000;
layer_11.parent = layer_2;
layer_11.property('Transform').property('Opacity').setValue(100);
// Smart detection: 2 keyframes for layer_11.property('Transform').property('Position')
setKeyframeWithEasing(layer_11.property('Transform').property('Position'), 0, [266.63, 165.048, 0], {x: 0.667, y: 1}, {x: 0.333, y: 0}, frameRate, 327.02, 2.000);
layer_11.property('Transform').property('Position').setValueAtTime(48.0009765625/frameRate, [635.009, -84.463, 0]);
// Smart detection: 3 keyframes for layer_11.property('Transform').property('Scale')
setKeyframeWithEasing(layer_11.property('Transform').property('Scale'), 0, [0, 0, 100], {x: [0.667, 0.667, 0.667], y: [1, 1, 1]}, {x: [0.333, 0.333, 0.333], y: [0, 0, 0]}, frameRate, 192.39, 1.042);
setKeyframeWithEasing(layer_11.property('Transform').property('Scale'), 33.333, [136.043, 136.043, 100], {x: [0.667, 0.667, 0.667], y: [1, 1, 1]}, {x: [0.333, 0.333, 0.333], y: [0, 0, 0]}, frameRate, 192.39, 1.042);
layer_11.property('Transform').property('Scale').setValueAtTime(50/frameRate, [0, 0, 100]);
layer_11.property('Transform').property('Rotation').setValue(0.789);
layer_11.property('Transform').property('Anchor Point').setValue([28.279, 74.042, 0]);

// Layer 10: Water Dot 5
var layer_10 = createShapeLayer(comp, 'Water Dot 5');
// Shape Group: Group 3
var layer_10_group_0 = layer_10.property('Contents').addProperty('ADBE Vector Group');
layer_10_group_0.name = 'Group 3';
var layer_10_group_0_path_0 = layer_10_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[8.304, 0], [0, 8.304], [-8.304, 0], [0, -8.304]];
pathShape.inTangents = [[0, -4.586], [4.586, 0], [0, 4.586], [-4.587, 0]];
pathShape.outTangents = [[0, 4.586], [-4.587, 0], [0, -4.586], [4.586, 0]];
pathShape.closed = true;
layer_10_group_0_path_0.property('Path').setValue(pathShape);
var layer_10_group_0_fill_1 = layer_10_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_10_group_0_fill_1.property('Color').setValue([1.000000, 1.000000, 1.000000]);
layer_10_group_0_fill_1.property('Opacity').setValue(100);
var layer_10_group_0_transform = layer_10_group_0.property('Transform');
layer_10_group_0_transform.property('Position').setValue([28.279, 74.042]);
layer_10_group_0_transform.property('Scale').setValue([100, 100]);
layer_10_group_0_transform.property('Rotation').setValue(0);
layer_10_group_0_transform.property('Opacity').setValue(100);
layer_10.inPoint = 0.000000;
layer_10.outPoint = 6.250000;
layer_10.startTime = 0.000000;
layer_10.parent = layer_2;
layer_10.property('Transform').property('Opacity').setValue(100);
// Smart detection: 2 keyframes for layer_10.property('Transform').property('Position')
setKeyframeWithEasing(layer_10.property('Transform').property('Position'), 0, [262.4, 193.512, 0], {x: 0.667, y: 1}, {x: 0.333, y: 0}, frameRate, 307.22, 2.000);
layer_10.property('Transform').property('Position').setValueAtTime(48.0009765625/frameRate, [630.778, -55.999, 0]);
// Smart detection: 3 keyframes for layer_10.property('Transform').property('Scale')
setKeyframeWithEasing(layer_10.property('Transform').property('Scale'), 0, [0, 0, 100], {x: [0.667, 0.667, 0.667], y: [1, 1, 1]}, {x: [0.333, 0.333, 0.333], y: [0, 0, 0]}, frameRate, 192.39, 1.042);
setKeyframeWithEasing(layer_10.property('Transform').property('Scale'), 33.333, [136.043, 136.043, 100], {x: [0.667, 0.667, 0.667], y: [1, 1, 1]}, {x: [0.333, 0.333, 0.333], y: [0, 0, 0]}, frameRate, 192.39, 1.042);
layer_10.property('Transform').property('Scale').setValueAtTime(50/frameRate, [0, 0, 100]);
layer_10.property('Transform').property('Rotation').setValue(0.789);
layer_10.property('Transform').property('Anchor Point').setValue([28.279, 74.042, 0]);

// Layer 9: Water Dot 2
var layer_9 = createShapeLayer(comp, 'Water Dot 2');
// Shape Group: Group 3
var layer_9_group_0 = layer_9.property('Contents').addProperty('ADBE Vector Group');
layer_9_group_0.name = 'Group 3';
var layer_9_group_0_path_0 = layer_9_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[8.304, 0], [0, 8.304], [-8.304, 0], [0, -8.304]];
pathShape.inTangents = [[0, -4.586], [4.586, 0], [0, 4.586], [-4.587, 0]];
pathShape.outTangents = [[0, 4.586], [-4.587, 0], [0, -4.586], [4.586, 0]];
pathShape.closed = true;
layer_9_group_0_path_0.property('Path').setValue(pathShape);
var layer_9_group_0_fill_1 = layer_9_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_9_group_0_fill_1.property('Color').setValue([1.000000, 1.000000, 1.000000]);
layer_9_group_0_fill_1.property('Opacity').setValue(100);
var layer_9_group_0_transform = layer_9_group_0.property('Transform');
layer_9_group_0_transform.property('Position').setValue([28.279, 74.042]);
layer_9_group_0_transform.property('Scale').setValue([100, 100]);
layer_9_group_0_transform.property('Rotation').setValue(0);
layer_9_group_0_transform.property('Opacity').setValue(100);
layer_9.inPoint = 0.958333;
layer_9.outPoint = 7.208333;
layer_9.startTime = 0.958333;
layer_9.parent = layer_2;
layer_9.property('Transform').property('Opacity').setValue(100);
// Smart detection: 2 keyframes for layer_9.property('Transform').property('Position')
setKeyframeWithEasing(layer_9.property('Transform').property('Position'), 23, [262.4, 193.512, 0], {x: 0.667, y: 1}, {x: 0.333, y: 0}, frameRate, 307.22, 2.000);
layer_9.property('Transform').property('Position').setValueAtTime(71.0009765625/frameRate, [630.778, -55.999, 0]);
// Smart detection: 3 keyframes for layer_9.property('Transform').property('Scale')
setKeyframeWithEasing(layer_9.property('Transform').property('Scale'), 23, [0, 0, 100], {x: [0.667, 0.667, 0.667], y: [1, 1, 1]}, {x: [0.333, 0.333, 0.333], y: [0, 0, 0]}, frameRate, 192.39, 1.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Scale'), 56.333, [136.043, 136.043, 100], {x: [0.667, 0.667, 0.667], y: [1, 1, 1]}, {x: [0.333, 0.333, 0.333], y: [0, 0, 0]}, frameRate, 192.39, 1.042);
layer_9.property('Transform').property('Scale').setValueAtTime(73/frameRate, [0, 0, 100]);
layer_9.property('Transform').property('Rotation').setValue(0.789);
layer_9.property('Transform').property('Anchor Point').setValue([28.279, 74.042, 0]);

// Layer 8: Water Dot 3
var layer_8 = createShapeLayer(comp, 'Water Dot 3');
// Shape Group: Group 3
var layer_8_group_0 = layer_8.property('Contents').addProperty('ADBE Vector Group');
layer_8_group_0.name = 'Group 3';
var layer_8_group_0_path_0 = layer_8_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[8.304, 0], [0, 8.304], [-8.304, 0], [0, -8.304]];
pathShape.inTangents = [[0, -4.586], [4.586, 0], [0, 4.586], [-4.587, 0]];
pathShape.outTangents = [[0, 4.586], [-4.587, 0], [0, -4.586], [4.586, 0]];
pathShape.closed = true;
layer_8_group_0_path_0.property('Path').setValue(pathShape);
var layer_8_group_0_fill_1 = layer_8_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_8_group_0_fill_1.property('Color').setValue([1.000000, 1.000000, 1.000000]);
layer_8_group_0_fill_1.property('Opacity').setValue(100);
var layer_8_group_0_transform = layer_8_group_0.property('Transform');
layer_8_group_0_transform.property('Position').setValue([28.279, 74.042]);
layer_8_group_0_transform.property('Scale').setValue([100, 100]);
layer_8_group_0_transform.property('Rotation').setValue(0);
layer_8_group_0_transform.property('Opacity').setValue(100);
layer_8.inPoint = 2.708333;
layer_8.outPoint = 8.958333;
layer_8.startTime = 2.708333;
layer_8.parent = layer_2;
layer_8.property('Transform').property('Opacity').setValue(100);
// Smart detection: 2 keyframes for layer_8.property('Transform').property('Position')
setKeyframeWithEasing(layer_8.property('Transform').property('Position'), 65, [262.4, 193.512, 0], {x: 0.667, y: 1}, {x: 0.333, y: 0}, frameRate, 307.22, 2.000);
layer_8.property('Transform').property('Position').setValueAtTime(113.0009765625/frameRate, [630.778, -55.999, 0]);
// Smart detection: 3 keyframes for layer_8.property('Transform').property('Scale')
setKeyframeWithEasing(layer_8.property('Transform').property('Scale'), 65, [0, 0, 100], {x: [0.667, 0.667, 0.667], y: [1, 1, 1]}, {x: [0.333, 0.333, 0.333], y: [0, 0, 0]}, frameRate, 192.39, 1.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Scale'), 98.333, [136.043, 136.043, 100], {x: [0.667, 0.667, 0.667], y: [1, 1, 1]}, {x: [0.333, 0.333, 0.333], y: [0, 0, 0]}, frameRate, 192.39, 1.042);
layer_8.property('Transform').property('Scale').setValueAtTime(115/frameRate, [0, 0, 100]);
layer_8.property('Transform').property('Rotation').setValue(0.789);
layer_8.property('Transform').property('Anchor Point').setValue([28.279, 74.042, 0]);

// Layer 7: Water Dot 4
var layer_7 = createShapeLayer(comp, 'Water Dot 4');
// Shape Group: Group 3
var layer_7_group_0 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_0.name = 'Group 3';
var layer_7_group_0_path_0 = layer_7_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[8.304, 0], [0, 8.304], [-8.304, 0], [0, -8.304]];
pathShape.inTangents = [[0, -4.586], [4.586, 0], [0, 4.586], [-4.587, 0]];
pathShape.outTangents = [[0, 4.586], [-4.587, 0], [0, -4.586], [4.586, 0]];
pathShape.closed = true;
layer_7_group_0_path_0.property('Path').setValue(pathShape);
var layer_7_group_0_fill_1 = layer_7_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_0_fill_1.property('Color').setValue([1.000000, 1.000000, 1.000000]);
layer_7_group_0_fill_1.property('Opacity').setValue(100);
var layer_7_group_0_transform = layer_7_group_0.property('Transform');
layer_7_group_0_transform.property('Position').setValue([28.279, 74.042]);
layer_7_group_0_transform.property('Scale').setValue([100, 100]);
layer_7_group_0_transform.property('Rotation').setValue(0);
layer_7_group_0_transform.property('Opacity').setValue(100);
layer_7.inPoint = 4.291667;
layer_7.outPoint = 10.541667;
layer_7.startTime = 4.291667;
layer_7.parent = layer_2;
layer_7.property('Transform').property('Opacity').setValue(100);
// Smart detection: 2 keyframes for layer_7.property('Transform').property('Position')
setKeyframeWithEasing(layer_7.property('Transform').property('Position'), 103, [262.4, 193.512, 0], {x: 0.667, y: 1}, {x: 0.333, y: 0}, frameRate, 307.22, 2.000);
layer_7.property('Transform').property('Position').setValueAtTime(151.0009765625/frameRate, [630.778, -55.999, 0]);
// Smart detection: 3 keyframes for layer_7.property('Transform').property('Scale')
setKeyframeWithEasing(layer_7.property('Transform').property('Scale'), 103, [0, 0, 100], {x: [0.667, 0.667, 0.667], y: [1, 1, 1]}, {x: [0.333, 0.333, 0.333], y: [0, 0, 0]}, frameRate, 192.39, 1.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Scale'), 136.333, [136.043, 136.043, 100], {x: [0.667, 0.667, 0.667], y: [1, 1, 1]}, {x: [0.333, 0.333, 0.333], y: [0, 0, 0]}, frameRate, 192.39, 1.042);
layer_7.property('Transform').property('Scale').setValueAtTime(153/frameRate, [0, 0, 100]);
layer_7.property('Transform').property('Rotation').setValue(0.789);
layer_7.property('Transform').property('Anchor Point').setValue([28.279, 74.042, 0]);

// Layer 6: Fether
var layer_6 = createShapeLayer(comp, 'Fether');
// Shape Group: Group 1
var layer_6_group_0 = layer_6.property('Contents').addProperty('ADBE Vector Group');
layer_6_group_0.name = 'Group 1';
var layer_6_group_0_path_0 = layer_6_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-46.09, 13.767], [-45.711, 17.519], [-22.219, 26.135], [-0.694, 28.534], [5.426, 28.275], [5.495, 28.268], [25.187, 24.17], [46.032, 12.556], [39.753, 5.939], [20.41, 0.357], [-40.296, -25.016], [-44.465, -28.534], [-45.018, -24.881], [-42.436, -22.749], [19.788, 3.402], [42.925, 12.483], [24.407, 21.164], [5.056, 25.192], [-21.653, 23.077]];
pathShape.inTangents = [[8.722, 4.939], [-0.146, -1.256], [-6.855, -1.519], [-4.981, 0], [-0.207, 0.024], [0, 0], [-9.744, 2.531], [-0.062, 2.682], [4.283, 1.988], [8.685, 1.774], [10.138, 7.987], [1.254, 1.133], [0.173, -1.229], [-0.911, -0.721], [-32.573, -6.658], [0.017, -0.721], [17.538, -4.553], [0.673, -0.107], [14.608, 3.251]];
pathShape.outTangents = [[0.11, 1.247], [8.412, 4.487], [8.899, 1.967], [3.683, 0], [0, 0], [0.1, -0.018], [18.553, -4.815], [0.058, -2.465], [-4.149, -1.93], [-31.811, -6.503], [-1.532, -1.208], [-0.196, 1.205], [0.812, 0.7], [10.407, 8.254], [22.119, 4.521], [-0.014, 0.652], [-9.292, 2.413], [-0.687, 0.069], [-7.093, -1.578]];
pathShape.closed = true;
layer_6_group_0_path_0.property('Path').setValue(pathShape);
var layer_6_group_0_fill_1 = layer_6_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_6_group_0_fill_1.property('Color').setValue([0.847059, 0.662745, 0.207843]);
layer_6_group_0_fill_1.property('Opacity').setValue(100);
var layer_6_group_0_transform = layer_6_group_0.property('Transform');
layer_6_group_0_transform.property('Position').setValue([47.562, 92.948]);
layer_6_group_0_transform.property('Scale').setValue([100, 100]);
layer_6_group_0_transform.property('Rotation').setValue(0);
layer_6_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_6_group_1 = layer_6.property('Contents').addProperty('ADBE Vector Group');
layer_6_group_1.name = 'Group 2';
var layer_6_group_1_path_0 = layer_6_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-12.022, 1.615], [53.081, 26.391], [73.287, 41.265], [75.523, 39.108], [54.676, 23.727], [-11.359, -1.422], [-67.07, -31.39], [-74.066, -41.265], [-75.523, -37.631], [-69.565, -29.53], [-48.496, -12.136]];
pathShape.inTangents = [[-13.862, -3.027], [-12.688, -7.587], [-0.062, -0.066], [0, 0], [14.034, 8.391], [31.231, 6.82], [8.588, 9.996], [1.68, 2.954], [0.486, -1.249], [-2.431, -2.847], [-8.309, -4.895]];
pathShape.outTangents = [[30.82, 6.731], [13.683, 8.184], [0, 0], [-0.272, -0.284], [-12.886, -7.708], [-30.486, -6.659], [-3.041, -3.538], [-0.484, 1.173], [1.57, 2.517], [5.67, 6.645], [10.341, 6.099]];
pathShape.closed = true;
layer_6_group_1_path_0.property('Path').setValue(pathShape);
var layer_6_group_1_fill_1 = layer_6_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_6_group_1_fill_1.property('Color').setValue([0.847059, 0.662745, 0.207843]);
layer_6_group_1_fill_1.property('Opacity').setValue(100);
var layer_6_group_1_transform = layer_6_group_1.property('Transform');
layer_6_group_1_transform.property('Position').setValue([89.017, 64.011]);
layer_6_group_1_transform.property('Scale').setValue([100, 100]);
layer_6_group_1_transform.property('Rotation').setValue(0);
layer_6_group_1_transform.property('Opacity').setValue(100);
// Shape Group: Group 3
var layer_6_group_2 = layer_6.property('Contents').addProperty('ADBE Vector Group');
layer_6_group_2.name = 'Group 3';
var layer_6_group_2_path_0 = layer_6_group_2.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-113.599, -13.31], [-113.219, -9.558], [-93.911, 71.588], [120.441, 69.047], [10.234, -58.334], [-88.829, -119.775], [-100.12, -97.28], [-101.577, -93.645], [-111.974, -55.611], [-112.526, -51.958]];
pathShape.inTangents = [[-1.222, -13.755], [-0.146, -1.256], [-6.858, -15.895], [0, 0], [104.587, 43.146], [0, 0], [5.847, -14.19], [0.487, -1.249], [2.403, -14.801], [0.173, -1.229]];
pathShape.outTangents = [[0.11, 1.247], [1.653, 14.722], [35.898, 83.187], [0, 0], [-81.025, -33.427], [0, 0], [-0.483, 1.174], [-4.001, 10.3], [-0.196, 1.205], [-1.68, 11.883]];
pathShape.closed = true;
layer_6_group_2_path_0.property('Path').setValue(pathShape);
var layer_6_group_2_fill_1 = layer_6_group_2.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_6_group_2_fill_1.property('Color').setValue([0.992157, 0.819608, 0.247059]);
layer_6_group_2_fill_1.property('Opacity').setValue(100);
var layer_6_group_2_transform = layer_6_group_2.property('Transform');
layer_6_group_2_transform.property('Position').setValue([115.071, 120.025]);
layer_6_group_2_transform.property('Scale').setValue([100, 100]);
layer_6_group_2_transform.property('Rotation').setValue(0);
layer_6_group_2_transform.property('Opacity').setValue(100);
layer_6.inPoint = 0.000000;
layer_6.outPoint = 6.250000;
layer_6.startTime = 0.000000;
layer_6.parent = layer_2;
layer_6.property('Transform').property('Opacity').setValue(100);
layer_6.property('Transform').property('Position').setValue([189.857, 90.774, 0]);
layer_6.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 150 keyframes for layer_6.property('Transform').property('Rotation')
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 0, 0, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 1, -0.046, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 2, -0.181, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 3, -0.399, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 4, -0.695, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 5, -1.066, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 6, -1.505, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 7, -2.008, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 8, -2.569, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 9, -3.184, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 10, -3.847, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 11, -4.555, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 12, -5.301, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 13, -6.08, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 14, -6.889, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 15, -7.721, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 16, -8.572, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 17, -9.436, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 18, -10.309, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 19, -11.186, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 20, -12.062, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 21, -12.932, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 22, -13.79, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 23, -14.632, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 24, -15.453, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 25, -16.248, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 26, -17.011, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 27, -17.738, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 28, -18.424, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 29, -19.064, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 30, -19.653, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 31, -20.186, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 32, -20.657, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 33, -21.062, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 34, -21.397, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 35, -21.655, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 36, -21.832, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 37, -21.923, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 38, -21.923, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 39, -21.832, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 40, -21.655, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 41, -21.397, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 42, -21.062, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 43, -20.657, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 44, -20.186, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 45, -19.653, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 46, -19.064, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 47, -18.424, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 48, -17.738, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 49, -17.011, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 50, -16.248, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 51, -15.453, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 52, -14.632, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 53, -13.79, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 54, -12.932, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 55, -12.062, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 56, -11.186, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 57, -10.309, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 58, -9.436, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 59, -8.572, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 60, -7.721, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 61, -6.889, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 62, -6.08, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 63, -5.301, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 64, -4.555, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 65, -3.847, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 66, -3.184, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 67, -2.569, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 68, -2.008, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 69, -1.505, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 70, -1.066, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 71, -0.695, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 72, -0.399, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 73, -0.181, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 74, -0.046, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 75, 0, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 76, -0.046, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 77, -0.181, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 78, -0.399, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 79, -0.695, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 80, -1.066, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 81, -1.505, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 82, -2.008, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 83, -2.569, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 84, -3.184, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 85, -3.847, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 86, -4.555, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 87, -5.301, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 88, -6.08, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 89, -6.889, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 90, -7.721, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 91, -8.572, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 92, -9.436, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 93, -10.309, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 94, -11.186, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 95, -12.062, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 96, -12.932, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 97, -13.79, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 98, -14.632, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 99, -15.453, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 100, -16.248, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 101, -17.011, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 102, -17.738, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 103, -18.424, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 104, -19.064, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 105, -19.653, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 106, -20.186, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 107, -20.657, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 108, -21.062, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 109, -21.397, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 110, -21.655, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 111, -21.832, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 112, -21.923, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 113, -21.923, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 114, -21.832, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 115, -21.655, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 116, -21.397, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 117, -21.062, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 118, -20.657, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 119, -20.186, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 120, -19.653, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 121, -19.064, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 122, -18.424, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 123, -17.738, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 124, -17.011, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 125, -16.248, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 126, -15.453, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 127, -14.632, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 128, -13.79, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 129, -12.932, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 130, -12.062, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 131, -11.186, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 132, -10.309, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 133, -9.436, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 134, -8.572, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 135, -7.721, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 136, -6.889, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 137, -6.08, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 138, -5.301, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 139, -4.555, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 140, -3.847, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 141, -3.184, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 142, -2.569, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 143, -2.008, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 144, -1.505, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 145, -1.066, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 146, -0.695, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 147, -0.399, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 148, -0.181, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 149, -0.046, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 21.92, 0.042);
layer_6.property('Transform').property('Anchor Point').setValue([170.071, 173.025, 0]);

// Layer 5: Fether
var layer_5 = createShapeLayer(comp, 'Fether');
// Shape Group: Group 1
var layer_5_group_0 = layer_5.property('Contents').addProperty('ADBE Vector Group');
layer_5_group_0.name = 'Group 1';
var layer_5_group_0_path_0 = layer_5_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[12.61, -22.338], [8.318, -0.199], [-14.553, 23.882], [-13.268, 26.822], [11.204, 0.947], [13.689, -26.143], [12.598, -26.822], [11.726, -25.876], [-5.865, -11.917], [-19.872, -5.477], [-19.747, -2.26], [-4.191, -9.298]];
pathShape.inTangents = [[-2.596, 2.561], [5.537, -13.958], [9.962, -6.963], [-0.46, -0.96], [-3.769, 9.502], [1.108, 0.695], [0, 0], [0, 0], [10.49, -6.324], [5.288, -1.671], [-0.059, -1.066], [-5.236, 3.162]];
pathShape.outTangents = [[0.752, 2.261], [-3.431, 8.653], [0.401, 0.997], [10.523, -7.307], [8.667, -21.843], [0, 0], [0, 0], [-0.069, 0.076], [-3.938, 2.374], [0.024, 1.077], [5.111, -1.525], [8.181, -4.942]];
pathShape.closed = true;
layer_5_group_0_path_0.property('Path').setValue(pathShape);
var layer_5_group_0_fill_1 = layer_5_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_5_group_0_fill_1.property('Color').setValue([0.847059, 0.662745, 0.207843]);
layer_5_group_0_fill_1.property('Opacity').setValue(100);
var layer_5_group_0_transform = layer_5_group_0.property('Transform');
layer_5_group_0_transform.property('Position').setValue([21.07, 84.96]);
layer_5_group_0_transform.property('Scale').setValue([100, 100]);
layer_5_group_0_transform.property('Rotation').setValue(0);
layer_5_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_5_group_1 = layer_5.property('Contents').addProperty('ADBE Vector Group');
layer_5_group_1.name = 'Group 2';
var layer_5_group_1_path_0 = layer_5_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-21.953, 22.281], [-18.608, 23.606], [-16.665, 21.898], [-2.054, 3.141], [9.205, -21.87], [14.797, -22.222], [18.439, -7.466], [6.772, 27.365], [10.441, 27.265], [21.545, -7.328], [16.343, -24.918], [7.083, -24.241], [6.592, -23.934], [6.423, -23.378], [-4.764, 1.623]];
pathShape.inTangents = [[7.338, -5.727], [-1.157, -0.403], [-0.642, 0.594], [-4.487, 7.646], [-0.855, 2.682], [-2.022, -1.184], [0.445, -10.176], [5.198, -9.099], [-1.218, 0.059], [-0.552, 12.703], [3.86, 2.24], [0.203, -0.131], [0, 0], [0, 0], [7.425, -12.641]];
pathShape.outTangents = [[1.073, 0.479], [0.652, -0.545], [5.209, -4.799], [6.734, -11.476], [1.032, -0.514], [1.906, 1.115], [-0.553, 12.744], [1.225, -0.007], [5.119, -9.247], [0.408, -9.433], [-4.224, -2.447], [0, 0], [0, 0], [-0.038, 0.124], [-4.093, 6.969]];
pathShape.closed = true;
layer_5_group_1_path_0.property('Path').setValue(pathShape);
var layer_5_group_1_fill_1 = layer_5_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_5_group_1_fill_1.property('Color').setValue([0.847059, 0.662745, 0.207843]);
layer_5_group_1_fill_1.property('Opacity').setValue(100);
var layer_5_group_1_transform = layer_5_group_1.property('Transform');
layer_5_group_1_transform.property('Position').setValue([50.19, 109.99]);
layer_5_group_1_transform.property('Scale').setValue([100, 100]);
layer_5_group_1_transform.property('Rotation').setValue(0);
layer_5_group_1_transform.property('Opacity').setValue(100);
// Shape Group: Group 3
var layer_5_group_2 = layer_5.property('Contents').addProperty('ADBE Vector Group');
layer_5_group_2.name = 'Group 3';
var layer_5_group_2_path_0 = layer_5_group_2.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-10.269, 16.102], [-3.286, 13.99], [6.831, -1.582], [10.269, -16.013], [7.162, -16.102], [3.907, -2.634]];
pathShape.inTangents = [[6.324, -4.874], [-2.296, 0.769], [-1.85, 4.564], [-0.007, 0.259], [0, 0], [3.038, -7.435]];
pathShape.outTangents = [[2.357, -0.635], [5.16, -5.326], [3.22, -7.935], [0, 0], [0, 0.058], [-3.099, 7.584]];
pathShape.closed = true;
layer_5_group_2_path_0.property('Path').setValue(pathShape);
var layer_5_group_2_fill_1 = layer_5_group_2.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_5_group_2_fill_1.property('Color').setValue([0.847059, 0.662745, 0.207843]);
layer_5_group_2_fill_1.property('Opacity').setValue(100);
var layer_5_group_2_transform = layer_5_group_2.property('Transform');
layer_5_group_2_transform.property('Position').setValue([92.877, 117.615]);
layer_5_group_2_transform.property('Scale').setValue([100, 100]);
layer_5_group_2_transform.property('Rotation').setValue(0);
layer_5_group_2_transform.property('Opacity').setValue(100);
// Shape Group: Group 4
var layer_5_group_3 = layer_5.property('Contents').addProperty('ADBE Vector Group');
layer_5_group_3.name = 'Group 4';
var layer_5_group_3_path_0 = layer_5_group_3.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-79.148, 10.657], [-79.023, 13.874], [-73.829, 40.014], [-72.544, 42.955], [-52.11, 63.444], [-48.766, 64.769], [-44.51, 66.04], [-23.385, 68.528], [-19.716, 68.428], [2.261, 64.89], [9.244, 62.778], [104.097, -17.32], [-40.875, -102.577]];
pathShape.inTangents = [[-0.949, -37.344], [-0.059, -1.067], [-3.154, -7.846], [-0.46, -0.96], [-9.686, -4.308], [-1.156, -0.403], [-1.477, -0.369], [-6.955, 0.049], [-1.219, 0.059], [-7.107, 1.919], [-2.296, 0.77], [0, 0], [0, 0]];
pathShape.outTangents = [[0.024, 1.077], [0.476, 9.334], [0.401, 0.997], [4.214, 8.939], [1.073, 0.48], [1.36, 0.477], [7.1, 1.774], [1.225, -0.007], [7.511, -0.352], [2.357, -0.634], [40.33, -13.503], [0, 0], [0, 0]];
pathShape.closed = true;
layer_5_group_3_path_0.property('Path').setValue(pathShape);
var layer_5_group_3_fill_1 = layer_5_group_3.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_5_group_3_fill_1.property('Color').setValue([0.992157, 0.819608, 0.247059]);
layer_5_group_3_fill_1.property('Opacity').setValue(100);
var layer_5_group_3_transform = layer_5_group_3.property('Transform');
layer_5_group_3_transform.property('Position').setValue([80.347, 68.827]);
layer_5_group_3_transform.property('Scale').setValue([100, 100]);
layer_5_group_3_transform.property('Rotation').setValue(0);
layer_5_group_3_transform.property('Opacity').setValue(100);
layer_5.inPoint = 0.000000;
layer_5.outPoint = 6.250000;
layer_5.startTime = 0.000000;
layer_5.parent = layer_2;
layer_5.property('Transform').property('Opacity').setValue(100);
layer_5.property('Transform').property('Position').setValue([157.643, 191.497, 0]);
layer_5.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 150 keyframes for layer_5.property('Transform').property('Rotation')
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 0, 0, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 1, 0.047, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 2, 0.183, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 3, 0.404, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 4, 0.705, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 5, 1.08, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 6, 1.525, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 7, 2.035, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 8, 2.603, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 9, 3.227, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 10, 3.899, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 11, 4.616, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 12, 5.372, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 13, 6.162, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 14, 6.981, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 15, 7.825, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 16, 8.687, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 17, 9.563, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 18, 10.448, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 19, 11.337, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 20, 12.224, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 21, 13.106, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 22, 13.975, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 23, 14.829, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 24, 15.661, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 25, 16.466, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 26, 17.24, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 27, 17.977, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 28, 18.672, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 29, 19.321, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 30, 19.917, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 31, 20.457, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 32, 20.935, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 33, 21.346, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 34, 21.684, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 35, 21.946, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 36, 22.125, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 37, 22.217, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 38, 22.217, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 39, 22.125, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 40, 21.946, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 41, 21.684, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 42, 21.346, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 43, 20.935, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 44, 20.457, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 45, 19.917, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 46, 19.321, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 47, 18.672, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 48, 17.977, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 49, 17.24, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 50, 16.466, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 51, 15.661, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 52, 14.829, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 53, 13.975, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 54, 13.106, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 55, 12.224, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 56, 11.337, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 57, 10.448, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 58, 9.563, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 59, 8.687, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 60, 7.825, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 61, 6.981, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 62, 6.162, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 63, 5.372, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 64, 4.616, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 65, 3.899, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 66, 3.227, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 67, 2.603, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 68, 2.035, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 69, 1.525, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 70, 1.08, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 71, 0.705, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 72, 0.404, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 73, 0.183, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 74, 0.047, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 75, 0, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 76, 0.047, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 77, 0.183, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 78, 0.404, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 79, 0.705, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 80, 1.08, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 81, 1.525, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 82, 2.035, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 83, 2.603, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 84, 3.227, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 85, 3.899, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 86, 4.616, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 87, 5.372, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 88, 6.162, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 89, 6.981, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 90, 7.825, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 91, 8.687, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 92, 9.563, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 93, 10.448, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 94, 11.337, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 95, 12.224, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 96, 13.106, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 97, 13.975, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 98, 14.829, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 99, 15.661, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 100, 16.466, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 101, 17.24, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 102, 17.977, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 103, 18.672, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 104, 19.321, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 105, 19.917, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 106, 20.457, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 107, 20.935, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 108, 21.346, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 109, 21.684, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 110, 21.946, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 111, 22.125, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 112, 22.217, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 113, 22.217, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 114, 22.125, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 115, 21.946, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 116, 21.684, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 117, 21.346, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 118, 20.935, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 119, 20.457, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 120, 19.917, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 121, 19.321, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 122, 18.672, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 123, 17.977, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 124, 17.24, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 125, 16.466, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 126, 15.661, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 127, 14.829, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 128, 13.975, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 129, 13.106, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 130, 12.224, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 131, 11.337, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 132, 10.448, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 133, 9.563, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 134, 8.687, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 135, 7.825, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 136, 6.981, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 137, 6.162, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 138, 5.372, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 139, 4.616, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 140, 3.899, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 141, 3.227, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 142, 2.603, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 143, 2.035, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 144, 1.525, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 145, 1.08, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 146, 0.705, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 147, 0.404, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 148, 0.183, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 149, 0.047, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 22.22, 0.042);
layer_5.property('Transform').property('Anchor Point').setValue([144.347, 42.827, 0]);

// Layer 4: Fether
var layer_4 = createShapeLayer(comp, 'Fether');
// Shape Group: Group 1
var layer_4_group_0 = layer_4.property('Contents').addProperty('ADBE Vector Group');
layer_4_group_0.name = 'Group 1';
var layer_4_group_0_path_0 = layer_4_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-6.927, 25.035], [-5.133, 23.862], [-1.674, 20.862], [4.812, -2.589], [6.603, -25.035], [3.497, -24.926]];
pathShape.inTangents = [[11.229, -23.003], [-0.631, 0.473], [-1.181, 1.151], [-1.119, 6.699], [0.014, 0.393], [0, 0]];
pathShape.outTangents = [[0.566, -0.312], [1.126, -0.843], [3.41, -8.401], [2.115, -12.654], [0, 0], [0.01, 0.318]];
pathShape.closed = true;
layer_4_group_0_path_0.property('Path').setValue(pathShape);
var layer_4_group_0_fill_1 = layer_4_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_4_group_0_fill_1.property('Color').setValue([0.847059, 0.662745, 0.207843]);
layer_4_group_0_fill_1.property('Opacity').setValue(100);
var layer_4_group_0_transform = layer_4_group_0.property('Transform');
layer_4_group_0_transform.property('Position').setValue([23.98, 53.706]);
layer_4_group_0_transform.property('Scale').setValue([100, 100]);
layer_4_group_0_transform.property('Rotation').setValue(0);
layer_4_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_4_group_1 = layer_4.property('Contents').addProperty('ADBE Vector Group');
layer_4_group_1.name = 'Group 2';
var layer_4_group_1_path_0 = layer_4_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-16.485, 34.655], [-14.69, 33.481], [-11.232, 30.481], [26.289, -50.837], [-31.63, -58.569]];
pathShape.inTangents = [[-16.803, 9.182], [-0.631, 0.473], [-1.181, 1.151], [0, 0], [0, 0]];
pathShape.outTangents = [[0.566, -0.312], [1.126, -0.843], [19.105, -18.591], [0, 0], [0, 0]];
pathShape.closed = true;
layer_4_group_1_path_0.property('Path').setValue(pathShape);
var layer_4_group_1_fill_1 = layer_4_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_4_group_1_fill_1.property('Color').setValue([0.972549, 0.780392, 0.235294]);
layer_4_group_1_fill_1.property('Opacity').setValue(100);
var layer_4_group_1_transform = layer_4_group_1.property('Transform');
layer_4_group_1_transform.property('Position').setValue([33.538, 44.087]);
layer_4_group_1_transform.property('Scale').setValue([100, 100]);
layer_4_group_1_transform.property('Rotation').setValue(0);
layer_4_group_1_transform.property('Opacity').setValue(100);
layer_4.inPoint = 0.000000;
layer_4.outPoint = 6.250000;
layer_4.startTime = 0.000000;
layer_4.parent = layer_2;
layer_4.property('Transform').property('Opacity').setValue(100);
layer_4.property('Transform').property('Position').setValue([211.46, 189.487, 0]);
layer_4.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 150 keyframes for layer_4.property('Transform').property('Rotation')
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 0, 0, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 1, 0.025, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 2, 0.099, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 3, 0.219, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 4, 0.382, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 5, 0.585, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 6, 0.827, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 7, 1.103, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 8, 1.411, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 9, 1.749, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 10, 2.113, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 11, 2.502, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 12, 2.911, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 13, 3.34, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 14, 3.784, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 15, 4.241, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 16, 4.708, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 17, 5.183, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 18, 5.663, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 19, 6.144, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 20, 6.625, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 21, 7.103, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 22, 7.574, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 23, 8.037, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 24, 8.488, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 25, 8.924, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 26, 9.343, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 27, 9.743, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 28, 10.12, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 29, 10.471, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 30, 10.795, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 31, 11.087, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 32, 11.346, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 33, 11.569, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 34, 11.752, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 35, 11.894, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 36, 11.991, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 37, 12.041, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 38, 12.041, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 39, 11.991, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 40, 11.894, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 41, 11.752, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 42, 11.569, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 43, 11.346, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 44, 11.087, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 45, 10.795, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 46, 10.471, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 47, 10.12, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 48, 9.743, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 49, 9.343, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 50, 8.924, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 51, 8.488, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 52, 8.037, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 53, 7.574, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 54, 7.103, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 55, 6.625, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 56, 6.144, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 57, 5.663, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 58, 5.183, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 59, 4.708, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 60, 4.241, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 61, 3.784, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 62, 3.34, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 63, 2.911, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 64, 2.502, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 65, 2.113, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 66, 1.749, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 67, 1.411, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 68, 1.103, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 69, 0.827, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 70, 0.585, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 71, 0.382, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 72, 0.219, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 73, 0.099, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 74, 0.025, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 75, 0, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 76, 0.025, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 77, 0.099, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 78, 0.219, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 79, 0.382, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 80, 0.585, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 81, 0.827, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 82, 1.103, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 83, 1.411, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 84, 1.749, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 85, 2.113, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 86, 2.502, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 87, 2.911, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 88, 3.34, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 89, 3.784, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 90, 4.241, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 91, 4.708, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 92, 5.183, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 93, 5.663, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 94, 6.144, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 95, 6.625, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 96, 7.103, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 97, 7.574, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 98, 8.037, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 99, 8.488, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 100, 8.924, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 101, 9.343, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 102, 9.743, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 103, 10.12, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 104, 10.471, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 105, 10.795, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 106, 11.087, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 107, 11.346, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 108, 11.569, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 109, 11.752, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 110, 11.894, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 111, 11.991, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 112, 12.041, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 113, 12.041, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 114, 11.991, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 115, 11.894, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 116, 11.752, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 117, 11.569, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 118, 11.346, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 119, 11.087, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 120, 10.795, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 121, 10.471, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 122, 10.12, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 123, 9.743, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 124, 9.343, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 125, 8.924, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 126, 8.488, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 127, 8.037, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 128, 7.574, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 129, 7.103, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 130, 6.625, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 131, 6.144, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 132, 5.663, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 133, 5.183, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 134, 4.708, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 135, 4.241, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 136, 3.784, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 137, 3.34, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 138, 2.911, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 139, 2.502, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 140, 2.113, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 141, 1.749, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 142, 1.411, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 143, 1.103, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 144, 0.827, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 145, 0.585, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 146, 0.382, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 147, 0.219, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 148, 0.099, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 149, 0.025, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.04, 0.042);
layer_4.property('Transform').property('Anchor Point').setValue([26.538, 0.087, 0]);

// Layer 3: Fether
var layer_3 = createShapeLayer(comp, 'Fether');
// Shape Group: Group 1
var layer_3_group_0 = layer_3.property('Contents').addProperty('ADBE Vector Group');
layer_3_group_0.name = 'Group 1';
var layer_3_group_0_path_0 = layer_3_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-48.907, 16.654], [-18.473, 22.785], [-8.094, 23.282], [12.696, 20.786], [29.874, 13.013], [34.52, 8.933], [41.02, 7.01], [48.7, 1.34], [43.05, -2.102], [31.141, -3.203], [27.486, -3.325], [-45.511, -23.282], [-46.036, -20.096], [-32.795, -14.522], [27.403, -0.218], [30.899, -0.104], [45.277, 1.709], [33.291, 6.041], [32.764, 6.168], [32.429, 6.596], [30.202, 8.785], [11.601, 17.862], [-48.911, 13.406]];
pathShape.inTangents = [[-0.014, -1.06], [-9.441, -0.881], [-3.352, 0], [-6.307, 1.66], [-3.538, 2.551], [-0.642, 0.725], [-2.714, 0.932], [0.222, 2.074], [2.665, 0.504], [5.167, 0.217], [1.276, 0.036], [17.535, 7.891], [0.166, -1.063], [-4.857, -1.908], [-21.087, -0.541], [-1.084, -0.041], [-1.064, -0.494], [5.302, -1.288], [0, 0], [0, 0], [1.505, -1.291], [9.785, -2.522], [29.25, 8.771]];
pathShape.outTangents = [[10.804, 3.197], [3.566, 0.331], [7.521, 0], [8.077, -2.126], [2.513, -1.816], [1.087, -0.276], [5.674, -1.954], [-0.227, -2.137], [-2.744, -0.515], [-1.16, -0.048], [-25.861, -0.662], [-0.183, 1.06], [3.914, 1.757], [15.823, 6.228], [1.249, 0.031], [10.031, 0.414], [-1.516, 1.152], [0, 0], [0, 0], [-0.017, 0.024], [-2.91, 2.495], [-11.75, 3.031], [-0.011, 1.105]];
pathShape.closed = true;
layer_3_group_0_path_0.property('Path').setValue(pathShape);
var layer_3_group_0_fill_1 = layer_3_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_3_group_0_fill_1.property('Color').setValue([0.847059, 0.662745, 0.207843]);
layer_3_group_0_fill_1.property('Opacity').setValue(100);
var layer_3_group_0_transform = layer_3_group_0.property('Transform');
layer_3_group_0_transform.property('Position').setValue([49.171, 76.06]);
layer_3_group_0_transform.property('Scale').setValue([100, 100]);
layer_3_group_0_transform.property('Rotation').setValue(0);
layer_3_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_3_group_1 = layer_3.property('Contents').addProperty('ADBE Vector Group');
layer_3_group_1.name = 'Group 2';
var layer_3_group_1_path_0 = layer_3_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[35.507, 17.495], [35.799, 17.265], [35.548, 16.953], [15.438, -0.979], [-34.708, -19.19], [-35.799, -16.152], [13.671, 1.573], [33.359, 19.19]];
pathShape.inTangents = [[0, 0], [0, 0], [0.141, 0.172], [11.815, 8.187], [21.874, 1.612], [0.356, -1.035], [-10.808, -7.487], [-0.069, -0.086]];
pathShape.outTangents = [[0, 0], [-0.027, -0.036], [-1.388, -1.685], [-10.991, -7.618], [-0.372, 0.987], [21.591, 1.384], [12.629, 8.754], [0, 0]];
pathShape.closed = true;
layer_3_group_1_path_0.property('Path').setValue(pathShape);
var layer_3_group_1_fill_1 = layer_3_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_3_group_1_fill_1.property('Color').setValue([0.847059, 0.662745, 0.207843]);
layer_3_group_1_fill_1.property('Opacity').setValue(100);
var layer_3_group_1_transform = layer_3_group_1.property('Transform');
layer_3_group_1_transform.property('Position').setValue([45.759, 43.167]);
layer_3_group_1_transform.property('Scale').setValue([100, 100]);
layer_3_group_1_transform.property('Rotation').setValue(0);
layer_3_group_1_transform.property('Opacity').setValue(100);
// Shape Group: Group 3
var layer_3_group_2 = layer_3.property('Contents').addProperty('ADBE Vector Group');
layer_3_group_2.name = 'Group 3';
var layer_3_group_2_path_0 = layer_3_group_2.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-40.844, 28.089], [-36.46, 52.294], [37.406, 31.238], [37.938, 24.449], [38.266, 20.221], [38.963, 11.331], [39.205, 8.232], [40.157, -3.962], [40.199, -4.505], [40.858, -12.945], [-13.679, -60.922], [-30.057, -40.648], [-31.148, -37.61], [-37.448, -11.846], [-37.972, -8.661], [-40.847, 24.842]];
pathShape.inTangents = [[-0.014, -1.06], [-2.827, -3.193], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [24.162, -3.452], [4.28, -11.349], [0.355, -1.035], [1.575, -9.195], [0.166, -1.063], [0.086, -10.175]];
pathShape.outTangents = [[0.125, 11.833], [10.7, 12.081], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-6.603, 0.946], [-0.373, 0.987], [-2.616, 7.653], [-0.183, 1.06], [-1.812, 11.529], [-0.011, 1.105]];
pathShape.closed = true;
layer_3_group_2_path_0.property('Path').setValue(pathShape);
var layer_3_group_2_fill_1 = layer_3_group_2.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_3_group_2_fill_1.property('Color').setValue([0.992157, 0.819608, 0.247059]);
layer_3_group_2_fill_1.property('Opacity').setValue(100);
var layer_3_group_2_transform = layer_3_group_2.property('Transform');
layer_3_group_2_transform.property('Position').setValue([41.108, 64.625]);
layer_3_group_2_transform.property('Scale').setValue([100, 100]);
layer_3_group_2_transform.property('Rotation').setValue(0);
layer_3_group_2_transform.property('Opacity').setValue(100);
layer_3.inPoint = 0.000000;
layer_3.outPoint = 6.250000;
layer_3.startTime = 0.000000;
layer_3.parent = layer_2;
layer_3.property('Transform').property('Opacity').setValue(100);
layer_3.property('Transform').property('Position').setValue([32.151, 119.159, 0]);
layer_3.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 150 keyframes for layer_3.property('Transform').property('Rotation')
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 0, 3.064, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 1, 3.013, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 2, 2.867, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 3, 2.629, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 4, 2.305, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 5, 1.9, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 6, 1.421, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 7, 0.873, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 8, 0.26, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 9, -0.411, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 10, -1.135, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 11, -1.907, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 12, -2.721, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 13, -3.572, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 14, -4.454, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 15, -5.362, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 16, -6.291, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 17, -7.234, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 18, -8.187, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 19, -9.144, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 20, -10.1, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 21, -11.049, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 22, -11.986, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 23, -12.904, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 24, -13.8, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 25, -14.668, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 26, -15.501, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 27, -16.294, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 28, -17.043, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 29, -17.741, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 30, -18.384, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 31, -18.965, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 32, -19.48, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 33, -19.922, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 34, -20.287, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 35, -20.568, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 36, -20.762, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 37, -20.861, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 38, -20.861, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 39, -20.762, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 40, -20.568, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 41, -20.287, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 42, -19.922, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 43, -19.48, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 44, -18.965, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 45, -18.384, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 46, -17.741, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 47, -17.043, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 48, -16.294, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 49, -15.501, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 50, -14.668, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 51, -13.8, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 52, -12.904, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 53, -11.986, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 54, -11.049, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 55, -10.1, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 56, -9.144, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 57, -8.187, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 58, -7.234, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 59, -6.291, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 60, -5.362, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 61, -4.454, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 62, -3.572, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 63, -2.721, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 64, -1.907, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 65, -1.135, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 66, -0.411, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 67, 0.26, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 68, 0.873, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 69, 1.421, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 70, 1.9, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 71, 2.305, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 72, 2.629, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 73, 2.867, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 74, 3.013, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 75, 3.064, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 76, 3.013, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 77, 2.867, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 78, 2.629, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 79, 2.305, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 80, 1.9, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 81, 1.421, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 82, 0.873, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 83, 0.26, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 84, -0.411, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 85, -1.135, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 86, -1.907, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 87, -2.721, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 88, -3.572, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 89, -4.454, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 90, -5.362, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 91, -6.291, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 92, -7.234, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 93, -8.187, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 94, -9.144, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 95, -10.1, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 96, -11.049, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 97, -11.986, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 98, -12.904, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 99, -13.8, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 100, -14.668, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 101, -15.501, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 102, -16.294, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 103, -17.043, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 104, -17.741, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 105, -18.384, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 106, -18.965, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 107, -19.48, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 108, -19.922, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 109, -20.287, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 110, -20.568, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 111, -20.762, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 112, -20.861, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 113, -20.861, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 114, -20.762, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 115, -20.568, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 116, -20.287, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 117, -19.922, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 118, -19.48, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 119, -18.965, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 120, -18.384, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 121, -17.741, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 122, -17.043, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 123, -16.294, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 124, -15.501, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 125, -14.668, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 126, -13.8, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 127, -12.904, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 128, -11.986, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 129, -11.049, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 130, -10.1, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 131, -9.144, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 132, -8.187, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 133, -7.234, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 134, -6.291, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 135, -5.362, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 136, -4.454, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 137, -3.572, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 138, -2.721, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 139, -1.907, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 140, -1.135, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 141, -0.411, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 142, 0.26, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 143, 0.873, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 144, 1.421, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 145, 1.9, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 146, 2.305, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 147, 2.629, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 148, 2.867, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 149, 3.013, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 20.60, 0.042);
layer_3.property('Transform').property('Anchor Point').setValue([80.172, 71.624, 0]);

// Layer 1: Eyes
var layer_1 = createShapeLayer(comp, 'Eyes');
// Shape Group: Group 1
var layer_1_group_0 = layer_1.property('Contents').addProperty('ADBE Vector Group');
layer_1_group_0.name = 'Group 1';
var layer_1_group_0_path_0 = layer_1_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[5.22, 0], [-0.001, 5.22], [-5.22, 0], [-0.001, -5.22]];
pathShape.inTangents = [[0, -2.883], [2.883, 0], [0, 2.883], [-2.883, 0]];
pathShape.outTangents = [[0, 2.883], [-2.883, 0], [0, -2.883], [2.883, 0]];
pathShape.closed = true;
layer_1_group_0_path_0.property('Path').setValue(pathShape);
var layer_1_group_0_fill_1 = layer_1_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_1_group_0_fill_1.property('Color').setValue([1.000000, 1.000000, 1.000000]);
layer_1_group_0_fill_1.property('Opacity').setValue(100);
var layer_1_group_0_transform = layer_1_group_0.property('Transform');
layer_1_group_0_transform.property('Position').setValue([29.281, 19.317]);
layer_1_group_0_transform.property('Scale').setValue([100, 100]);
layer_1_group_0_transform.property('Rotation').setValue(0);
layer_1_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_1_group_1 = layer_1.property('Contents').addProperty('ADBE Vector Group');
layer_1_group_1.name = 'Group 2';
var layer_1_group_1_path_0 = layer_1_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-13.825, 0], [0.001, 13.825], [13.825, -0.001], [0.001, -13.825]];
pathShape.inTangents = [[0, -7.636], [-7.636, 0], [0, 7.636], [7.635, 0]];
pathShape.outTangents = [[0, 7.635], [7.635, 0], [0, -7.635], [-7.636, 0]];
pathShape.closed = true;
layer_1_group_1_path_0.property('Path').setValue(pathShape);
var layer_1_group_1_fill_1 = layer_1_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_1_group_1_fill_1.property('Color').setValue([0.160784, 0.164706, 0.235294]);
layer_1_group_1_fill_1.property('Opacity').setValue(100);
var layer_1_group_1_transform = layer_1_group_1.property('Transform');
layer_1_group_1_transform.property('Position').setValue([27.899, 25.667]);
layer_1_group_1_transform.property('Scale').setValue([100, 100]);
layer_1_group_1_transform.property('Rotation').setValue(0);
layer_1_group_1_transform.property('Opacity').setValue(100);
// Shape Group: Group 3
var layer_1_group_2 = layer_1.property('Contents').addProperty('ADBE Vector Group');
layer_1_group_2.name = 'Group 3';
var layer_1_group_2_path_0 = layer_1_group_2.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-22.623, 0], [-0.001, 22.623], [22.623, 0], [0, -22.623]];
pathShape.inTangents = [[0, -12.495], [-12.494, 0], [0, 12.494], [12.494, 0]];
pathShape.outTangents = [[0, 12.494], [12.495, 0], [0, -12.495], [-12.495, 0]];
pathShape.closed = true;
layer_1_group_2_path_0.property('Path').setValue(pathShape);
var layer_1_group_2_fill_1 = layer_1_group_2.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_1_group_2_fill_1.property('Color').setValue([1.000000, 1.000000, 1.000000]);
layer_1_group_2_fill_1.property('Opacity').setValue(100);
var layer_1_group_2_transform = layer_1_group_2.property('Transform');
layer_1_group_2_transform.property('Position').setValue([22.873, 22.874]);
layer_1_group_2_transform.property('Scale').setValue([100, 100]);
layer_1_group_2_transform.property('Rotation').setValue(0);
layer_1_group_2_transform.property('Opacity').setValue(100);
layer_1.inPoint = 0.000000;
layer_1.outPoint = 6.250000;
layer_1.startTime = 0.000000;
layer_1.parent = layer_2;
layer_1.property('Transform').property('Opacity').setValue(100);
layer_1.property('Transform').property('Position').setValue([240.907, 124.944, 0]);
// Smart detection: 45 keyframes for layer_1.property('Transform').property('Scale')
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 30, [100, 100, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 31, [100, 94.461, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 32, [100, 80.175, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 33, [100, 60.641, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 34, [100, 39.359, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 35, [100, 19.825, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 36, [100, 5.539, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 37, [100, 0, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 38, [100, 4.297, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 39, [100, 15.625, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 40, [100, 31.641, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 42, [100, 68.359, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 43, [100, 84.375, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 44, [100, 95.703, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 45, [100, 100, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 75, [100, 100, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 76, [100, 94.461, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 77, [100, 80.175, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 78, [100, 60.641, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 79, [100, 39.359, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 80, [100, 19.825, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 81, [100, 5.539, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 82, [100, 0, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 83, [100, 4.297, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 84, [100, 15.625, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 85, [100, 31.641, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 87, [100, 68.359, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 88, [100, 84.375, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 89, [100, 95.703, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 90, [100, 100, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 120, [100, 100, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 121, [100, 94.461, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 122, [100, 80.175, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 123, [100, 60.641, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 124, [100, 39.359, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 125, [100, 19.825, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 126, [100, 5.539, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 127, [100, 0, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 128, [100, 4.297, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 129, [100, 15.625, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 130, [100, 31.641, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 132, [100, 68.359, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 133, [100, 84.375, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 134, [100, 95.703, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 135, [100, 100, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
layer_1.property('Transform').property('Rotation').setValue(0);
layer_1.property('Transform').property('Anchor Point').setValue([22.873, 22.874, 0]);


// Set up track matte relationships

// Animation creation complete
alert('Animation "08" created successfully!\n' +
      'Duration: 6.00 seconds\n' +
      'Layers: 12\n' +
      'Assets: 0');

app.endUndoGroup();