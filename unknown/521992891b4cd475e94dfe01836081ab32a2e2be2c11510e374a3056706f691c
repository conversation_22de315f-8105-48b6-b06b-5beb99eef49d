#!/usr/bin/env python3
"""
INTELLIGENT LOTTIE TO JSX CONVERTER with ASSET DEPENDENCY MANAGEMENT
Converts Bodymovin/Lottie JSON files to complete After Effects JSX scripts
with smart layer ordering and interactive asset resolution
"""

import json
import math
import os
import sys
from typing import Dict, List, Any, Optional, Tuple, Union
from asset_dependency_manager import AssetDependencyManager

class LottieToJSXConverter:
    def __init__(self):
        self.jsx_code = []
        self.layer_references = {}
        self.comp_references = {}
        self.asset_references = {}
        self.font_references = {}
        self.asset_manager = AssetDependencyManager()
        self.dependencies = None
        
    def convert_json_to_jsx(self, json_file_path: str, output_jsx_path: str):
        """Main conversion function with smart runtime asset dialogs"""
        print(f"🎬 Loading JSON file: {json_file_path}")

        with open(json_file_path, 'r', encoding='utf-8') as f:
            lottie_data = json.load(f)

        print("� Converting to JSX with smart asset dialogs...")
        
        # Add header with global try-catch
        self.jsx_code.extend([
            f"// {lottie_data.get('nm', 'Animation')} - Converted from Lottie JSON",
            f"// Original Bodymovin version: {lottie_data.get('v', '5.12.1')}",
            f"// Generated by Lottie to JSX Converter",
            "",
            "try {",
            "    // Disable undo for performance",
            "    app.beginUndoGroup('Create Animation');",
            ""
        ])
        
        # Add JSON shim
        self.add_json_shim()
        
        # Add helper functions
        self.add_helper_functions()
        
        # Two-pass approach for better composition handling
        print("Pass 1: Creating composition shells...")
        self.create_composition_shells(lottie_data)
        
        print("Pass 2: Populating compositions with layers...")
        self.populate_compositions(lottie_data)
        
        # Add footer with proper error handling
        self.jsx_code.extend([
            "    // Set active composition",
            "    app.project.activeItem = main_comp;",
            "",
            "    // End undo group",
            "    app.endUndoGroup();",
            "",
            "} catch (e) {",
            "    // Clean up undo group on error",
            "    app.endUndoGroup();",
            "    alert('Error in JSX script: ' + e.toString() + '\\nLine: ' + (e.line || 'unknown'));",
            "    throw e;",
            "}"
        ])

        print(f"💾 Writing JSX file: {output_jsx_path}")
        with open(output_jsx_path, 'w', encoding='utf-8') as f:
            f.write("\n".join(self.jsx_code))

        print("✅ Conversion complete!")
        print("💡 When you run this JSX in After Effects:")
        print("   - Missing assets will prompt file dialogs automatically")
        print("   - You can browse and select the required files")
        print("   - Placeholders will be created if files are not found")

        return "\n".join(self.jsx_code)

    def add_json_shim(self):
        """Add JSON compatibility layer for After Effects ExtendScript"""
        self.jsx_code.extend([
            "// JSON shim for After Effects ExtendScript",
            "if (typeof JSON === 'undefined') {",
            "    JSON = {};",
            "    JSON.stringify = function(obj) {",
            "        if (obj === null) return 'null';",
            "        if (typeof obj === 'string') return '\"' + obj.replace(/\\\\/g, '\\\\\\\\').replace(/\"/g, '\\\\\"') + '\"';",
            "        if (typeof obj === 'number' || typeof obj === 'boolean') return String(obj);",
            "        if (obj instanceof Array) {",
            "            var arr = [];",
            "            for (var i = 0; i < obj.length; i++) arr.push(JSON.stringify(obj[i]));",
            "            return '[' + arr.join(',') + ']';",
            "        }",
            "        if (typeof obj === 'object') {",
            "            var pairs = [];",
            "            for (var key in obj) {",
            "                if (obj.hasOwnProperty(key)) pairs.push(JSON.stringify(key) + ':' + JSON.stringify(obj[key]));",
            "            }",
            "            return '{' + pairs.join(',') + '}';",
            "        }",
            "        return 'undefined';",
            "    };",
            "}",
            ""
        ])

    def create_composition_shells(self, data: Dict):
        """First pass: Create all composition shells without layers"""
        # Process all asset compositions first
        for asset in data.get('assets', []):
            if 'layers' in asset:
                self._create_comp_shell(asset)
        
        # Create the main composition shell
        self._create_comp_shell(data, is_root=True)

    def _create_comp_shell(self, comp_data: Dict, is_root: bool = False):
        """Create a composition shell without layers"""
        comp_id = comp_data.get('id', '')
        comp_name = comp_data.get('nm', 'Composition')
        width = comp_data.get('w', 1920)
        height = comp_data.get('h', 1080)
        duration = comp_data.get('op', 120) / comp_data.get('fr', 24)
        frame_rate = comp_data.get('fr', 24)
        
        # Ensure minimum duration of 1 frame
        if duration <= 0:
            duration = 1.0 / frame_rate
        
        # Use consistent comp variable naming
        comp_var = "main_comp" if is_root else f"comp_{comp_id}"
        self.comp_references[comp_id] = comp_var
        
        self.jsx_code.extend([
            f"// Create composition shell: {comp_name}",
            f"try {{",
            f"    var {comp_var} = app.project.items.addComp(",
            f"        '{comp_name}',",
            f"        {width},",
            f"        {height},",
            f"        1.0,",
            f"        {duration:.3f},",
            f"        {frame_rate}",
            f"    );",
            "",
            f"    // Store comp reference for later use",
            f"    var comp = {comp_var};",
            "",
            f"}} catch(e) {{",
            f"    alert('Failed to create composition {comp_name}: ' + e.toString());",
            f"    throw e;  // Re-throw to stop processing",
            f"}}",
            "",
        ])
        
        if is_root:
            self.jsx_code.extend([
                f"try {{",
                f"    {comp_var}.openInViewer();",
                f"}} catch(e) {{",
                f"    alert('Failed to open composition in viewer: ' + e.toString());",
                f"}}",
                "",
            ])

    def populate_compositions(self, data: Dict):
        """Second pass: Populate all compositions with layers"""
        # Process asset compositions first
        for asset in data.get('assets', []):
            if 'layers' in asset:
                self._populate_comp_layers(asset)
        
        # Process main composition
        self._populate_comp_layers(data, is_root=True)

    def _populate_comp_layers(self, comp_data: Dict, is_root: bool = False):
        """Populate a composition with its layers"""
        # Use consistent comp variable naming
        comp_id = comp_data.get('id', '')
        comp_var = "main_comp" if is_root else f"comp_{comp_id}"
        
        # Get layers
        layers = comp_data.get('layers', [])
        
        # Sort layers by dependencies
        sorted_layers = self.sort_layers_by_dependencies(layers)
        
        # Process each layer
        self.jsx_code.append(f"\n// Populate composition {comp_var}")
        for layer in sorted_layers:
            if is_root:
                self.process_single_layer(layer, comp_var)
            else:
                layer_references = {}  # Track layer references for precomps
                self.process_single_layer_in_precomp(layer, comp_var, layer_references)
        
        # Setup track mattes and parenting after all layers are created
        self.setup_track_mattes(sorted_layers)
        self.setup_layer_parenting(sorted_layers, comp_var)

    def add_header(self, data: Dict):
        """Add JSX script header"""
        # Header is now handled in convert_json_to_jsx
        pass

    def add_helper_functions(self):
        """Add utility functions"""
        self.jsx_code.extend([
            "// Helper Functions",
            "function createSolidLayer(comp, name, color, width, height) {",
            "    try {",
            "        var layer = comp.layers.addSolid(color, name, width, height, 1);",
            "        return layer;",
            "    } catch(e) {",
            "        alert('Failed to create solid layer: ' + e.toString());",
            "        return null;",
            "    }",
            "}",
            "",
            "function createShapeLayer(comp, name) {",
            "    try {",
            "        var layer = comp.layers.addShape();",
            "        layer.name = name;",
            "        return layer;",
            "    } catch(e) {",
            "        alert('Failed to create shape layer: ' + e.toString());",
            "        return null;",
            "    }",
            "}",
            "",
            "function createNullLayer(comp, name) {",
            "    try {",
            "        var layer = comp.layers.addNull();",
            "        layer.name = name;",
            "        return layer;",
            "    } catch(e) {",
            "        alert('Failed to create null layer: ' + e.toString());",
            "        return null;",
            "    }",
            "}",
            "",
            "function importAssetWithDialog(comp, assetId, originalPath, assetType, layerName, dimensions, description) {",
            "    try {",
            "        // Create file dialog",
            "        var dialogTitle = 'Import ' + assetType.charAt(0).toUpperCase() + assetType.slice(1);",
            "        var dialogPrompt = description + '\\n' + ",
            "                          'Original path: ' + originalPath + '\\n' +",
            "                          (dimensions ? 'Dimensions: ' + dimensions : '');",
            "        ",
            "        // Create the layer based on asset type",
            "        var layer;",
            "        if (assetType === 'image') {",
            "            var imageFile = File.openDialog(dialogPrompt, '*.png;*.jpg;*.jpeg;*.gif;*.tif;*.tiff;*.psd');",
            "            if (imageFile) {",
            "                var importOptions = new ImportOptions(imageFile);",
            "                var importedFile = app.project.importFile(importOptions);",
            "                layer = comp.layers.add(importedFile);",
            "            }",
            "        } else if (assetType === 'audio') {",
            "            var audioFile = File.openDialog(dialogPrompt, '*.mp3;*.wav;*.aif;*.aiff;*.m4a');",
            "            if (audioFile) {",
            "                var importOptions = new ImportOptions(audioFile);",
            "                var importedFile = app.project.importFile(importOptions);",
            "                layer = comp.layers.add(importedFile);",
            "            }",
            "        }",
            "        ",
            "        if (layer) {",
            "            layer.name = layerName;",
            "            return layer;",
            "        } else {",
            "            // If no file selected, create a placeholder",
            "            return createNullLayer(comp, layerName + ' (Missing Asset: ' + assetId + ')');",
            "        }",
            "    } catch(e) {",
            "        alert('Failed to import asset: ' + e.toString());",
            "        return createNullLayer(comp, layerName + ' (Import Failed)');",
            "    }",
            "}",
            "",
            "function setKeyframes(property, keyframes, frameRate) {",
            "    if (!keyframes || !keyframes.length) return;",
            "    ",
            "    for (var i = 0; i < keyframes.length; i++) {",
            "        try {",
            "            var kf = keyframes[i];",
            "            var time = kf[0] / frameRate;",
            "            var value = kf[1];",
            "            var easeIn = kf[2] ? kf[2].i : null;",
            "            var easeOut = kf[2] ? kf[2].o : null;",
            "            ",
            "            if (i === 0 && time > 0) {",
            "                // Set initial value at time 0",
            "                property.setValueAtTime(0, value);",
            "            }",
            "            ",
            "            property.setValueAtTime(time, value);",
            "            ",
            "            // Apply easing if available",
            "            if (easeIn && easeOut) {",
            "                var key = property.nearestKeyIndex(time);",
            "                if (key !== 0) {",
            "                    property.setTemporalEaseAtKey(key, [easeIn], [easeOut]);",
            "                }",
            "            }",
            "        } catch(e) {",
            "            alert('Failed to set keyframe at time ' + time + ': ' + e.toString());",
            "        }",
            "    }",
            "}",
            "",
            "function setTransformProperty(property, value, frameRate) {",
            "    try {",
            "        if (Array.isArray(value)) {",
            "            // Handle keyframe data",
            "            setKeyframes(property, value, frameRate);",
            "        } else {",
            "            // Handle static value",
            "            property.setValue(value);",
            "        }",
            "    } catch(e) {",
            "        alert('Failed to set transform property: ' + e.toString());",
            "    }",
            "}"
        ])
    
    def create_main_composition(self, data: Dict):
        """Create the main composition"""
        name = data.get('nm', 'Converted Animation')
        width = data.get('w', 1920)
        height = data.get('h', 1080)
        duration = data.get('op', 60) / data.get('fr', 24)  # Convert frames to seconds
        frame_rate = data.get('fr', 24)
        
        self.jsx_code.extend([
            "// Create main composition",
            "try {",
            f"    var main_comp = app.project.items.addComp(",
            f"        '{name}',",
            f"        {width},",
            f"        {height},",
            f"        1,  // pixel aspect",
            f"        {duration},",
            f"        {frame_rate}",
            "    );",
            "",
            "    // Store reference to main comp",
            "    var comp = main_comp;",
            f"    var frameRate = {frame_rate};",
            "",
            "} catch(e) {",
            "    alert('Failed to create main composition: ' + e.toString());",
            "    throw e;",
            "}",
            "",
        ])
        
        # Store reference
        self.comp_references['main'] = 'main_comp'

    def process_assets(self, assets: List[Dict]):
        """Process asset references and create precomps"""
        self.jsx_code.append("// Asset References and Precomps")

        for asset in assets:
            asset_id = asset.get('id', '')
            asset_type = 'unknown'

            if 'e' in asset:
                if asset['e'] == 0:
                    asset_type = 'image'
                elif asset['e'] == 1:
                    asset_type = 'video'
            elif 't' in asset and asset['t'] == 2:
                asset_type = 'audio'
            elif 'layers' in asset:
                asset_type = 'precomp'

            self.asset_references[asset_id] = asset

            if asset_type == 'precomp':
                self.create_precomp_composition(asset)
            else:
                self.jsx_code.append(f"// Asset: {asset_id} ({asset_type})")

        self.jsx_code.append("")

    def create_precomp_composition(self, asset: Dict):
        """Create a precomp composition"""
        comp_name = asset.get('nm', 'Precomp')
        comp_id = asset.get('id', '')
        width = asset.get('w', 1920)
        height = asset.get('h', 1080)
        frame_rate = asset.get('fr', 24)
        duration = 5.0  # Default duration

        if 'layers' in asset and len(asset['layers']) > 0:
            # Try to determine duration from layers
            max_out_point = 0
            for layer in asset['layers']:
                if 'op' in layer:
                    max_out_point = max(max_out_point, layer['op'])
            if max_out_point > 0:
                duration = max_out_point / frame_rate

        comp_var = f"comp_{comp_id.replace('comp_', '')}"
        self.comp_references[comp_id] = comp_var

        self.jsx_code.extend([
            f"// Create precomp: {comp_name}",
            f"var {comp_var} = app.project.items.addComp('{comp_name}', {width}, {height}, 1.0, {duration:.3f}, {frame_rate});",
            "",
        ])

        # Process layers in this precomp
        if 'layers' in asset:
            self.jsx_code.append(f"// Processing {len(asset['layers'])} layers in {comp_name}")

            # Sort layers by dependency order (parents first, then by normal index for AE stacking)
            sorted_layers = self.sort_layers_by_dependencies(asset['layers'])

            # Store current layer references to restore later
            old_layer_references = self.layer_references.copy()

            # Clear layer references for this precomp
            precomp_layer_references = {}

            for layer in sorted_layers:
                self.process_single_layer_in_precomp(layer, comp_var, precomp_layer_references)

            # Restore original layer references
            self.layer_references = old_layer_references

        self.jsx_code.append("")
    
    def process_fonts(self, fonts: Dict):
        """Process font data"""
        if 'list' in fonts:
            self.jsx_code.append("// Font References")
            for font in fonts['list']:
                font_name = font.get('fName', 'Unknown')
                font_family = font.get('fFamily', 'Unknown')
                self.jsx_code.append(f"// Font: {font_name} ({font_family})")
                self.font_references[font_name] = font
            self.jsx_code.append("")
    
    def process_layers(self, layers: List[Dict], parent_comp: str):
        """Process all layers in a composition"""
        self.jsx_code.append(f"// Creating {len(layers)} layers")

        # Sort layers by dependency order (parents first, then by normal index for AE stacking)
        sorted_layers = self.sort_layers_by_dependencies(layers)

        for layer in sorted_layers:
            self.process_single_layer(layer, parent_comp)

        self.jsx_code.append("")

    # REMOVED: Semantic analysis was causing incorrect layer ordering
    # The original JSON order is the correct visual stacking order

    def sort_layers_by_dependencies(self, layers: List[Dict]) -> List[Dict]:
        """Sort layers respecting ORIGINAL JSON ORDER and parent-child dependencies.
        The original JSON order is the intended visual stacking order."""

        # Create a map of layer index to layer data
        layer_map = {layer.get('ind', 0): layer for layer in layers}

        # Track processed layers
        processed = set()
        result = []

        def process_layer_and_dependencies(layer_index: int):
            if layer_index in processed or layer_index not in layer_map:
                return

            layer = layer_map[layer_index]

            # Process parent first if it exists
            if 'parent' in layer:
                parent_index = layer['parent']
                if parent_index in layer_map and parent_index not in processed:
                    process_layer_and_dependencies(parent_index)

            # Process track matte parent if it exists
            if 'tp' in layer:
                matte_index = layer['tp']
                if matte_index in layer_map and matte_index not in processed:
                    process_layer_and_dependencies(matte_index)

            # Add this layer to result
            result.append(layer)
            processed.add(layer_index)

        # PRESERVE ORIGINAL JSON ORDER: This is the intended visual stacking
        # Process layers in their original JSON order to maintain proper depth
        self.jsx_code.append("// Preserving original JSON layer order for correct visual stacking")

        for layer in layers:
            layer_index = layer.get('ind', 0)
            name = layer.get('nm', '')
            self.jsx_code.append(f"// {name} (index {layer_index})")
            process_layer_and_dependencies(layer_index)

        return result
    
    def process_single_layer(self, layer: Dict, parent_comp: str):
        """Process a single layer in the main composition"""
        layer_type = layer.get('ty', 0)
        layer_var = f"layer_{layer['ind']}"
        layer_name = layer.get('nm', f'Layer {layer["ind"]}')
        
        self.jsx_code.extend([
            f"try {{",
            f"    // Create layer: {layer_name}",
        ])
        
        # Create layer based on type
        if layer_type == 0:  # Precomp
            self.jsx_code.append(f"    var {layer_var} = {parent_comp}.layers.addComp('{layer_name}', {layer.get('w', 100)}, {layer.get('h', 100)}, 1, {layer.get('op', 60)}/{layer.get('sr', 24)}, {layer.get('sr', 24)});")
        elif layer_type == 1:  # Solid
            color = layer.get('sc', [1, 1, 1])
            if isinstance(color, list) and len(color) >= 3:
                color_array = f"[{color[0]}, {color[1]}, {color[2]}, 1]"
            else:
                color_array = "[1, 1, 1, 1]"
            self.jsx_code.append(f"    var {layer_var} = {parent_comp}.layers.addSolid({color_array}, '{layer_name}', {layer.get('w', 100)}, {layer.get('h', 100)}, 1);")
        elif layer_type == 2:  # Image
            self.jsx_code.append(f"    var {layer_var} = {parent_comp}.layers.addNull();")  # Placeholder for now
            self.jsx_code.append(f"    {layer_var}.name = '{layer_name}';")
        elif layer_type == 3:  # Null
            self.jsx_code.append(f"    var {layer_var} = {parent_comp}.layers.addNull();")
            self.jsx_code.append(f"    {layer_var}.name = '{layer_name}';")
        elif layer_type == 4:  # Shape
            self.jsx_code.append(f"    var {layer_var} = {parent_comp}.layers.addShape();")
            self.jsx_code.append(f"    {layer_var}.name = '{layer_name}';")
            if 'shapes' in layer:
                self.process_shapes(layer['shapes'], layer_var)
        elif layer_type == 5:  # Text
            self.jsx_code.append(f"    var {layer_var} = {parent_comp}.layers.addText();")
            self.jsx_code.append(f"    {layer_var}.name = '{layer_name}';")
        
        # Set common layer properties
        if 'ks' in layer:  # Transform properties
            self.set_transform_properties(layer['ks'], layer_var)
        
        # Set layer styles as effects
        if 'sy' in layer:
            self.process_layer_styles(layer, layer_var)
        
        # Set effects
        if 'ef' in layer:
            self.process_effects(layer['ef'], layer_var)
        
        # Set masks
        if 'masksProperties' in layer:
            self.process_masks(layer['masksProperties'], layer_var)
        
        # Set time remap
        if 'tm' in layer:
            self.process_time_remap(layer['tm'], layer_var)
        
        # Set blend mode
        if 'bm' in layer:
            blend_mode = self.get_blend_mode(layer['bm'])
            if blend_mode:
                self.jsx_code.append(f"    {layer_var}.blendingMode = {blend_mode};")
        
        # Set in/out points
        if 'ip' in layer:
            self.jsx_code.append(f"    {layer_var}.inPoint = {layer['ip']}/frameRate;")
        if 'op' in layer:
            self.jsx_code.append(f"    {layer_var}.outPoint = {layer['op']}/frameRate;")
        
        # Set visibility
        if 'hd' in layer and layer['hd']:
            self.jsx_code.append(f"    {layer_var}.enabled = false;")
        
        self.jsx_code.extend([
            "} catch(e) {",
            "    alert('Failed to create layer: ' + e.toString());",
            "}",
            ""
        ])

    def process_single_layer_in_precomp(self, layer: Dict, parent_comp_var: str, layer_references: Dict):
        """Process a single layer within a precomp composition"""
        # Temporarily store the current composition context
        old_comp_context = getattr(self, '_current_comp_var', 'comp')
        self._current_comp_var = parent_comp_var

        # Process the layer normally but with the precomp context
        self.process_single_layer(layer, parent_comp_var)

        # Store layer reference in the precomp's layer references
        layer_index = layer.get('ind', 0)
        layer_var = f"layer_{layer_index}"
        layer_references[layer_index] = layer_var

        # Restore the original composition context
        self._current_comp_var = old_comp_context

    def create_solid_layer(self, layer: Dict, parent_comp: str):
        """Create a solid layer"""
        name = layer.get('nm', 'Solid Layer')
        color = layer.get('sc', [1, 1, 1])
        
        # Ensure color values are properly formatted
        if isinstance(color, list) and len(color) >= 3:
            # Convert color values to proper decimal format
            r = float(color[0])
            g = float(color[1])
            b = float(color[2])
            # Ensure values are between 0 and 1
            r = min(max(r, 0), 1)
            g = min(max(g, 0), 1)
            b = min(max(b, 0), 1)
            color_array = f"[{r:.6f}, {g:.6f}, {b:.6f}]"
        else:
            # Default to white if color is invalid
            color_array = "[1, 1, 1]"
        
        width = layer.get('sw', 1920)
        height = layer.get('sh', 1080)
        
        self.jsx_code.extend([
            f"// Layer {layer.get('ind')}: {name}",
            f"var layer_{layer['ind']} = createSolidLayer({parent_comp}, '{name}', {color_array}, {width}, {height});"
        ])
    
    def create_shape_layer(self, layer: Dict, parent_comp: str):
        """Create shape layer with all shapes and effects"""
        layer_name = layer.get('nm', 'Shape')
        layer_index = layer.get('ind', 0)
        layer_var = f"layer_{layer_index}"

        # Get the current composition context
        comp_var = getattr(self, '_current_comp_var', 'comp')

        self.jsx_code.extend([
            f"var {layer_var} = createShapeLayer({comp_var}, '{layer_name}');",
        ])

        # Process shapes
        if 'shapes' in layer:
            self.process_shapes(layer['shapes'], layer_var)

        # Process effects (Drop Shadow, Glow, etc.)
        if 'ef' in layer:
            self.process_layer_effects(layer['ef'], layer_var)

    def process_layer_effects(self, effects: Union[List[Dict], Dict], layer_var: str):
        """Process layer effects"""
        # Handle both array and single effect object
        if isinstance(effects, dict):
            effects = [effects]
        elif not isinstance(effects, list):
            return
        
        # Only create effects property once
        self.jsx_code.append(f"var effectsProperty = {layer_var}.property('ADBE Effect Parade');")
        
        for i, effect in enumerate(effects):
            effect_type = effect.get('ty', 0)
            effect_name = effect.get('nm', f'Effect {i+1}')
            
            # Get the effect match name
            match_name = self.get_effect_match_name(effect_type)
            if not match_name:
                continue
            
            # Create effect with error handling
            self.jsx_code.extend([
                "try {",
                f"    var effect_{i} = effectsProperty.addProperty('{match_name}');",
                f"    effect_{i}.name = '{effect_name}';"
            ])
            
            # Process effect properties
            if 'ef' in effect:
                for prop in effect['ef']:
                    self.process_effect_property(prop, f"effect_{i}")
            
            self.jsx_code.append("} catch(e) { alert('Could not add effect: ' + e.toString()); }")
            self.jsx_code.append("")

    def create_drop_shadow_effect(self, effect: Dict, layer_var: str, index: int):
        """Create Drop Shadow effect"""
        effect_var = f"{layer_var}_dropShadow_{index}"

        self.jsx_code.extend([
            f"var {effect_var} = {layer_var}.property('Effects').addProperty('ADBE Drop Shadow');",
        ])

        # Process effect properties
        if 'ef' in effect:
            for prop in effect['ef']:
                self.process_effect_property(prop, effect_var)

    def create_glow_effect(self, effect: Dict, layer_var: str, index: int):
        """Create Glow effect"""
        effect_var = f"{layer_var}_glow_{index}"

        self.jsx_code.extend([
            f"var {effect_var} = {layer_var}.property('Effects').addProperty('ADBE Glow');",
        ])

        # Process effect properties
        if 'ef' in effect:
            for prop in effect['ef']:
                self.process_effect_property(prop, effect_var)

    def create_fill_effect(self, effect: Dict, layer_var: str, index: int):
        """Create Fill effect"""
        effect_var = f"{layer_var}_fill_{index}"

        self.jsx_code.extend([
            f"var {effect_var} = {layer_var}.property('Effects').addProperty('ADBE Fill');",
        ])

        # Process effect properties
        if 'ef' in effect:
            for prop in effect['ef']:
                self.process_effect_property(prop, effect_var)

    def create_stroke_effect(self, effect: Dict, layer_var: str, index: int):
        """Create Stroke effect"""
        effect_var = f"{layer_var}_stroke_{index}"

        self.jsx_code.extend([
            f"var {effect_var} = {layer_var}.property('Effects').addProperty('ADBE Stroke');",
        ])

        # Process effect properties
        if 'ef' in effect:
            for prop in effect['ef']:
                self.process_effect_property(prop, effect_var)

    def create_generic_effect(self, effect: Dict, layer_var: str, index: int):
        """Create generic effect with custom properties"""
        effect_name = effect.get('nm', f'Effect_{index}')
        effect_var = f"{layer_var}_effect_{index}"

        self.jsx_code.extend([
            f"// Generic effect: {effect_name}",
            f"// Note: This effect may need manual recreation in After Effects",
        ])

        # Process effect properties as comments for manual recreation
        if 'ef' in effect:
            for prop in effect['ef']:
                prop_name = prop.get('nm', 'Unknown')
                if 'v' in prop and 'k' in prop['v']:
                    value = prop['v']['k']
                    self.jsx_code.append(f"// Property '{prop_name}': {value}")

    def process_effect_property(self, prop: Dict, effect_var: str):
        """Process individual effect property with error handling and enhanced property support"""
        if not prop:
            return

        prop_name = prop.get('nm', '')
        prop_type = prop.get('ty', 0)
        
        # Get the property match name
        if 'mn' in prop:
            prop_match_name = prop['mn']
        else:
            prop_match_name = self.get_effect_property_match_name(prop_name, prop_type)
        
        if not prop_match_name:
            return

        prop_path = f"{effect_var}.property('{prop_match_name}')"

        # Handle expressions
        if prop.get('expressionEnabled', False) and 'expression' in prop:
            safe_exp = prop['expression'].replace('\\', '\\\\').replace("'", "\\'")
            self.jsx_code.append(f"try {{ {prop_path}.expression = '{safe_exp}'; }} catch(e) {{ }}")

        # Handle property value
        if 'v' in prop:
            value_data = prop['v']
            
            # Handle different property types
            if prop_type == 0:  # Slider/Number
                self.set_numeric_property(prop_path, value_data)
            elif prop_type == 1:  # Checkbox
                self.set_checkbox_property(prop_path, value_data)
            elif prop_type == 2:  # Color
                self.set_color_property(prop_path, value_data)
            elif prop_type == 3:  # Point
                self.set_point_property(prop_path, value_data)
            elif prop_type == 4:  # Layer
                self.set_layer_property(prop_path, value_data)
            elif prop_type == 7:  # Dropdown/Menu
                self.set_dropdown_property(prop_path, value_data)
            else:
                # Default handling for unknown types
                self.set_property_with_error_handling(prop_path, value_data)

    def get_effect_property_match_name(self, prop_name: str, prop_type: int) -> str:
        """Map effect property names to After Effects matchNames"""
        # Common property match names
        common_props = {
            'Color': 'ADBE Effect Built In Params 1',
            'Opacity': 'ADBE Effect Built In Params 2',
            'Blurriness': 'ADBE Gaussian Blur 2-0001',
            'Blur Dimensions': 'ADBE Gaussian Blur 2-0002',
            'Blur Radius': 'ADBE Radial Blur Blur Amount',
            'Shadow Color': 'ADBE Drop Shadow-0002',
            'Shadow Opacity': 'ADBE Drop Shadow-0001',
            'Shadow Distance': 'ADBE Drop Shadow-0003',
            'Shadow Softness': 'ADBE Drop Shadow-0004',
            'Grid Spacing': 'ADBE Grid-0001',
            'Grid Color': 'ADBE Grid-0002',
            'Amount': 'ADBE Turbulent Displace-0001',
            'Size': 'ADBE Turbulent Displace-0002',
            'Evolution': 'ADBE Turbulent Displace-0003',
            'Offset': 'ADBE Turbulent Displace-0004',
            'Slider': 'ADBE Slider Control-0001',
            'Point': 'ADBE Point Control-0001'
        }
        
        # Try to find exact match
        if prop_name in common_props:
            return common_props[prop_name]
            
        # Try to find partial match
        for key in common_props:
            if key.lower() in prop_name.lower():
                return common_props[key]
                
        # Return a generic property name based on type
        type_defaults = {
            0: 'ADBE Effect Built In Params 1',  # Number
            1: 'ADBE Effect Built In Params 2',  # Checkbox
            2: 'ADBE Effect Built In Params 3',  # Color
            3: 'ADBE Effect Built In Params 4',  # Point
            4: 'ADBE Effect Built In Params 5',  # Layer
            7: 'ADBE Effect Built In Params 6'   # Dropdown
        }
        return type_defaults.get(prop_type, '')

    def set_numeric_property(self, prop_path: str, value_data: Dict):
        """Set numeric property with proper handling of keyframes"""
        if isinstance(value_data, dict) and 'k' in value_data:
            if isinstance(value_data['k'], list):
                # Animated property
                self.set_keyframes_with_error_handling(prop_path, value_data['k'])
            else:
                # Static property
                self.set_property_with_error_handling(prop_path, value_data['k'])

    def set_checkbox_property(self, prop_path: str, value_data: Dict):
        """Set checkbox property"""
        if isinstance(value_data, dict) and 'k' in value_data:
            value = bool(value_data['k'])
            self.jsx_code.append(f"try {{ {prop_path}.setValue({str(value).lower()}); }} catch(e) {{ }}")

    def set_color_property(self, prop_path: str, value_data: Dict):
        """Set color property with proper RGB array format"""
        if isinstance(value_data, dict) and 'k' in value_data:
            color_data = value_data['k']
            
            # Handle static color
            if isinstance(color_data, list):
                # Ensure we have exactly 3 values for RGB
                if len(color_data) >= 3:
                    r = min(max(color_data[0], 0), 1)
                    g = min(max(color_data[1], 0), 1)
                    b = min(max(color_data[2], 0), 1)
                    self.jsx_code.append(
                        f"try {{ {prop_path}.setValue([{r:.3f}, {g:.3f}, {b:.3f}]); }} "
                        f"catch(e) {{ alert('Failed to set color property: ' + e.toString()); }}"
                    )
                else:
                    self.jsx_code.append(
                        f"alert('Invalid color data: Expected at least 3 values for RGB');"
                    )
            # Handle animated color
            elif isinstance(color_data, dict) and 'k' in color_data:
                keyframes = color_data['k']
                if isinstance(keyframes, list):
                    self.set_keyframes_with_error_handling(prop_path, keyframes)
                else:
                    self.jsx_code.append(
                        f"alert('Invalid animated color data format');"
                    )

    def set_point_property(self, prop_path: str, value_data: Dict):
        """Set point property"""
        if isinstance(value_data, dict) and 'k' in value_data:
            point = value_data['k']
            if isinstance(point, list) and len(point) >= 2:
                point_str = f"[{point[0]}, {point[1]}]"
                self.jsx_code.append(f"try {{ {prop_path}.setValue({point_str}); }} catch(e) {{ }}")

    def set_layer_property(self, prop_path: str, value_data: Dict):
        """Set layer reference property"""
        if isinstance(value_data, dict) and 'k' in value_data:
            layer_index = value_data['k']
            if layer_index in self.layer_references:
                layer_var = self.layer_references[layer_index]
                self.jsx_code.append(f"try {{ {prop_path}.setValue({layer_var}.index); }} catch(e) {{ }}")

    def set_dropdown_property(self, prop_path: str, value_data: Dict):
        """Set dropdown/menu property"""
        if isinstance(value_data, dict) and 'k' in value_data:
            value = value_data['k']
            self.jsx_code.append(f"try {{ {prop_path}.setValue({value}); }} catch(e) {{ }}")

    def set_keyframes_with_error_handling(self, prop_path: str, keyframes: List[Dict]):
        """Set keyframes with proper error handling"""
        self.jsx_code.append(f"try {{")
        for kf in keyframes:
            time = kf.get('t', 0)
            value = kf.get('s', [])
            
            # Handle different value types
            if isinstance(value, list):
                if len(value) == 1:
                    value_str = str(value[0])
                else:
                    value_str = f"[{', '.join(str(x) for x in value)}]"
            else:
                value_str = str(value)
            
            self.jsx_code.append(f"    {prop_path}.setValueAtTime({time}/frameRate, {value_str});")
            
            # Handle easing if available
            if 'i' in kf and 'o' in kf:
                self.jsx_code.append(f"    var keyIndex = {prop_path}.nearestKeyIndex({time}/frameRate);")
                self.jsx_code.append(f"    {prop_path}.setTemporalEaseAtKey(keyIndex, {kf['i']}, {kf['o']});")
                
        self.jsx_code.append("} catch(e) { /* Could not set keyframes */ }")

    def map_effect_property_name(self, lottie_name: str, prop_type: int) -> str:
        """Map Lottie property names to After Effects property names"""
        name_mapping = {
            'Shadow Color': 'Shadow Color',
            'Opacity': 'Opacity',
            'Direction': 'Direction',
            'Distance': 'Distance',
            'Softness': 'Softness',
            'Glow Color': 'Glow Color',
            'Glow Intensity': 'Glow Intensity',
            'Glow Radius': 'Glow Radius',
            'Fill Color': 'Color',
            'Stroke Color': 'Color',
            'Stroke Width': 'Brush Size'
        }

        return name_mapping.get(lottie_name, lottie_name)

    def create_null_layer(self, layer: Dict, parent_comp: str):
        """Create null layer"""
        layer_name = layer.get('nm', 'Null')
        layer_index = layer.get('ind', 0)
        layer_var = f"layer_{layer_index}"

        # Get the current composition context
        comp_var = getattr(self, '_current_comp_var', 'comp')

        self.jsx_code.extend([
            f"var {layer_var} = createNullLayer({comp_var}, '{layer_name}');",
        ])

    def create_text_layer(self, layer: Dict, parent_comp: str):
        """Create text layer with proper text content"""
        layer_name = layer.get('nm', 'Text')
        layer_index = layer.get('ind', 0)
        layer_var = f"layer_{layer_index}"

        # Get the current composition context
        comp_var = getattr(self, '_current_comp_var', 'comp')

        self.jsx_code.extend([
            f"var {layer_var} = createTextLayer({comp_var}, '{layer_name}');",
        ])

        # Set text properties from Lottie data
        if 't' in layer and 'd' in layer['t'] and 'k' in layer['t']['d']:
            text_data = layer['t']['d']['k']
            if text_data and len(text_data) > 0:
                text_info = text_data[0].get('s', {})
                text_content = text_info.get('t', '').replace("'", "\\'")  # Escape quotes
                font_size = text_info.get('s', 50)
                font_name = text_info.get('f', 'Arial')

                # Handle text color
                if 'fc' in text_info:
                    color = text_info['fc']
                    if len(color) >= 3:
                        color_str = f"[{color[0]:.6f}, {color[1]:.6f}, {color[2]:.6f}]"
                    else:
                        color_str = "[1, 1, 1]"
                else:
                    color_str = "[1, 1, 1]"

                # Handle text justification
                justification = text_info.get('j', 0)  # 0=left, 1=right, 2=center
                justification_map = {
                    0: "ParagraphJustification.LEFT_JUSTIFY",
                    1: "ParagraphJustification.RIGHT_JUSTIFY",
                    2: "ParagraphJustification.CENTER_JUSTIFY"
                }
                justify_str = justification_map.get(justification, "ParagraphJustification.LEFT_JUSTIFY")

                self.jsx_code.extend([
                    f"var textDoc = {layer_var}.property('Source Text');",
                    f"var textValue = textDoc.value;",
                    f"textValue.text = '{text_content}';",
                    f"textValue.font = '{font_name}';",
                    f"textValue.fontSize = {font_size};",
                    f"textValue.fillColor = {color_str};",
                    f"textValue.justification = {justify_str};",
                    f"textDoc.setValue(textValue);",
                ])
            else:
                self.jsx_code.append(f"// No text data found for {layer_name}")
        else:
            self.jsx_code.append(f"// No text properties found for {layer_name}")
    
    def create_precomp_layer(self, layer: Dict, parent_comp: str):
        """Create precomp layer with actual precomp reference"""
        layer_name = layer.get('nm', 'Precomp')
        layer_index = layer.get('ind', 0)
        layer_var = f"layer_{layer_index}"
        ref_id = layer.get('refId', '')

        # Get the current composition context
        comp_var = parent_comp

        if ref_id in self.comp_references:
            # Reference the actual precomp
            precomp_var = self.comp_references[ref_id]
            self.jsx_code.extend([
                f"try {{",
                f"    // Add precomp layer",
                f"    var {layer_var} = {comp_var}.layers.add({precomp_var});",
                f"    {layer_var}.name = '{layer_name}';",
                f"}} catch(e) {{",
                f"    alert('Failed to create precomp layer {layer_name}: ' + e.toString());",
                f"    // Create a placeholder null layer",
                f"    {layer_var} = createNullLayer({comp_var}, '{layer_name} (Failed Precomp: {ref_id})');",
                f"}}",
                "",
            ])
        else:
            # Fallback to null layer if precomp not found
            self.jsx_code.extend([
                f"// Precomp {ref_id} not found, creating placeholder",
                f"var {layer_var} = createNullLayer({comp_var}, '{layer_name} (Missing Precomp: {ref_id})');",
                "",
            ])

        return layer_var

    def create_image_layer(self, layer: Dict, parent_comp: str):
        """Create image layer with smart file dialog prompts"""
        layer_name = layer.get('nm', 'Image')
        layer_index = layer.get('ind', 0)
        layer_var = f"layer_{layer_index}"
        ref_id = layer.get('refId', '')

        # Get asset information with enhanced metadata
        asset_info = self.asset_references.get(ref_id, {})
        original_path = ''
        dimensions = ''
        description = ''

        if asset_info:
            # Build original path from asset data
            folder = asset_info.get('u', '')
            filename = asset_info.get('p', '')
            if folder and filename:
                original_path = folder + filename

            # Get dimensions if available
            width = asset_info.get('w', 0)
            height = asset_info.get('h', 0)
            if width and height:
                dimensions = f"{width}x{height}"

            # Generate smart description
            description = self.get_asset_description_from_metadata(asset_info, layer_name)

        self.jsx_code.extend([
            f"// Image Layer: {layer_name} (Asset: {ref_id})",
            f"try {{",
            f"    var {layer_var} = importAssetWithDialog({parent_comp}, '{ref_id}', '{original_path}', 'image', '{layer_name}', '{dimensions}', '{description}');",
            f"}} catch(e) {{",
            f"    alert('Failed to create image layer: ' + e.toString());",
            f"}}",
        ])
        
        return layer_var

    def get_asset_description_from_metadata(self, asset_info: Dict, layer_name: str) -> str:
        """Generate intelligent description based on asset metadata and context"""
        asset_name = asset_info.get('nm', layer_name).lower()
        filename = asset_info.get('p', '').lower()

        # Analyze name patterns for smart descriptions
        if any(keyword in asset_name for keyword in ['logo', 'brand', 'company']):
            return 'Company or brand logo image'
        elif any(keyword in asset_name for keyword in ['background', 'bg', 'backdrop']):
            return 'Background image for the scene'
        elif any(keyword in asset_name for keyword in ['placeholder', 'temp', 'dummy']):
            return 'Placeholder image to be replaced with final content'
        elif any(keyword in asset_name for keyword in ['character', 'person', 'avatar']):
            return 'Character or person image'
        elif any(keyword in asset_name for keyword in ['icon', 'symbol', 'graphic']):
            return 'Icon or graphic element'
        elif any(keyword in asset_name for keyword in ['texture', 'pattern', 'material']):
            return 'Texture or pattern overlay'
        elif any(keyword in filename for keyword in ['img', 'image', 'pic', 'photo']):
            return 'General image asset for visual content'
        elif any(keyword in filename for keyword in ['music', 'song', 'track']):
            return 'Background music or soundtrack'
        elif any(keyword in filename for keyword in ['sound', 'sfx', 'effect', 'audio']):
            return 'Sound effect or audio clip'
        elif any(keyword in filename for keyword in ['voice', 'speech', 'narration']):
            return 'Voice-over or narration audio'
        else:
            return 'Media asset for the animation'

    def create_audio_layer(self, layer: Dict, parent_comp: str):
        """Create audio layer with smart file dialog prompts"""
        layer_name = layer.get('nm', 'Audio')
        layer_index = layer.get('ind', 0)
        layer_var = f"layer_{layer_index}"
        ref_id = layer.get('refId', '')

        # Get asset information with enhanced metadata
        asset_info = self.asset_references.get(ref_id, {})
        original_path = ''
        description = ''

        if asset_info:
            # Build original path from asset data
            folder = asset_info.get('u', '')
            filename = asset_info.get('p', '')
            if folder and filename:
                original_path = folder + filename

            # Generate smart description
            description = self.get_asset_description_from_metadata(asset_info, layer_name)

        self.jsx_code.extend([
            f"// Audio Layer: {layer_name} (Asset: {ref_id})",
            f"try {{",
            f"    var {layer_var} = importAssetWithDialog({parent_comp}, '{ref_id}', '{original_path}', 'audio', '{layer_name}', '', '{description}');",
            f"}} catch(e) {{",
            f"    alert('Failed to create audio layer: ' + e.toString());",
            f"}}",
        ])
        
        return layer_var

    def process_shapes(self, shapes: List[Dict], layer_var: str):
        """Process all shapes in a shape layer"""
        for i, shape in enumerate(shapes):
            shape_type = shape.get('ty', '')

            if shape_type == 'gr':  # Group
                self.process_shape_group(shape, layer_var, i)
            elif shape_type == 'el':  # Ellipse
                self.process_ellipse_direct(shape, layer_var, i)
            elif shape_type == 'rc':  # Rectangle
                self.process_rectangle_direct(shape, layer_var, i)
            elif shape_type == 'sh':  # Path
                self.process_path_direct(shape, layer_var, i)
            elif shape_type == 'fl':  # Fill
                self.process_fill_direct(shape, layer_var, i)
            elif shape_type == 'st':  # Stroke
                self.process_stroke_direct(shape, layer_var, i)
            elif shape_type == 'tm':  # Trim
                self.process_trim_direct(shape, layer_var, i)
            elif shape_type == 'tr':  # Transform
                self.process_shape_transform_direct(shape, layer_var, i)

    def process_ellipse_direct(self, shape: Dict, layer_var: str, index: int):
        """Process ellipse shape directly in layer"""
        ellipse_var = f"{layer_var}_ellipse_{index}"
        self.jsx_code.extend([
            f"var {ellipse_var} = {layer_var}.property('Contents').addProperty('ADBE Vector Shape - Ellipse');",
        ])

        if 's' in shape and 'k' in shape['s']:
            size_data = shape['s']['k']
            if isinstance(size_data, list) and len(size_data) >= 2:
                self.jsx_code.append(f"{ellipse_var}.property('Size').setValue([{size_data[0]}, {size_data[1]}]);")

    def process_rectangle_direct(self, shape: Dict, layer_var: str, index: int):
        """Process rectangle shape directly in layer"""
        rect_var = f"{layer_var}_rect_{index}"
        self.jsx_code.extend([
            f"var {rect_var} = {layer_var}.property('Contents').addProperty('ADBE Vector Shape - Rect');",
        ])

        if 's' in shape and 'k' in shape['s']:
            size_data = shape['s']['k']
            if isinstance(size_data, list) and len(size_data) >= 2:
                self.jsx_code.append(f"{rect_var}.property('Size').setValue([{size_data[0]}, {size_data[1]}]);")

    def process_path_direct(self, shape: Dict, layer_var: str, index: int):
        """Process path shape directly in layer"""
        path_var = f"{layer_var}_path_{index}"
        self.jsx_code.extend([
            f"var {path_var} = {layer_var}.property('Contents').addProperty('ADBE Vector Shape - Group');",
        ])

        if 'ks' in shape and 'k' in shape['ks']:
            path_data = shape['ks']['k']
            if isinstance(path_data, dict):
                self.set_static_path(path_var, path_data)
            elif isinstance(path_data, list):
                self.set_animated_path(path_var, path_data)

    def process_fill_direct(self, shape: Dict, layer_var: str, index: int):
        """Process fill directly in layer"""
        fill_var = f"{layer_var}_fill_{index}"
        self.jsx_code.extend([
            f"var {fill_var} = {layer_var}.property('Contents').addProperty('ADBE Vector Graphic - Fill');",
        ])

        if 'c' in shape and 'k' in shape['c']:
            color_data = shape['c']['k']
            if isinstance(color_data, list) and len(color_data) >= 3:
                color_str = f"[{color_data[0]:.6f}, {color_data[1]:.6f}, {color_data[2]:.6f}]"
                self.jsx_code.append(f"{fill_var}.property('Color').setValue({color_str});")

    def process_stroke_direct(self, shape: Dict, layer_var: str, index: int):
        """Process stroke directly in layer"""
        stroke_var = f"{layer_var}_stroke_{index}"
        self.jsx_code.extend([
            f"var {stroke_var} = {layer_var}.property('Contents').addProperty('ADBE Vector Graphic - Stroke');",
        ])

        if 'c' in shape and 'k' in shape['c']:
            color_data = shape['c']['k']
            if isinstance(color_data, list) and len(color_data) >= 3:
                color_str = f"[{color_data[0]:.6f}, {color_data[1]:.6f}, {color_data[2]:.6f}]"
                self.jsx_code.append(f"{stroke_var}.property('Color').setValue({color_str});")

        if 'w' in shape and 'k' in shape['w']:
            width_data = shape['w']['k']
            if isinstance(width_data, (int, float)):
                self.jsx_code.append(f"{stroke_var}.property('Stroke Width').setValue({width_data});")

    def process_trim_direct(self, shape: Dict, layer_var: str, index: int):
        """Process trim paths directly in layer"""
        trim_var = f"{layer_var}_trim_{index}"
        self.jsx_code.extend([
            f"var {trim_var} = {layer_var}.property('Contents').addProperty('ADBE Vector Filter - Trim');",
        ])

        if 's' in shape:
            self.set_property_keyframes(f"{trim_var}.property('Start')", shape['s'])
        if 'e' in shape:
            self.set_property_keyframes(f"{trim_var}.property('End')", shape['e'])
        if 'o' in shape:
            self.set_property_keyframes(f"{trim_var}.property('Offset')", shape['o'])

    def process_shape_transform_direct(self, shape: Dict, layer_var: str, index: int):
        """Process shape transform directly in layer"""
        # Shape transforms are handled differently - this is a placeholder
        self.jsx_code.append(f"// Shape transform {index} - TODO: implement")

    def process_rectangle_in_group(self, item: Dict, parent_var: str, index: int):
        """Process rectangle shape in group"""
        rect_var = f"{parent_var}_rect_{index}"
        self.jsx_code.extend([
            f"var {rect_var} = {parent_var}.property('Contents').addProperty('ADBE Vector Shape - Rect');",
        ])

        if 's' in item and 'k' in item['s']:
            size_data = item['s']['k']
            if isinstance(size_data, list) and len(size_data) >= 2:
                self.jsx_code.append(f"{rect_var}.property('Size').setValue([{size_data[0]}, {size_data[1]}]);")

        if 'p' in item and 'k' in item['p']:
            pos_data = item['p']['k']
            if isinstance(pos_data, list) and len(pos_data) >= 2:
                self.jsx_code.append(f"{rect_var}.property('Position').setValue([{pos_data[0]}, {pos_data[1]}]);")

    def process_transform_in_group(self, item: Dict, parent_var: str, index: int):
        """Process transform in group"""
        transform_var = f"{parent_var}_transform"
        self.jsx_code.append(f"var {transform_var} = {parent_var}.property('Transform');")

        # Set transform properties
        if 'p' in item:
            self.set_property_keyframes(f"{transform_var}.property('Position')", item['p'])
        if 's' in item:
            self.set_property_keyframes(f"{transform_var}.property('Scale')", item['s'])
        if 'r' in item:
            self.set_property_keyframes(f"{transform_var}.property('Rotation')", item['r'])
        if 'o' in item:
            self.set_property_keyframes(f"{transform_var}.property('Opacity')", item['o'])

    def process_shape_group(self, shape: Dict, layer_var: str, index: int):
        """Process shape group"""
        group_name = shape.get('nm', f'Group {index+1}')
        group_var = f"{layer_var}_group_{index}"

        self.jsx_code.extend([
            f"// Shape Group: {group_name}",
            f"var {group_var} = {layer_var}.property('Contents').addProperty('ADBE Vector Group');",
            f"{group_var}.name = '{group_name}';",
        ])

        # Process items in group
        if 'it' in shape:
            for item_index, item in enumerate(shape['it']):
                self.process_shape_item(item, group_var, item_index)

    def process_shape_item(self, item: Dict, parent_var: str, index: int):
        """Process individual shape items"""
        item_type = item.get('ty', '')

        if item_type == 'sh':  # Path
            self.process_path_in_group(item, parent_var, index)
        elif item_type == 'el':  # Ellipse
            self.process_ellipse_in_group(item, parent_var, index)
        elif item_type == 'rc':  # Rectangle
            self.process_rectangle_in_group(item, parent_var, index)
        elif item_type == 'fl':  # Fill
            self.process_fill_in_group(item, parent_var, index)
        elif item_type == 'st':  # Stroke
            self.process_stroke_in_group(item, parent_var, index)
        elif item_type == 'tm':  # Trim
            self.process_trim_in_group(item, parent_var, index)
        elif item_type == 'tr':  # Transform
            self.process_transform_in_group(item, parent_var, index)

    def process_path_in_group(self, item: Dict, parent_var: str, index: int):
        """Process path shape in group"""
        path_var = f"{parent_var}_path_{index}"

        self.jsx_code.extend([
            f"var {path_var} = {parent_var}.property('Contents').addProperty('ADBE Vector Shape - Group');",
        ])

        # Set path data
        if 'ks' in item and 'k' in item['ks']:
            path_data = item['ks']['k']
            if isinstance(path_data, dict):
                # Static path
                self.set_static_path(path_var, path_data)
            elif isinstance(path_data, list):
                # Animated path
                self.set_animated_path(path_var, path_data)

    def process_ellipse_in_group(self, item: Dict, parent_var: str, index: int):
        """Process ellipse shape in group"""
        ellipse_var = f"{parent_var}_ellipse_{index}"

        self.jsx_code.extend([
            f"var {ellipse_var} = {parent_var}.property('Contents').addProperty('ADBE Vector Shape - Ellipse');",
        ])

        # Set ellipse size
        if 's' in item and 'k' in item['s']:
            size_data = item['s']['k']
            if isinstance(size_data, list) and len(size_data) >= 2:
                self.jsx_code.append(f"{ellipse_var}.property('Size').setValue([{size_data[0]}, {size_data[1]}]);")

        # Set ellipse position
        if 'p' in item and 'k' in item['p']:
            pos_data = item['p']['k']
            if isinstance(pos_data, list) and len(pos_data) >= 2:
                self.jsx_code.append(f"{ellipse_var}.property('Position').setValue([{pos_data[0]}, {pos_data[1]}]);")

    def process_fill_in_group(self, item: Dict, parent_var: str, index: int):
        """Process fill in group"""
        fill_var = f"{parent_var}_fill_{index}"

        self.jsx_code.extend([
            f"var {fill_var} = {parent_var}.property('Contents').addProperty('ADBE Vector Graphic - Fill');",
        ])

        # Set fill color
        if 'c' in item and 'k' in item['c']:
            color_data = item['c']['k']
            if isinstance(color_data, list) and len(color_data) >= 3:
                color_str = f"[{color_data[0]:.6f}, {color_data[1]:.6f}, {color_data[2]:.6f}]"
                self.jsx_code.append(f"{fill_var}.property('Color').setValue({color_str});")

        # Set fill opacity
        if 'o' in item and 'k' in item['o']:
            opacity_data = item['o']['k']
            if isinstance(opacity_data, (int, float)):
                self.jsx_code.append(f"{fill_var}.property('Opacity').setValue({opacity_data});")

    def process_stroke_in_group(self, item: Dict, parent_var: str, index: int):
        """Process stroke in group"""
        stroke_var = f"{parent_var}_stroke_{index}"

        self.jsx_code.extend([
            f"var {stroke_var} = {parent_var}.property('Contents').addProperty('ADBE Vector Graphic - Stroke');",
        ])

        # Set stroke color
        if 'c' in item and 'k' in item['c']:
            color_data = item['c']['k']
            if isinstance(color_data, list) and len(color_data) >= 3:
                color_str = f"[{color_data[0]:.6f}, {color_data[1]:.6f}, {color_data[2]:.6f}]"
                self.jsx_code.append(f"{stroke_var}.property('Color').setValue({color_str});")

        # Set stroke width
        if 'w' in item and 'k' in item['w']:
            width_data = item['w']['k']
            if isinstance(width_data, (int, float)):
                self.jsx_code.append(f"{stroke_var}.property('Stroke Width').setValue({width_data});")

        # Set stroke opacity
        if 'o' in item and 'k' in item['o']:
            opacity_data = item['o']['k']
            if isinstance(opacity_data, (int, float)):
                self.jsx_code.append(f"{stroke_var}.property('Opacity').setValue({opacity_data});")

    def process_trim_in_group(self, item: Dict, parent_var: str, index: int):
        """Process trim paths in group"""
        trim_var = f"{parent_var}_trim_{index}"

        self.jsx_code.extend([
            f"var {trim_var} = {parent_var}.property('Contents').addProperty('ADBE Vector Filter - Trim');",
        ])

        # Set trim start
        if 's' in item:
            self.set_property_keyframes(f"{trim_var}.property('Start')", item['s'])

        # Set trim end
        if 'e' in item:
            self.set_property_keyframes(f"{trim_var}.property('End')", item['e'])

        # Set trim offset
        if 'o' in item:
            self.set_property_keyframes(f"{trim_var}.property('Offset')", item['o'])

    def set_static_path(self, path_var: str, path_data: Dict):
        """Set static path data"""
        if 'v' in path_data and 'i' in path_data and 'o' in path_data:
            vertices = path_data['v']
            in_tangents = path_data['i']
            out_tangents = path_data['o']
            closed = path_data.get('c', False)

            self.jsx_code.extend([
                f"var pathShape = new Shape();",
                f"pathShape.vertices = {vertices};",
                f"pathShape.inTangents = {in_tangents};",
                f"pathShape.outTangents = {out_tangents};",
                f"pathShape.closed = {str(closed).lower()};",
                f"{path_var}.property('Path').setValue(pathShape);",
            ])

    def set_animated_path(self, path_var: str, keyframes: List[Dict]):
        """Set animated path data with improved handling"""
        self.jsx_code.append(f"// Animated path with {len(keyframes)} keyframes")

        # Process all keyframes
        for i, kf in enumerate(keyframes):
            time = kf.get('t', 0)
            if 's' in kf:
                path_data = kf['s']
                if isinstance(path_data, list) and len(path_data) > 0:
                    # Handle case where 's' contains an array of path data
                    path_data = path_data[0]

                if isinstance(path_data, dict) and 'v' in path_data:
                    vertices = path_data['v']
                    in_tangents = path_data.get('i', [])
                    out_tangents = path_data.get('o', [])
                    closed = path_data.get('c', False)

                    self.jsx_code.extend([
                        f"var pathShape_{i} = new Shape();",
                        f"pathShape_{i}.vertices = {vertices};",
                        f"pathShape_{i}.inTangents = {in_tangents};",
                        f"pathShape_{i}.outTangents = {out_tangents};",
                        f"pathShape_{i}.closed = {str(closed).lower()};",
                        f"{path_var}.property('Path').setValueAtTime({time}/frameRate, pathShape_{i});",
                    ])
                else:
                    self.jsx_code.append(f"// Skipping keyframe {i} - invalid path data structure")
            else:
                self.jsx_code.append(f"// Skipping keyframe {i} - no 's' data")

    def set_property_keyframes(self, property_path: str, property_data: Dict):
        """Set keyframes for any property with smart keyframe detection"""
        # Skip if property_data is actually a list of raw keyframe objects
        if isinstance(property_data, list):
            # Check if it's a list of keyframe objects
            if len(property_data) > 0 and isinstance(property_data[0], dict) and 't' in property_data[0]:
                self.jsx_code.append(f"// Processing {len(property_data)} raw keyframes for {property_path}")
                for kf in property_data:
                    time = kf.get('t', 0)
                    value = kf.get('s', 0)

                    # Handle value properly - NEVER output keyframe objects
                    if isinstance(value, list):
                        if len(value) == 1:
                            value_str = str(value[0])
                        else:
                            value_str = f"[{', '.join(map(str, value))}]"
                    else:
                        value_str = str(value)

                    self.jsx_code.append(f"{property_path}.setValueAtTime({time}/frameRate, {value_str});")
            else:
                self.jsx_code.append(f"// Skipping invalid keyframe data for {property_path}")
            return

        # SMART KEYFRAME DETECTION - Check for keyframes regardless of 'a' flag
        if 'k' in property_data:
            k_data = property_data['k']

            # Smart detection: if 'k' contains a list with keyframe objects, treat as animated
            if isinstance(k_data, list) and len(k_data) > 0:
                # Check if first item looks like a keyframe (has 't' and 's')
                first_item = k_data[0]
                if isinstance(first_item, dict) and 't' in first_item and 's' in first_item:
                    # This is definitely a keyframe array - process all keyframes
                    self.jsx_code.append(f"// Smart detection: {len(k_data)} keyframes for {property_path}")

                    for kf in k_data:
                        time = kf.get('t', 0)
                        value = kf.get('s', 0)

                        # Handle value properly - NEVER output keyframe objects
                        if isinstance(value, list):
                            if len(value) == 1:
                                # Single value in array - extract it
                                value_str = str(value[0])
                            else:
                                # Multiple values - keep as array
                                value_str = f"[{', '.join(map(str, value))}]"
                        else:
                            value_str = str(value)

                        self.jsx_code.append(f"{property_path}.setValueAtTime({time}/frameRate, {value_str});")
                    return

        # Handle static values and other cases
        if 'k' in property_data:
            k_data = property_data['k']

            # Not keyframes, treat as static array value
            if isinstance(k_data, list):
                if len(k_data) == 1:
                    # Single value in array - extract it for single-dimension properties
                    if 'Position' in property_path and ('X Position' in property_path or 'Y Position' in property_path):
                        value_str = str(k_data[0])
                    else:
                        value_str = f"[{', '.join(map(str, k_data))}]"
                else:
                    # Multiple values - keep as array
                    value_str = f"[{', '.join(map(str, k_data))}]"

                self.jsx_code.append(f"{property_path}.setValue({value_str});")
                return

            # Single keyframe object in 'k' - extract the value
            elif isinstance(k_data, dict) and 's' in k_data:
                # This is a keyframe object, extract the value
                value = k_data['s']
                if isinstance(value, list):
                    if len(value) == 1:
                        value_str = str(value[0])
                    else:
                        value_str = f"[{', '.join(map(str, value))}]"
                else:
                    value_str = str(value)
                self.jsx_code.append(f"{property_path}.setValue({value_str});")
                return

            # Regular static value
            else:
                value_str = str(k_data)
                self.jsx_code.append(f"{property_path}.setValue({value_str});")
                return

        # Fallback: check legacy 'a' flag method (for compatibility)
        if 'a' in property_data and property_data['a'] == 1:
            # Animated property with explicit flag
            if 'k' in property_data and isinstance(property_data['k'], list):
                keyframes = property_data['k']
                self.jsx_code.append(f"// Legacy animated: {len(keyframes)} keyframes for {property_path}")

                for kf in keyframes:
                    time = kf.get('t', 0)
                    value = kf.get('s', 0)

                    # Handle single values vs arrays properly - NEVER output keyframe objects
                    if isinstance(value, list):
                        if len(value) == 1:
                            # Single value in array - extract it
                            value_str = str(value[0])
                        else:
                            # Multiple values - keep as array
                            value_str = f"[{', '.join(map(str, value))}]"
                    else:
                        value_str = str(value)

                    self.jsx_code.append(f"{property_path}.setValueAtTime({time}/frameRate, {value_str});")

        # If we get here, no valid data was found
        self.jsx_code.append(f"// No valid keyframe data found for {property_path}")

    def set_layer_properties(self, layer: Dict, layer_var: str):
        """Set all layer properties with proper error handling"""
        # Transform properties
        if 'ks' in layer:
            self.set_transform_properties(layer['ks'], layer_var)
        
        # Layer specific properties
        self.jsx_code.extend([
            f"try {{",
            f"    // Set basic layer properties",
            f"    {layer_var}.startTime = {layer.get('st', 0)}/comp.frameRate;",
            f"    {layer_var}.inPoint = {layer.get('ip', 0)}/comp.frameRate;",
            f"    {layer_var}.outPoint = {layer.get('op', 0)}/comp.frameRate;",
            f"    {layer_var}.enabled = {str(not layer.get('hd', False)).lower()};",  # Hidden property
            "",
            f"    // Set blend mode if specified",
            f"    if ({layer.get('bm', 0)} !== 0) {{",
            f"        {layer_var}.blendingMode = {self.get_blend_mode(layer.get('bm', 0))};",
            f"    }}",
            "",
            f"    // Set auto-orient if specified",
            f"    {layer_var}.autoOrient = {layer.get('ao', 0)};",
            "",
            f"    // Set time remapping if present",
            f"    if ({layer_var}.canSetTimeRemapEnabled && {str(bool(layer.get('tm', False))).lower()}) {{",
            f"        {layer_var}.timeRemapEnabled = true;",
            f"        if ({layer_var}.timeRemap) {{",
            f"            this.process_time_remap({json.dumps(layer.get('tm', {}))}, {layer_var});",
            f"        }}",
            f"    }}",
            f"}} catch(e) {{",
            f"    alert('Failed to set layer properties for {layer_var}: ' + e.toString());",
            f"}}",
        ])
        
        # Effects
        if 'ef' in layer:
            self.process_effects(layer['ef'], layer_var)
        
        # Masks
        if 'masksProperties' in layer:
            self.process_masks(layer['masksProperties'], layer_var)
        
        # Layer styles
        if 'sy' in layer:
            self.process_layer_styles(layer['sy'], layer_var)

    def process_masks(self, masks: List[Dict], layer_var: str):
        """Process layer masks with all properties"""
        if not masks:
            return

        for i, mask in enumerate(masks):
            mask_name = mask.get('nm', f'Mask {i+1}')
            mask_var = f"{layer_var}_mask_{i}"

            self.jsx_code.extend([
                f"var {mask_var} = {layer_var}.property('ADBE Mask Parade').addProperty('ADBE Mask Atom');",
                f"{mask_var}.name = '{mask_name}';"
            ])

            # Set mask shape
            if 'pt' in mask:
                self.set_mask_shape(mask['pt'], mask_var)

            # Set mask mode
            if 'mode' in mask:
                mode = self.get_mask_mode(mask['mode'])
                self.jsx_code.append(f"{mask_var}.property('ADBE Mask Mode').setValue({mode});")

            # Set mask opacity
            if 'o' in mask:
                self.set_property_with_error_handling(f"{mask_var}.property('ADBE Mask Opacity')", mask['o'])

            # Set mask expansion
            if 'x' in mask:
                self.set_property_with_error_handling(f"{mask_var}.property('ADBE Mask Offset')", mask['x'])

            # Set mask feather
            if 'f' in mask:
                self.set_property_with_error_handling(f"{mask_var}.property('ADBE Mask Feather')", mask['f'])

    def get_mask_mode(self, mode: int) -> str:
        """Convert mask mode value to AE constant"""
        mode_map = {
            'a': "MaskMode.ADD",
            's': "MaskMode.SUBTRACT",
            'i': "MaskMode.INTERSECT",
            'l': "MaskMode.LIGHTEN",
            'd': "MaskMode.DARKEN",
            'f': "MaskMode.DIFFERENCE"
        }
        return mode_map.get(mode, "MaskMode.ADD")

    def process_time_remap(self, time_remap: Dict, layer_var: str):
        """Process layer time remap"""
        if 'k' in time_remap:
            self.jsx_code.append(f"{layer_var}.timeRemapEnabled = true;")
            self.set_property_with_error_handling(f"{layer_var}.property('ADBE Time Remapping')", time_remap)

    def set_transform_properties(self, transform: Dict, layer_var: str):
        """Set transform properties for a layer"""
        self.jsx_code.append(f"try {{")
        
        # Position
        if 'p' in transform:
            position = transform['p']
            if isinstance(position, dict) and 'k' in position:
                if isinstance(position['k'], list) and len(position['k']) > 0:
                    if isinstance(position['k'][0], dict):  # Animated
                        self.set_property_keyframes(f"{layer_var}.transform.position", position)
                    else:  # Static
                        self.jsx_code.extend([
                            f"    try {{",
                            f"        {layer_var}.transform.position.setValue({position['k']});",
                            f"    }} catch(e) {{ alert('Failed to set position: ' + e.toString()); }}"
                        ])
        
        # Scale
        if 's' in transform:
            scale = transform['s']
            if isinstance(scale, dict) and 'k' in scale:
                if isinstance(scale['k'], list) and len(scale['k']) > 0:
                    if isinstance(scale['k'][0], dict):  # Animated
                        self.set_property_keyframes(f"{layer_var}.transform.scale", scale)
                    else:  # Static
                        self.jsx_code.extend([
                            f"    try {{",
                            f"        {layer_var}.transform.scale.setValue({scale['k']});",
                            f"    }} catch(e) {{ alert('Failed to set scale: ' + e.toString()); }}"
                        ])
        
        # Rotation
        if 'r' in transform:
            rotation = transform['r']
            if isinstance(rotation, dict) and 'k' in rotation:
                if isinstance(rotation['k'], list) and len(rotation['k']) > 0:
                    if isinstance(rotation['k'][0], dict):  # Animated
                        self.set_property_keyframes(f"{layer_var}.transform.rotation", rotation)
                    else:  # Static
                        self.jsx_code.extend([
                            f"    try {{",
                            f"        {layer_var}.transform.rotation.setValue({rotation['k']});",
                            f"    }} catch(e) {{ alert('Failed to set rotation: ' + e.toString()); }}"
                        ])
        
        # Opacity
        if 'o' in transform:
            opacity = transform['o']
            if isinstance(opacity, dict) and 'k' in opacity:
                if isinstance(opacity['k'], list) and len(opacity['k']) > 0:
                    if isinstance(opacity['k'][0], dict):  # Animated
                        self.set_property_keyframes(f"{layer_var}.transform.opacity", opacity)
                    else:  # Static
                        self.jsx_code.extend([
                            f"    try {{",
                            f"        {layer_var}.transform.opacity.setValue({opacity['k']});",
                            f"    }} catch(e) {{ alert('Failed to set opacity: ' + e.toString()); }}"
                        ])
        
        # Anchor Point
        if 'a' in transform:
            anchor = transform['a']
            if isinstance(anchor, dict) and 'k' in anchor:
                if isinstance(anchor['k'], list) and len(anchor['k']) > 0:
                    if isinstance(anchor['k'][0], dict):  # Animated
                        self.set_property_keyframes(f"{layer_var}.transform.anchorPoint", anchor)
                    else:  # Static
                        self.jsx_code.extend([
                            f"    try {{",
                            f"        {layer_var}.transform.anchorPoint.setValue({anchor['k']});",
                            f"    }} catch(e) {{ alert('Failed to set anchor point: ' + e.toString()); }}"
                        ])
        
        # Auto-Orient
        if 'ao' in transform:
            auto_orient = transform['ao']
            if isinstance(auto_orient, (int, float)):
                # Map Lottie auto-orient values to After Effects enum values
                ae_orient_map = {
                    0: "AutoOrientType.NO_AUTO_ORIENT",  # No Auto-Orient
                    1: "AutoOrientType.ALONG_PATH",      # Along Path
                    2: "AutoOrientType.CAMERA_TOWARDS"    # Towards Camera
                }
                orient_value = ae_orient_map.get(int(auto_orient), "AutoOrientType.NO_AUTO_ORIENT")
                self.jsx_code.extend([
                    f"    try {{",
                    f"        {layer_var}.autoOrient = {orient_value};",
                    f"    }} catch(e) {{ alert('Failed to set auto-orient: ' + e.toString()); }}"
                ])
        
        self.jsx_code.extend([
            "}} catch(e) {{",
            "    alert('Failed to access transform properties: ' + e.toString());",
            "}}",
            ""
        ])

    def set_property_with_error_handling(self, prop_path: str, value: Any):
        """Set property value with proper error handling"""
        # Handle arrays
        if isinstance(value, list):
            value_str = f"[{', '.join(str(x) for x in value)}]"
        else:
            value_str = str(value)
            
        self.jsx_code.extend([
            f"try {{",
            f"    {prop_path}.setValue({value_str});",
            f"}} catch(e) {{ /* Property cannot be set */ }}"
        ])

    def get_blend_mode(self, bm_value: int) -> str:
        """Convert blend mode value to AE constant"""
        blend_modes = {
            0: "BlendingMode.NORMAL",
            1: "BlendingMode.MULTIPLY",
            2: "BlendingMode.SCREEN",
            3: "BlendingMode.OVERLAY",
            # Add more as needed
        }
        return blend_modes.get(bm_value, "BlendingMode.NORMAL")

    def get_track_matte_type(self, tt_value: int) -> str:
        """Convert track matte type to AE constant"""
        matte_types = {
            1: "TrackMatteType.ALPHA",
            2: "TrackMatteType.ALPHA_INVERTED",
            3: "TrackMatteType.LUMA",
            4: "TrackMatteType.LUMA_INVERTED",
        }
        return matte_types.get(tt_value, "TrackMatteType.NO_TRACK_MATTE")

    def setup_track_mattes(self, layers: List[Dict]):
        """Setup track mattes after all layers are created"""
        self.jsx_code.append("\n// Setup track mattes")
        
        for i, layer in enumerate(layers):
            if 'tt' in layer and layer['tt'] > 0:  # If layer has track matte
                layer_index = layer.get('ind', 0)
                matte_type = self.get_track_matte_type(layer['tt'])
                
                # The matte layer is always the layer above
                if i > 0:
                    matte_layer_index = layers[i-1].get('ind', 0)
                    
                    self.jsx_code.extend([
                        f"try {{",
                        f"    var layer_{layer_index} = comp.layer('{layer.get('nm', f'Layer {layer_index}')}');",
                        f"    var matte_layer_{matte_layer_index} = comp.layer('{layers[i-1].get('nm', f'Layer {matte_layer_index}')}');",
                        f"    if (layer_{layer_index} && matte_layer_{matte_layer_index}) {{",
                        f"        layer_{layer_index}.setTrackMatte(matte_layer_{matte_layer_index}, {matte_type});",
                        f"        matte_layer_{matte_layer_index}.enabled = true;  // Ensure matte layer is enabled",
                        f"    }}",
                        f"}} catch(e) {{",
                        f"    alert('Track matte setup failed for layer_{layer_index}: ' + e.toString());",
                        f"}}",
                    ])

    def get_track_matte_type(self, tt_value: int) -> str:
        """Get track matte type from Lottie value"""
        matte_types = {
            1: "TrackMatteType.ALPHA",
            2: "TrackMatteType.ALPHA_INVERTED",
            3: "TrackMatteType.LUMA",
            4: "TrackMatteType.LUMA_INVERTED"
        }
        return matte_types.get(tt_value, "TrackMatteType.NO_TRACK_MATTE")

    def setup_layer_parenting(self, layers: List[Dict], comp_var: str):
        """Set up parent-child relationships between layers after all layers are created"""
        self.jsx_code.append("// Set up layer parenting relationships")
        
        # Find all parent-child relationships
        parent_child_pairs = []
        for layer in layers:
            if 'parent' in layer:
                child_index = layer.get('ind', 0)
                parent_index = layer['parent']
                parent_child_pairs.append((parent_index, child_index))
        
        # Set up parent-child relationships
        for parent_index, child_index in parent_child_pairs:
            child_var = f"layer_{child_index}"
            parent_var = f"layer_{parent_index}"
            
            self.jsx_code.extend([
                f"// Set up parenting: {child_var} -> {parent_var}",
                f"try {{",
                f"    {child_var}.parent = {parent_var};",
                f"}} catch(e) {{",
                f"    // Parenting setup failed for {child_var}",
                f"    alert('Parenting setup failed for {child_var}: ' + e.toString());",
                f"}}",
            ])
        
        if parent_child_pairs:
            self.jsx_code.append("")

    @staticmethod
    def find_json_files() -> List[str]:
        """Find all JSON files in the current directory"""
        json_files = []
        for file in os.listdir('.'):
            if file.lower().endswith('.json'):
                json_files.append(file)
        return sorted(json_files)

    @staticmethod
    def interactive_file_selection() -> Optional[str]:
        """Interactive file selection with enhanced user experience"""
        print("🎬 INTELLIGENT LOTTIE TO JSX CONVERTER")
        print("=" * 50)
        print()

        # Find JSON files
        json_files = LottieToJSXConverter.find_json_files()

        if not json_files:
            print("❌ No JSON files found in the current directory.")
            print("💡 Please ensure your Lottie/Bodymovin JSON files are in this folder.")
            return None

        print(f"📁 Found {len(json_files)} JSON file(s):")
        print()

        # Display files with analysis
        for i, file in enumerate(json_files, 1):
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                name = data.get('nm', 'Unnamed')
                width = data.get('w', 0)
                height = data.get('h', 0)
                duration = data.get('op', 0) / data.get('fr', 24)
                layers = len(data.get('layers', []))
                assets = len(data.get('assets', []))

                print(f"  {i}. {file}")
                print(f"     📋 Name: {name}")
                print(f"     📐 Size: {width}x{height}")
                print(f"     ⏱️  Duration: {duration:.1f}s")
                print(f"     🎭 Layers: {layers}")
                print(f"     📦 Assets: {assets}")
                print()

            except Exception as e:
                print(f"  {i}. {file} (⚠️  Error reading: {str(e)})")
                print()

        # Get user selection
        while True:
            try:
                choice = input(f"🔍 Select a file to convert (1-{len(json_files)}) or 'q' to quit: ").strip()

                if choice.lower() == 'q':
                    print("👋 Goodbye!")
                    return None

                index = int(choice) - 1
                if 0 <= index < len(json_files):
                    selected_file = json_files[index]
                    print(f"✅ Selected: {selected_file}")
                    return selected_file
                else:
                    print(f"❌ Please enter a number between 1 and {len(json_files)}")

            except ValueError:
                print("❌ Please enter a valid number or 'q' to quit")
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                return None

    def analyze_and_convert_with_smart_prompts(self, json_file: str) -> bool:
        """Analyze JSON file and convert with intelligent user guidance"""
        print(f"\n🔍 ANALYZING: {json_file}")
        print("-" * 40)

        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except Exception as e:
            print(f"❌ Error reading JSON file: {e}")
            return False

        # Analyze assets
        assets = data.get('assets', [])
        external_assets = []

        for asset in assets:
            if 'u' in asset and 'p' in asset:
                asset_type = 'unknown'
                if 'e' in asset:
                    if asset['e'] == 0:
                        asset_type = 'image'
                    elif asset['e'] == 1:
                        asset_type = 'video'
                elif 't' in asset and asset['t'] == 2:
                    asset_type = 'audio'

                if asset_type != 'unknown':
                    external_assets.append({
                        'id': asset.get('id', ''),
                        'name': asset.get('nm', ''),
                        'type': asset_type,
                        'path': asset.get('u', '') + asset.get('p', ''),
                        'width': asset.get('w', 0),
                        'height': asset.get('h', 0)
                    })

        # Show analysis results
        print(f"📊 ANALYSIS RESULTS:")
        print(f"   • Animation: {data.get('nm', 'Unnamed')}")
        print(f"   • Dimensions: {data.get('w', 0)}x{data.get('h', 0)}")
        print(f"   • Duration: {data.get('op', 0) / data.get('fr', 24):.1f} seconds")
        print(f"   • Layers: {len(data.get('layers', []))}")
        print(f"   • External Assets: {len(external_assets)}")

        if external_assets:
            print(f"\n📦 EXTERNAL ASSETS DETECTED:")
            for asset in external_assets:
                print(f"   • {asset['name']} ({asset['type']}) - {asset['path']}")
                if asset['width'] and asset['height']:
                    print(f"     Size: {asset['width']}x{asset['height']}")

        print(f"\n💡 CONVERSION NOTES:")
        print(f"   • Missing assets will prompt file dialogs during JSX execution")
        print(f"   • You can browse and select replacement files")
        print(f"   • Placeholders will be created for skipped assets")

        # Generate output filename
        base_name = os.path.splitext(json_file)[0]
        output_file = f"{base_name}_converted.jsx"

        print(f"\n🚀 CONVERTING...")
        print(f"   Input:  {json_file}")
        print(f"   Output: {output_file}")

        # Convert
        try:
            jsx_script = self.convert_json_to_jsx(json_file, output_file)
            print(f"\n✅ CONVERSION SUCCESSFUL!")
            print(f"   📄 Generated: {output_file}")
            print(f"   📏 Lines: {jsx_script.count(chr(10)) + 1:,}")
            return True

        except Exception as e:
            print(f"\n❌ CONVERSION FAILED: {e}")
            return False

    def add_footer(self, data: Dict):
        """Add footer code"""
        # Footer is now handled in convert_json_to_jsx
        pass

    def process_layer_styles(self, styles: List[Dict], layer_var: str):
        """Process layer styles (drop shadow, glow, etc.)"""
        if not styles or not isinstance(styles, list):
            return

        self.jsx_code.append(f"// Process layer styles")
        
        for i, style in enumerate(styles):
            if not isinstance(style, dict):
                continue
                
            style_type = style.get('ty', 0)
            style_name = style.get('nm', f'Style {i+1}')
            
            # Inner Shadow
            if style_type == 2:
                self.jsx_code.extend([
                    f"try {{",
                    f"    var innerShadow = {layer_var}.property('Effects').addProperty('ADBE Internal Drop Shadow');",
                    f"    innerShadow.name = '{style_name}';"
                ])
                
                # Process inner shadow properties
                if 'c' in style:  # Color
                    self.set_property_with_error_handling("innerShadow.property('Shadow Color')", style['c'])
                if 'o' in style:  # Opacity
                    self.set_property_with_error_handling("innerShadow.property('Opacity')", style['o'])
                if 'd' in style:  # Distance
                    self.set_property_with_error_handling("innerShadow.property('Distance')", style['d'])
                if 's' in style:  # Softness
                    self.set_property_with_error_handling("innerShadow.property('Softness')", style['s'])
                if 'a' in style:  # Angle
                    self.set_property_with_error_handling("innerShadow.property('Angle')", style['a'])
                
                self.jsx_code.append(f"}} catch(e) {{ alert('Failed to add inner shadow: ' + e.toString()); }}")
            
            # Stroke
            elif style_type == 0:
                self.jsx_code.extend([
                    f"try {{",
                    f"    var stroke = {layer_var}.property('Effects').addProperty('ADBE Stroke');",
                    f"    stroke.name = '{style_name}';"
                ])
                
                # Process stroke properties
                if 'c' in style:  # Color
                    self.set_property_with_error_handling("stroke.property('Color')", style['c'])
                if 's' in style:  # Size/Width
                    self.set_property_with_error_handling("stroke.property('Brush Size')", style['s'])
                
                self.jsx_code.append(f"}} catch(e) {{ alert('Failed to add stroke: ' + e.toString()); }}")
            
            # Drop Shadow
            elif style_type == 1:
                self.jsx_code.extend([
                    f"try {{",
                    f"    var dropShadow = {layer_var}.property('Effects').addProperty('ADBE Drop Shadow');",
                    f"    dropShadow.name = '{style_name}';"
                ])
                
                # Process drop shadow properties
                if 'c' in style:  # Color
                    self.set_property_with_error_handling("dropShadow.property('Color')", style['c'])
                if 'o' in style:  # Opacity
                    self.set_property_with_error_handling("dropShadow.property('Opacity')", style['o'])
                if 'd' in style:  # Distance
                    self.set_property_with_error_handling("dropShadow.property('Distance')", style['d'])
                if 's' in style:  # Softness
                    self.set_property_with_error_handling("dropShadow.property('Softness')", style['s'])
                if 'a' in style:  # Angle
                    self.set_property_with_error_handling("dropShadow.property('Angle')", style['a'])
                
                self.jsx_code.append(f"}} catch(e) {{ alert('Failed to add drop shadow: ' + e.toString()); }}")

        self.jsx_code.append("")

def main():
    """Main function for interactive conversion"""
    try:
        converter = LottieToJSXConverter()

        # Interactive file selection
        selected_file = converter.interactive_file_selection()

        if selected_file:
            # Analyze and convert with smart prompts
            success = converter.analyze_and_convert_with_smart_prompts(selected_file)

            if success:
                print(f"\n🎉 READY TO USE!")
                print(f"   1. Open After Effects")
                print(f"   2. Go to File > Scripts > Run Script File...")
                print(f"   3. Select the generated .jsx file")
                print(f"   4. Follow the smart file dialogs for missing assets")
                print(f"\n🎬 Your animation will be created with intelligent asset handling!")
            else:
                print(f"\n� Conversion failed. Please check the JSON file and try again.")

    except KeyboardInterrupt:
        print(f"\n� Conversion cancelled by user.")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        print(f"Please check your JSON file and try again.")

if __name__ == "__main__":
    main()
