@echo off
echo Testing JSX file in After Effects...
echo.
echo Instructions:
echo 1. Open After Effects
echo 2. Go to File > Scripts > Run Script File...
echo 3. Select: 3D_BOUNCE_converted.jsx
echo 4. The animation should be created with proper precomps and effects
echo.
echo Key things to check:
echo - Text layers should show "3D TEXT" (not missing precomps)
echo - Multiple colored text layers with strokes and shadows
echo - Orange, blue, and other colored text effects
echo - No "Missing Precomp" errors
echo.
pause
