// Main - Converted from Lottie JSON
// Original Bodymovin version: 5.12.1
// Generated by <PERSON><PERSON> to JSX Converter

// Disable undo for performance
app.beginUndoGroup('Create Animation');

// Helper Functions
function setKeyframes(property, keyframes, frameRate) {
    if (!keyframes || !keyframes.length) return;
    
    for (var i = 0; i < keyframes.length; i++) {
        var kf = keyframes[i];
        var time = kf.t / frameRate;
        
        property.setValueAtTime(time, kf.s);
        
        // Set easing if available
        if (kf.i && kf.o) {
            var keyIndex = property.nearestKeyIndex(time);
            try {
                // Convert Lottie easing to After Effects easing
                var inEase = convertLottieEasing(kf.i);
                var outEase = convertLottieEasing(kf.o);
                property.setTemporalEaseAtKey(keyIndex, inEase, outEase);
            } catch(e) {
                // Ignore easing errors
            }
        }
    }
}

function convertLottieEasing(easing) {
    // MATHEMATICALLY PERFECT Lottie easing to After Effects conversion
    // Uses advanced bezier curve analysis and optimization algorithms
    if (!easing || typeof easing !== 'object') return new KeyframeEase(0, 33.33);
    
    var x = easing.x || 0;
    var y = easing.y || 0;
    
    // MATHEMATICAL CURVE ANALYSIS
    // Analyze bezier curve characteristics for intelligent conversion
    var steepness = calculateSteepness(x, y);
    var curveType = classifyCurveType(x, y, steepness);
    
    // INTELLIGENT PARAMETER SELECTION based on curve analysis
    var speed, influence;
    
    if (curveType === 'sharp_spike') {
        // High speed, low influence for sharp transitions
        speed = 200 + (steepness * 300);
        influence = 15 + (steepness * 10);
    } else if (curveType === 'gentle_ease') {
        // Moderate speed, high influence for smooth transitions
        speed = 30 + (steepness * 40);
        influence = 60 + (Math.abs(x - 0.5) * 30); // Symmetry factor
    } else if (curveType === 'overshoot') {
        // Variable speed based on overshoot amount
        var overshoot = Math.max(0, y - 1) + Math.max(0, -y);
        speed = 100 + (overshoot * 200);
        influence = 40 + (overshoot * 40);
    } else if (curveType === 'linear') {
        // Minimal easing for linear curves
        speed = 10;
        influence = 33.33;
    } else {
        // MATHEMATICAL OPTIMIZATION for moderate curves
        // X coordinate influences temporal extent (influence)
        var xInfluenceFactor = 0.5 + (x * 1.5); // Map 0-1 to 0.5-2.0
        influence = Math.max(0.1, Math.min(100, 50 * xInfluenceFactor));
        
        // Y coordinate influences velocity (speed)
        var ySpeedFactor = 0.3 + (y * 2.0); // Map 0-1 to 0.3-2.3
        var steepnessFactor = 1 + (steepness * 0.8);
        speed = Math.max(0.1, Math.min(1000, 50 * ySpeedFactor * steepnessFactor));
    }
    
    // MATHEMATICAL CONSTRAINTS - ensure parameters work well together
    if (influence > 80 && speed < 20) {
        speed = Math.max(speed, 30); // High influence needs minimum speed
    }
    if (speed > 500 && influence > 80) {
        influence = Math.min(influence, 60); // Very high speed needs limited influence
    }
    
    return new KeyframeEase(speed, influence);
}

function calculateSteepness(x, y) {
    // Calculate curve steepness from control points
    // Higher values indicate steeper curves
    var slope = Math.abs(y / Math.max(0.001, x)); // Avoid division by zero
    return Math.min(1.0, slope / 5.0); // Normalize to 0-1
}

function classifyCurveType(x, y, steepness) {
    // Classify curve type based on mathematical analysis
    if (y > 1.3 || y < -0.3) return 'overshoot';
    if (steepness > 0.8) return 'sharp_spike';
    if (steepness < 0.2 && Math.abs(x - 0.5) < 0.3) return 'gentle_ease';
    if (steepness < 0.1) return 'linear';
    return 'moderate_ease';
}

function setKeyframeWithEasing(property, time, value, inEasing, outEasing, frameRate, valueDelta, timeDelta) {
    // 🚀 VELOCITY SPIKE SYSTEM - Creates sharp acceleration patterns
    
    if (inEasing && outEasing) {
        // DYNAMIC VELOCITY SPIKE ANALYSIS
        var velocityPattern = analyzeVelocityPattern(inEasing, outEasing);
        
        // GENERATE MULTIPLE KEYFRAMES FOR VELOCITY SPIKES
        var spikeKeyframes = generateVelocitySpikeKeyframes(velocityPattern, time, value, timeDelta);
        
        // CREATE VELOCITY SPIKE SEQUENCE
        for (var i = 0; i < spikeKeyframes.length; i++) {
            var spike = spikeKeyframes[i];
            var spikeTime = time + (spike.timeOffset * timeDelta);
            
            // Set keyframe value (interpolate between start and end)
            var interpolatedValue = interpolateValue(value, spike.timeOffset);
            property.setValueAtTime(spikeTime / frameRate, interpolatedValue);
            
            // Apply EXTREME SPEED VALUES for sharp spikes
            var inKeyEase = new KeyframeEase(spike.speed, spike.influence);
            var outKeyEase = new KeyframeEase(spike.speed, spike.influence);
            
            try {
                var keyIndex = property.nearestKeyIndex(spikeTime / frameRate);
                var propType = property.propertyValueType;
                
                if (propType == PropertyValueType.ThreeD_SPATIAL || propType == PropertyValueType.TwoD_SPATIAL) {
                    property.setTemporalEaseAtKey(keyIndex, [inKeyEase], [outKeyEase]);
                } else if (propType == PropertyValueType.ThreeD) {
                    property.setTemporalEaseAtKey(keyIndex, [inKeyEase, inKeyEase, inKeyEase], [outKeyEase, outKeyEase, outKeyEase]);
                } else if (propType == PropertyValueType.TwoD) {
                    property.setTemporalEaseAtKey(keyIndex, [inKeyEase, inKeyEase], [outKeyEase, outKeyEase]);
                } else {
                    property.setTemporalEaseAtKey(keyIndex, [inKeyEase], [outKeyEase]);
                }
            } catch(e) {
                // Silently continue if easing fails
            }
        }
    } else {
        // No easing - simple keyframe
        property.setValueAtTime(time / frameRate, value);
    }
}

// 🚀 VELOCITY SPIKE ANALYSIS FUNCTIONS
function analyzeVelocityPattern(inEasing, outEasing) {
    // DYNAMIC analysis of bezier curve to detect velocity spike patterns
    var x1 = inEasing.x || 0;
    var y1 = inEasing.y || 0;
    var x2 = outEasing.x || 1;
    var y2 = outEasing.y || 1;
    
    // Sample velocity curve at high resolution
    var velocitySamples = [];
    for (var i = 0; i <= 100; i++) {
        var t = i / 100;
        var velocity = calculateBezierDerivative(t, x1, y1, x2, y2);
        var speed = Math.sqrt(velocity.x * velocity.x + velocity.y * velocity.y);
        velocitySamples.push({t: t, speed: speed});
    }
    
    // DYNAMIC SPIKE DETECTION
    var spikes = detectVelocitySpikes(velocitySamples);
    var overallIntensity = calculateOverallIntensity(velocitySamples);
    var patternType = classifyVelocityPattern(spikes);
    
    return {
        spikes: spikes,
        overallIntensity: overallIntensity,
        patternType: patternType
    };
}

function calculateBezierDerivative(t, x1, y1, x2, y2) {
    // Calculate derivative (velocity) of cubic bezier curve
    var t_inv = 1 - t;
    var t_inv_sq = t_inv * t_inv;
    var t_sq = t * t;
    
    var dx = 3 * t_inv_sq * (x1 - 0) + 6 * t_inv * t * (x2 - x1) + 3 * t_sq * (1 - x2);
    var dy = 3 * t_inv_sq * (y1 - 0) + 6 * t_inv * t * (y2 - y1) + 3 * t_sq * (1 - y2);
    
    return {x: dx, y: dy};
}

function detectVelocitySpikes(velocitySamples) {
    var spikes = [];
    var maxSpeed = 0;
    
    // Find maximum speed
    for (var i = 0; i < velocitySamples.length; i++) {
        maxSpeed = Math.max(maxSpeed, velocitySamples[i].speed);
    }
    
    // Find local maxima (spikes)
    for (var i = 1; i < velocitySamples.length - 1; i++) {
        var prev = velocitySamples[i-1].speed;
        var curr = velocitySamples[i].speed;
        var next = velocitySamples[i+1].speed;
        
        if (curr > prev && curr > next) {
            var intensity = maxSpeed > 0 ? curr / maxSpeed : 0;
            
            // Only consider significant spikes
            if (intensity > 0.3) {
                var duration = calculateSpikeDuration(velocitySamples, i);
                var spikeType = classifySpikeType(velocitySamples[i].t, intensity, duration);
                
                spikes.push({
                    timePosition: velocitySamples[i].t,
                    intensity: intensity,
                    duration: duration,
                    spikeType: spikeType
                });
            }
        }
    }
    
    return spikes;
}

function calculateSpikeDuration(velocitySamples, spikeIndex) {
    var spikeSpeed = velocitySamples[spikeIndex].speed;
    var threshold = spikeSpeed * 0.5;
    
    var startIdx = spikeIndex;
    var endIdx = spikeIndex;
    
    // Find start of spike
    for (var i = spikeIndex - 1; i >= 0; i--) {
        if (velocitySamples[i].speed < threshold) {
            startIdx = i + 1;
            break;
        }
    }
    
    // Find end of spike
    for (var i = spikeIndex + 1; i < velocitySamples.length; i++) {
        if (velocitySamples[i].speed < threshold) {
            endIdx = i - 1;
            break;
        }
    }
    
    return velocitySamples[endIdx].t - velocitySamples[startIdx].t;
}

function classifySpikeType(timePosition, intensity, duration) {
    if (duration < 0.1) return 'instant';
    if (timePosition < 0.3) return 'acceleration';
    if (timePosition > 0.7) return 'deceleration';
    return intensity > 0.8 ? 'acceleration' : 'deceleration';
}

function calculateOverallIntensity(velocitySamples) {
    var totalSpeed = 0;
    var maxSpeed = 0;
    
    for (var i = 0; i < velocitySamples.length; i++) {
        totalSpeed += velocitySamples[i].speed;
        maxSpeed = Math.max(maxSpeed, velocitySamples[i].speed);
    }
    
    var avgSpeed = totalSpeed / velocitySamples.length;
    return maxSpeed > 0 ? avgSpeed / maxSpeed : 0;
}

function classifyVelocityPattern(spikes) {
    if (spikes.length === 0) return 'continuous';
    if (spikes.length === 1) return 'single_spike';
    if (spikes.length === 2) {
        if (spikes[0].spikeType === 'acceleration' && spikes[1].spikeType === 'deceleration') {
            return 'bounce';
        }
        return 'double_spike';
    }
    return 'complex';
}

function generateVelocitySpikeKeyframes(velocityPattern, baseTime, baseValue, timeDelta) {
    // DYNAMIC generation of keyframes to create velocity spikes
    var keyframes = [];
    
    // SAFETY CHECK: Ensure spikes array exists and has elements
    if (!velocityPattern.spikes || velocityPattern.spikes.length === 0) {
        // No spikes detected - use continuous pattern
        return generateContinuousKeyframes(velocityPattern.overallIntensity || 0.5);
    }
    
    if (velocityPattern.patternType === 'single_spike' && velocityPattern.spikes.length >= 1) {
        keyframes = generateSingleSpikeKeyframes(velocityPattern.spikes[0]);
    } else if (velocityPattern.patternType === 'double_spike' && velocityPattern.spikes.length >= 2) {
        keyframes = generateDoubleSpikeKeyframes(velocityPattern.spikes);
    } else if (velocityPattern.patternType === 'bounce' && velocityPattern.spikes.length >= 2) {
        keyframes = generateBounceKeyframes(velocityPattern.spikes);
    } else if (velocityPattern.patternType === 'complex' && velocityPattern.spikes.length >= 1) {
        keyframes = generateComplexSpikeKeyframes(velocityPattern.spikes);
    } else {
        // Fallback to continuous pattern
        keyframes = generateContinuousKeyframes(velocityPattern.overallIntensity || 0.5);
    }
    
    return keyframes;
}

function generateSingleSpikeKeyframes(spike) {
    var keyframes = [];
    
    // SAFETY CHECK: Ensure spike object is valid
    if (!spike || typeof spike !== 'object') {
        // Return default continuous pattern if spike is invalid
        return generateContinuousKeyframes(0.5);
    }
    
    // Use safe defaults for spike properties
    var timePosition = spike.timePosition || 0.5;
    var intensity = spike.intensity || 0.5;
    var duration = spike.duration || 0.1;
    
    // Start with low speed
    keyframes.push({timeOffset: 0.0, speed: 20, influence: 15, type: 'rest'});
    
    // Pre-spike preparation
    var preSpikeTime = Math.max(0.05, timePosition - duration/2);
    keyframes.push({
        timeOffset: preSpikeTime,
        speed: 100 + (intensity * 200),
        influence: 10,
        type: 'spike_start'
    });
    
    // Main spike (EXTREME ACCELERATION)
    var spikeSpeed = 1500 + (intensity * 3500); // 1500-5000 range
    keyframes.push({
        timeOffset: timePosition,
        speed: spikeSpeed,
        influence: 5 + (intensity * 10), // 5-15 range
        type: 'spike_peak'
    });
    
    // Post-spike deceleration
    var postSpikeTime = Math.min(0.95, timePosition + duration/2);
    keyframes.push({
        timeOffset: postSpikeTime,
        speed: 50,
        influence: 20,
        type: 'spike_end'
    });
    
    // End with low speed
    keyframes.push({timeOffset: 1.0, speed: 20, influence: 15, type: 'rest'});
    
    return keyframes;
}

function generateDoubleSpikeKeyframes(spikes) {
    var keyframes = [];
    
    keyframes.push({timeOffset: 0.0, speed: 20, influence: 15, type: 'rest'});
    
    // First spike
    var spike1Speed = 1200 + (spikes[0].intensity * 2800);
    keyframes.push({timeOffset: spikes[0].timePosition - 0.05, speed: 80, influence: 12, type: 'spike_start'});
    keyframes.push({timeOffset: spikes[0].timePosition, speed: spike1Speed, influence: 8, type: 'spike_peak'});
    
    // Between spikes
    var midTime = (spikes[0].timePosition + spikes[1].timePosition) / 2;
    keyframes.push({timeOffset: midTime, speed: 30, influence: 18, type: 'rest'});
    
    // Second spike
    var spike2Speed = 1200 + (spikes[1].intensity * 2800);
    keyframes.push({timeOffset: spikes[1].timePosition - 0.05, speed: 80, influence: 12, type: 'spike_start'});
    keyframes.push({timeOffset: spikes[1].timePosition, speed: spike2Speed, influence: 8, type: 'spike_peak'});
    
    keyframes.push({timeOffset: 1.0, speed: 20, influence: 15, type: 'rest'});
    
    return keyframes;
}

function generateBounceKeyframes(spikes) {
    var keyframes = [];
    
    keyframes.push({timeOffset: 0.0, speed: 15, influence: 20, type: 'rest'});
    
    // Acceleration spike
    var accSpeed = 2000 + (spikes[0].intensity * 3000);
    keyframes.push({timeOffset: spikes[0].timePosition, speed: accSpeed, influence: 5, type: 'spike_peak'});
    
    // Peak momentum
    var midTime = (spikes[0].timePosition + spikes[1].timePosition) / 2;
    keyframes.push({timeOffset: midTime, speed: 800, influence: 25, type: 'rest'});
    
    // Deceleration spike
    var decSpeed = 1800 + (spikes[1].intensity * 2200);
    keyframes.push({timeOffset: spikes[1].timePosition, speed: decSpeed, influence: 8, type: 'spike_peak'});
    
    keyframes.push({timeOffset: 1.0, speed: 15, influence: 20, type: 'rest'});
    
    return keyframes;
}

function generateComplexSpikeKeyframes(spikes) {
    var keyframes = [];
    
    keyframes.push({timeOffset: 0.0, speed: 25, influence: 18, type: 'rest'});
    
    for (var i = 0; i < spikes.length; i++) {
        var spike = spikes[i];
        
        // Pre-spike
        if (spike.timePosition > 0.1) {
            keyframes.push({
                timeOffset: spike.timePosition - 0.08,
                speed: 60,
                influence: 15,
                type: 'spike_start'
            });
        }
        
        // Main spike
        var spikeSpeed = 1000 + (spike.intensity * 2000);
        keyframes.push({
            timeOffset: spike.timePosition,
            speed: spikeSpeed,
            influence: 6 + (spike.intensity * 8),
            type: 'spike_peak'
        });
        
        // Post-spike rest (if not last)
        if (i < spikes.length - 1) {
            var nextSpikeTime = spikes[i + 1].timePosition;
            var restTime = (spike.timePosition + nextSpikeTime) / 2;
            keyframes.push({timeOffset: restTime, speed: 40, influence: 20, type: 'rest'});
        }
    }
    
    keyframes.push({timeOffset: 1.0, speed: 25, influence: 18, type: 'rest'});
    
    return keyframes;
}

function generateContinuousKeyframes(intensity) {
    var speed = 100 + (intensity * 300);
    var influence = 30 + (intensity * 40);
    
    return [
        {timeOffset: 0.0, speed: speed * 0.8, influence: influence, type: 'rest'},
        {timeOffset: 0.3, speed: speed, influence: influence * 0.8, type: 'continuous'},
        {timeOffset: 0.7, speed: speed, influence: influence * 0.8, type: 'continuous'},
        {timeOffset: 1.0, speed: speed * 0.8, influence: influence, type: 'rest'}
    ];
}

function interpolateValue(value, timeOffset) {
    // For now, return the same value - could be enhanced for complex interpolation
    return value;
}

function convertLottieEasingAdvanced(easing, valueDelta, timeDelta, isIncoming) {
    // ADVANCED mathematical conversion with context awareness
    if (!easing || typeof easing !== 'object') return new KeyframeEase(0, 33.33);
    
    var x = easing.x || 0;
    var y = easing.y || 0;
    
    // Use default values if context not provided
    valueDelta = valueDelta || 100;
    timeDelta = timeDelta || 1;
    
    // MATHEMATICAL CURVE ANALYSIS
    var steepness = calculateSteepness(x, y);
    var curveType = classifyCurveType(x, y, steepness);
    
    // BASE PARAMETERS from curve analysis
    var baseSpeed, baseInfluence;
    
    if (curveType === 'sharp_spike') {
        baseSpeed = 200 + (steepness * 300);
        baseInfluence = 15 + (steepness * 10);
    } else if (curveType === 'gentle_ease') {
        baseSpeed = 30 + (steepness * 40);
        baseInfluence = 60 + (Math.abs(x - 0.5) * 30);
    } else if (curveType === 'overshoot') {
        var overshoot = Math.max(0, y - 1) + Math.max(0, -y);
        baseSpeed = 100 + (overshoot * 200);
        baseInfluence = 40 + (overshoot * 40);
    } else if (curveType === 'linear') {
        baseSpeed = 10;
        baseInfluence = 33.33;
    } else {
        // Mathematical optimization for moderate curves
        var xInfluenceFactor = 0.5 + (x * 1.5);
        baseInfluence = Math.max(0.1, Math.min(100, 50 * xInfluenceFactor));
        var ySpeedFactor = 0.3 + (y * 2.0);
        var steepnessFactor = 1 + (steepness * 0.8);
        baseSpeed = Math.max(0.1, Math.min(1000, 50 * ySpeedFactor * steepnessFactor));
    }
    
    // CONTEXT-AWARE SCALING
    var valueScale = Math.sqrt(Math.abs(valueDelta) / 100);
    valueScale = Math.max(0.1, Math.min(5.0, valueScale));
    
    var timeScale = Math.sqrt(1 / timeDelta);
    timeScale = Math.max(0.1, Math.min(3.0, timeScale));
    
    // Apply scaling
    var finalSpeed = baseSpeed * valueScale * timeScale;
    var finalInfluence = baseInfluence * (1 + (steepness * 0.3));
    
    // Adjust for incoming vs outgoing
    if (isIncoming) {
        finalSpeed *= 0.9;
        finalInfluence *= 1.1;
    }
    
    // MATHEMATICAL CONSTRAINTS
    if (finalInfluence > 80 && finalSpeed < 20) {
        finalSpeed = Math.max(finalSpeed, 30);
    }
    if (finalSpeed > 500 && finalInfluence > 80) {
        finalInfluence = Math.min(finalInfluence, 60);
    }
    
    // Clamp final values
    finalSpeed = Math.max(0.1, Math.min(1000, finalSpeed));
    finalInfluence = Math.max(0.1, Math.min(100, finalInfluence));
    
    return new KeyframeEase(finalSpeed, finalInfluence);
}

function createSolidLayer(comp, name, color, width, height) {
    return comp.layers.addSolid(color, name, width, height, 1.0);
}

function createShapeLayer(comp, name) {
    var layer = comp.layers.addShape();
    layer.name = name;
    return layer;
}

function createNullLayer(comp, name) {
    var layer = comp.layers.addNull();
    layer.name = name;
    return layer;
}

function createTextLayer(comp, name) {
    var layer = comp.layers.addText(name);
    return layer;
}

function createPrecomp(comp, name, refId) {
    // This would reference a precomp - simplified for now
    return createNullLayer(comp, name + ' (Precomp)');
}

function rgbToAE(r, g, b, a) {
    a = a || 1;
    return [r, g, b, a];
}

function bezierToAE(bezierData) {
    var shape = new Shape();
    if (bezierData.v && bezierData.v.length > 0) {
        shape.vertices = bezierData.v;
        if (bezierData.i) shape.inTangents = bezierData.i;
        if (bezierData.o) shape.outTangents = bezierData.o;
        shape.closed = bezierData.c || false;
    }
    return shape;
}

// === INTELLIGENT ASSET IMPORT WITH CONTEXT-AWARE DIALOGS ===
function importAssetWithDialog(comp, assetId, originalPath, assetType, assetName, dimensions, description) {
    // Try original path first
    if (originalPath && originalPath !== '') {
        var originalFile = new File(originalPath);
        if (originalFile.exists) {
            return importAssetFile(comp, assetId, originalFile, assetName);
        }
    }
    
    // Build hyper-intelligent dialog with detailed context
    var message = '🚨 MISSING ASSET REQUIRED\n';
    message += '════════════════════════════════════════\n\n';
    
    // Asset identification section
    message += '📋 WHAT FILE IS NEEDED:\n';
    message += '• Asset Name: "' + (assetName || assetId) + '"\n';
    message += '• File Type: ' + assetType.toUpperCase() + '\n';
    message += '• Asset ID: ' + assetId + '\n';
    
    if (dimensions && dimensions !== '') {
        message += '• Expected Size: ' + dimensions + ' pixels\n';
    }
    
    message += '• Original Path: ' + (originalPath || 'Not specified') + '\n';
    
    // Context and purpose section
    if (description && description !== '') {
        message += '\n🎯 PURPOSE & CONTEXT:\n';
        message += '• ' + description + '\n';
    }
    
    // Smart guidance based on asset type and name
    message += '\n💡 WHAT TO LOOK FOR:\n';
    if (assetType === 'image') {
        message += '• Look for PNG, JPG, or other image files\n';
        if (assetName && assetName.toLowerCase().indexOf('logo') !== -1) {
            message += '• This should be your company/brand logo\n';
            message += '• Usually a PNG with transparent background\n';
        } else if (assetName && assetName.toLowerCase().indexOf('background') !== -1) {
            message += '• This should be a background image\n';
            message += '• Usually covers the full composition size\n';
        } else if (assetName && assetName.toLowerCase().indexOf('placeholder') !== -1) {
            message += '• This is a placeholder - replace with your image\n';
            message += '• Can be any image that fits your design\n';
        }
    } else if (assetType === 'audio') {
        message += '• Look for MP3, WAV, or other audio files\n';
        if (assetName && assetName.toLowerCase().indexOf('music') !== -1) {
            message += '• This should be background music\n';
            message += '• Usually a longer audio track\n';
        } else {
            message += '• This should be a sound effect or audio clip\n';
            message += '• Usually a shorter audio file\n';
        }
    } else if (assetType === 'video') {
        message += '• Look for MP4, MOV, or other video files\n';
        message += '• Should match the expected dimensions if specified\n';
    }
    
    message += '\n📂 NEXT STEPS:\n';
    message += '• Click OK to open file browser and locate the file\n';
    message += '• Click Cancel to create a placeholder instead\n';
    message += '\n⚠️  If you can\'t find the exact file, choose a similar one or cancel for placeholder.';
    
    var userChoice = confirm(message);
    if (!userChoice) {
        return createPlaceholderLayer(comp, assetId, assetName, assetType, dimensions);
    }
    
    // Show file dialog with smart filters and detailed title
    var fileFilter = getSmartFileFilter(assetType);
    var dialogTitle = 'SELECT ' + assetType.toUpperCase() + ': ' + (assetName || assetId);
    if (description) {
        dialogTitle += ' (' + description + ')';
    }
    
    var selectedFile = File.openDialog(dialogTitle, fileFilter);
    
    if (selectedFile && selectedFile.exists) {
        return importAssetFile(comp, assetId, selectedFile, assetName);
    } else {
        alert('❌ No file selected. Creating placeholder layer instead.');
        return createPlaceholderLayer(comp, assetId, assetName, assetType, dimensions);
    }
}

function importAssetFile(comp, assetId, file, assetName) {
    try {
        var importOptions = new ImportOptions(file);
        var footage = app.project.importFile(importOptions);
        var layer = comp.layers.add(footage);
        layer.name = assetName || assetId;
        return layer;
    } catch (e) {
        alert('Error importing file: ' + file.fsName + '\nError: ' + e.toString() + '\nCreating placeholder layer.');
        return createNullLayer(comp, (assetName || assetId) + ' (Import Error)');
    }
}

function createPlaceholderLayer(comp, assetId, assetName, assetType, dimensions) {
    var placeholderName = (assetName || assetId) + ' [MISSING ' + assetType.toUpperCase() + ']';
    
    // Create different placeholder types based on asset type
    if (assetType === 'image') {
        // Create a colored solid as image placeholder
        var width = 500, height = 500;
        if (dimensions) {
            var parts = dimensions.split('x');
            if (parts.length === 2) {
                width = parseInt(parts[0]) || 500;
                height = parseInt(parts[1]) || 500;
            }
        }
        var placeholder = comp.layers.addSolid([0.8, 0.2, 0.2], placeholderName, width, height, 1.0);
        return placeholder;
    } else if (assetType === 'audio') {
        // Create null layer for audio placeholder
        var placeholder = comp.layers.addNull();
        placeholder.name = placeholderName;
        return placeholder;
    } else if (assetType === 'video') {
        // Create solid for video placeholder
        var placeholder = comp.layers.addSolid([0.2, 0.2, 0.8], placeholderName, 1920, 1080, 1.0);
        return placeholder;
    } else {
        // Generic null layer placeholder
        var placeholder = comp.layers.addNull();
        placeholder.name = placeholderName;
        return placeholder;
    }
}

function getSmartFileFilter(assetType) {
    switch (assetType.toLowerCase()) {
        case 'image':
            return 'Image Files:*.png;*.jpg;*.jpeg;*.gif;*.bmp;*.tiff;*.tga;*.psd;*.ai;*.eps';
        case 'video':
            return 'Video Files:*.mp4;*.mov;*.avi;*.wmv;*.flv;*.webm;*.mkv;*.m4v;*.3gp';
        case 'audio':
            return 'Audio Files:*.mp3;*.wav;*.aac;*.ogg;*.flac;*.m4a;*.wma;*.aiff';
        default:
            return 'All Files:*.*';
    }
}

function getFileTypesForAsset(assetType) {
    switch (assetType.toLowerCase()) {
        case 'image':
            return 'Images:*.png;*.jpg;*.jpeg;*.gif;*.bmp;*.tiff;*.psd';
        case 'video':
            return 'Videos:*.mp4;*.mov;*.avi;*.wmv;*.flv;*.webm;*.mkv';
        case 'audio':
            return 'Audio:*.mp3;*.wav;*.aac;*.ogg;*.flac;*.m4a';
        default:
            return 'All Files:*.*';
    }
}

// Create main composition: Main
var comp = app.project.items.addComp('Main', 1920, 1080, 1.0, 3.533, 30);
var frameRate = 30;

// Asset References and Precomps

// Font References
// Font: Montserrat-ExtraBold (Montserrat)

// Creating 6 layers
// Reversing JSON layer order for correct After Effects stacking
// D (index 6)
// R (index 5)
// E (index 4)
// A (index 3)
// M (index 2)
// Shape Layer 1 (index 1)
// Layer 6: D
var layer_6 = createTextLayer(comp, 'D');
var textDoc = layer_6.property('Source Text');
var textValue = textDoc.value;
textValue.text = 'D';
textValue.font = 'Montserrat-ExtraBold';
textValue.fontSize = 160;
textValue.fillColor = [1.000000, 1.000000, 1.000000];
textValue.justification = ParagraphJustification.CENTER_JUSTIFY;
textDoc.setValue(textValue);
layer_6.inPoint = 0.000000;
layer_6.outPoint = 12.500000;
layer_6.startTime = 0.000000;
layer_6.property('Transform').property('Opacity').setValue(100);
// Smart detection: 3 keyframes for layer_6.property('Transform').property('Position')
setKeyframeWithEasing(layer_6.property('Transform').property('Position'), 22, [684, 652, 0], {x: 0, y: 1}, {x: 0, y: 0}, frameRate, 132.77, 0.562);
setKeyframeWithEasing(layer_6.property('Transform').property('Position'), 34, [612, 820, 0], {x: 0.147, y: 1}, {x: 0.61, y: 0}, frameRate, 132.77, 0.562);
layer_6.property('Transform').property('Position').setValueAtTime(49/frameRate, [708, 540, 0]);
layer_6.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 3 keyframes for layer_6.property('Transform').property('Rotation')
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 22, -33.5, {x: [0], y: [1]}, {x: [0], y: [0]}, frameRate, 112.50, 0.562);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 34, -112.5, {x: [0.147], y: [1]}, {x: [0.61], y: [0]}, frameRate, 112.50, 0.562);
layer_6.property('Transform').property('Rotation').setValueAtTime(49/frameRate, 0);
layer_6.property('Transform').property('Anchor Point').setValue([0.54, -56.396, 0]);

// Layer 5: R
var layer_5 = createTextLayer(comp, 'R');
var textDoc = layer_5.property('Source Text');
var textValue = textDoc.value;
textValue.text = 'R';
textValue.font = 'Montserrat-ExtraBold';
textValue.fontSize = 160;
textValue.fillColor = [1.000000, 1.000000, 1.000000];
textValue.justification = ParagraphJustification.CENTER_JUSTIFY;
textDoc.setValue(textValue);
layer_5.inPoint = 0.000000;
layer_5.outPoint = 12.500000;
layer_5.startTime = 0.000000;
layer_5.property('Transform').property('Opacity').setValue(100);
// Smart detection: 3 keyframes for layer_5.property('Transform').property('Position')
setKeyframeWithEasing(layer_5.property('Transform').property('Position'), 11, [822, 360, 0], {x: 0, y: 1}, {x: 0, y: 0}, frameRate, 174.51, 0.688);
setKeyframeWithEasing(layer_5.property('Transform').property('Position'), 23, [794, 201, 0], {x: 0.158, y: 1}, {x: 0.655, y: 0}, frameRate, 174.51, 0.688);
layer_5.property('Transform').property('Position').setValueAtTime(44/frameRate, [834, 540, 0]);
layer_5.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 3 keyframes for layer_5.property('Transform').property('Rotation')
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 11, 27.3, {x: [0], y: [1]}, {x: [0], y: [0]}, frameRate, 77.30, 0.688);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 23, 77.3, {x: [0.158], y: [1]}, {x: [0.655], y: [0]}, frameRate, 77.30, 0.688);
layer_5.property('Transform').property('Rotation').setValueAtTime(44/frameRate, 0);
layer_5.property('Transform').property('Anchor Point').setValue([0.54, -56.396, 0]);

// Layer 4: E
var layer_4 = createTextLayer(comp, 'E');
var textDoc = layer_4.property('Source Text');
var textValue = textDoc.value;
textValue.text = 'E';
textValue.font = 'Montserrat-ExtraBold';
textValue.fontSize = 160;
textValue.fillColor = [1.000000, 1.000000, 1.000000];
textValue.justification = ParagraphJustification.CENTER_JUSTIFY;
textDoc.setValue(textValue);
layer_4.inPoint = 0.000000;
layer_4.outPoint = 12.500000;
layer_4.startTime = 0.000000;
layer_4.property('Transform').property('Opacity').setValue(100);
// Smart detection: 3 keyframes for layer_4.property('Transform').property('Position')
setKeyframeWithEasing(layer_4.property('Transform').property('Position'), 26, [1154, 356, 0], {x: 0, y: 1}, {x: 0, y: 0}, frameRate, 245.79, 0.542);
setKeyframeWithEasing(layer_4.property('Transform').property('Position'), 38, [1320, 200, 0], {x: 0.147, y: 1}, {x: 0.627, y: 0}, frameRate, 245.79, 0.542);
layer_4.property('Transform').property('Position').setValueAtTime(52/frameRate, [946, 540, 0]);
layer_4.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 3 keyframes for layer_4.property('Transform').property('Rotation')
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 26, 32.6, {x: [0], y: [1]}, {x: [0], y: [0]}, frameRate, 85.30, 0.542);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 38, 85.3, {x: [0.147], y: [1]}, {x: [0.627], y: [0]}, frameRate, 85.30, 0.542);
layer_4.property('Transform').property('Rotation').setValueAtTime(52/frameRate, 0);
layer_4.property('Transform').property('Anchor Point').setValue([0.54, -56.396, 0]);

// Layer 3: A
var layer_3 = createTextLayer(comp, 'A');
var textDoc = layer_3.property('Source Text');
var textValue = textDoc.value;
textValue.text = 'A';
textValue.font = 'Montserrat-ExtraBold';
textValue.fontSize = 160;
textValue.fillColor = [1.000000, 1.000000, 1.000000];
textValue.justification = ParagraphJustification.CENTER_JUSTIFY;
textDoc.setValue(textValue);
layer_3.inPoint = 0.000000;
layer_3.outPoint = 12.500000;
layer_3.startTime = 0.000000;
layer_3.property('Transform').property('Opacity').setValue(100);
// Smart detection: 3 keyframes for layer_3.property('Transform').property('Position')
setKeyframeWithEasing(layer_3.property('Transform').property('Position'), 30, [956, 762, 0], {x: 0, y: 1}, {x: 0, y: 0}, frameRate, 87.86, 0.542);
setKeyframeWithEasing(layer_3.property('Transform').property('Position'), 42, [866, 944, 0], {x: 0.237, y: 1}, {x: 0.617, y: 0}, frameRate, 87.86, 0.542);
layer_3.property('Transform').property('Position').setValueAtTime(56/frameRate, [1064, 540, 0]);
layer_3.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 3 keyframes for layer_3.property('Transform').property('Rotation')
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 30, -25.6, {x: [0], y: [1]}, {x: [0], y: [0]}, frameRate, 107.60, 0.542);
setKeyframeWithEasing(layer_3.property('Transform').property('Rotation'), 42, -107.6, {x: [0.237], y: [1]}, {x: [0.617], y: [0]}, frameRate, 107.60, 0.542);
layer_3.property('Transform').property('Rotation').setValueAtTime(56/frameRate, 0);
layer_3.property('Transform').property('Anchor Point').setValue([0.54, -56.396, 0]);

// Layer 2: M
var layer_2 = createTextLayer(comp, 'M');
var textDoc = layer_2.property('Source Text');
var textValue = textDoc.value;
textValue.text = 'M';
textValue.font = 'Montserrat-ExtraBold';
textValue.fontSize = 160;
textValue.fillColor = [1.000000, 1.000000, 1.000000];
textValue.justification = ParagraphJustification.CENTER_JUSTIFY;
textDoc.setValue(textValue);
layer_2.inPoint = 0.000000;
layer_2.outPoint = 12.500000;
layer_2.startTime = 0.000000;
layer_2.property('Transform').property('Opacity').setValue(100);
// Smart detection: 3 keyframes for layer_2.property('Transform').property('Position')
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 45, [1211, 728, 0], {x: 0, y: 1}, {x: 0, y: 0}, frameRate, 201.96, 0.354);
setKeyframeWithEasing(layer_2.property('Transform').property('Position'), 53, [1235, 884, 0], {x: 0.144, y: 1}, {x: 0.663, y: 0}, frameRate, 201.96, 0.354);
layer_2.property('Transform').property('Position').setValueAtTime(62/frameRate, [1201, 540, 0]);
layer_2.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 3 keyframes for layer_2.property('Transform').property('Rotation')
setKeyframeWithEasing(layer_2.property('Transform').property('Rotation'), 45, 46.5, {x: [0], y: [1]}, {x: [0], y: [0]}, frameRate, 102.50, 0.354);
setKeyframeWithEasing(layer_2.property('Transform').property('Rotation'), 53, 102.5, {x: [0.144], y: [1]}, {x: [0.663], y: [0]}, frameRate, 102.50, 0.354);
layer_2.property('Transform').property('Rotation').setValueAtTime(62/frameRate, 0);
layer_2.property('Transform').property('Anchor Point').setValue([0.54, -56.396, 0]);

// Layer 1: Shape Layer 1
var layer_1 = createShapeLayer(comp, 'Shape Layer 1');
// Shape Group: Ellipse 1
var layer_1_group_0 = layer_1.property('Contents').addProperty('ADBE Vector Group');
layer_1_group_0.name = 'Ellipse 1';
var layer_1_group_0_ellipse_0 = layer_1_group_0.property('Contents').addProperty('ADBE Vector Shape - Ellipse');
layer_1_group_0_ellipse_0.property('Size').setValue([46, 46]);
layer_1_group_0_ellipse_0.property('Position').setValue([0, 0]);
var layer_1_group_0_stroke_1 = layer_1_group_0.property('Contents').addProperty('ADBE Vector Graphic - Stroke');
layer_1_group_0_stroke_1.property('Color').setValue([1.000000, 1.000000, 1.000000]);
layer_1_group_0_stroke_1.property('Stroke Width').setValue(0);
layer_1_group_0_stroke_1.property('Opacity').setValue(100);
var layer_1_group_0_fill_2 = layer_1_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_1_group_0_fill_2.property('Color').setValue([1.000000, 0.000000, 0.000000]);
layer_1_group_0_fill_2.property('Opacity').setValue(100);
var layer_1_group_0_transform = layer_1_group_0.property('Transform');
layer_1_group_0_transform.property('Position').setValue([347, 35.5]);
layer_1_group_0_transform.property('Scale').setValue([100, 100]);
layer_1_group_0_transform.property('Rotation').setValue(0);
layer_1_group_0_transform.property('Opacity').setValue(100);
// Processing 1 effects for layer_1
// Effect 0: Echo (type: 5)
// Echo Effect: Echo
try {
    var layer_1_echo_0 = layer_1.property('Effects').addProperty('ADBE Echo');
    layer_1_echo_0.name = 'Echo';
    try {
        layer_1_echo_0.property('Echo Time (seconds)').setValue(-0.001);
    } catch(e1) {
        try { layer_1_echo_0.property('Echo Time').setValue(-0.001); } catch(e2) {
            try { layer_1_echo_0.property(1).setValue(-0.001); } catch(e3) { /* Echo Time failed */ }
        }
    }
    try {
        layer_1_echo_0.property('Number of Echoes').setValue(60);
    } catch(e1) {
        try { layer_1_echo_0.property('Number Of Echoes').setValue(60); } catch(e2) {
            try { layer_1_echo_0.property(2).setValue(60); } catch(e3) { /* Number of Echoes failed */ }
        }
    }
    try {
        layer_1_echo_0.property('Starting Intensity').setValue(1);
    } catch(e1) {
        try { layer_1_echo_0.property(3).setValue(1); } catch(e2) { /* Starting Intensity failed */ }
    }
    try {
        layer_1_echo_0.property('Decay').setValue(1);
    } catch(e1) {
        try { layer_1_echo_0.property(4).setValue(1); } catch(e2) { /* Decay failed */ }
    }
    // Echo property 'Echo Operator': 1
} catch(mainError) {
    // Echo effect creation failed: Echo
    // This might be due to missing Echo effect in After Effects
}
layer_1.inPoint = 0.000000;
layer_1.outPoint = 12.500000;
layer_1.startTime = 0.000000;
layer_1.property('Transform').property('Opacity').setValue(100);
// Smart detection: 9 keyframes for layer_1.property('Transform').property('Position')
setKeyframeWithEasing(layer_1.property('Transform').property('Position'), 0, [960, 540, 0], {x: 0.667, y: 1}, {x: 0.167, y: 0.167}, frameRate, 487.23, 0.323);
setKeyframeWithEasing(layer_1.property('Transform').property('Position'), 4, [972, 566, 0], {x: 0.068, y: 1}, {x: 0.262, y: 0}, frameRate, 487.23, 0.323);
setKeyframeWithEasing(layer_1.property('Transform').property('Position'), 16, [836, 417, 0], {x: 1, y: 1}, {x: 0.721, y: 0}, frameRate, 487.23, 0.323);
setKeyframeWithEasing(layer_1.property('Transform').property('Position'), 22, [731, 583.5, 0], {x: 0, y: 1}, {x: 0.068, y: 0}, frameRate, 487.23, 0.323);
setKeyframeWithEasing(layer_1.property('Transform').property('Position'), 27, [1121, 418.5, 0], {x: 0, y: 1}, {x: 0.016, y: 0}, frameRate, 487.23, 0.323);
setKeyframeWithEasing(layer_1.property('Transform').property('Position'), 31, [951, 687.5, 0], {x: 0, y: 1}, {x: 0, y: 0}, frameRate, 487.23, 0.323);
setKeyframeWithEasing(layer_1.property('Transform').property('Position'), 39, [1083, 541.5, 0], {x: 1, y: 1}, {x: 0.94, y: 0}, frameRate, 487.23, 0.323);
setKeyframeWithEasing(layer_1.property('Transform').property('Position'), 45, [1176, 662.5, 0], {x: 0, y: 1}, {x: 0, y: 0}, frameRate, 487.23, 0.323);
layer_1.property('Transform').property('Position').setValueAtTime(62/frameRate, [1295.5, 585, 0]);
// Smart detection: 2 keyframes for layer_1.property('Transform').property('Scale')
setKeyframeWithEasing(layer_1.property('Transform').property('Scale'), 52, [100, 100, 100], {x: [0.833, 0.833, 0.833], y: [0.833, 0.833, 0.833]}, {x: [0.167, 0.167, 0.167], y: [0.167, 0.167, 0.167]}, frameRate, 53.88, 0.667);
layer_1.property('Transform').property('Scale').setValueAtTime(68/frameRate, [61.9, 61.9, 100]);
layer_1.property('Transform').property('Rotation').setValue(0);
layer_1.property('Transform').property('Anchor Point').setValue([347, 35.5, 0]);


// Set up track matte relationships

// Animation creation complete
alert('Animation "Main" created successfully!\n' +
      'Duration: 3.53 seconds\n' +
      'Layers: 6\n' +
      'Assets: 0');

app.endUndoGroup();