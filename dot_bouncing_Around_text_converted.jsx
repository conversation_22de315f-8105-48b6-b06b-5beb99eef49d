// Main - Converted from <PERSON><PERSON> JSON
// Original Bodymovin version: 5.12.1
// Generated by <PERSON><PERSON> to JSX Converter

// Disable undo for performance
app.beginUndoGroup('Create Animation');

// Helper Functions
function setKeyframes(property, keyframes, frameRate) {
    if (!keyframes || !keyframes.length) return;
    
    for (var i = 0; i < keyframes.length; i++) {
        var kf = keyframes[i];
        var time = kf.t / frameRate;
        
        if (i === 0) {
            property.setValueAtTime(time, kf.s);
        } else {
            property.setValueAtTime(time, kf.s);
            
            // Set easing if available
            if (kf.i && kf.o) {
                var keyIndex = property.nearestKeyIndex(time);
                try {
                    property.setTemporalEaseAtKey(keyIndex, kf.i, kf.o);
                } catch(e) {
                    // Ignore easing errors
                }
            }
        }
    }
}

function createSolidLayer(comp, name, color, width, height) {
    return comp.layers.addSolid(color, name, width, height, 1.0);
}

function createShapeLayer(comp, name) {
    var layer = comp.layers.addShape();
    layer.name = name;
    return layer;
}

function createNullLayer(comp, name) {
    var layer = comp.layers.addNull();
    layer.name = name;
    return layer;
}

function createTextLayer(comp, name) {
    var layer = comp.layers.addText(name);
    return layer;
}

function createPrecomp(comp, name, refId) {
    // This would reference a precomp - simplified for now
    return createNullLayer(comp, name + ' (Precomp)');
}

function rgbToAE(r, g, b, a) {
    a = a || 1;
    return [r, g, b, a];
}

function bezierToAE(bezierData) {
    var shape = new Shape();
    if (bezierData.v && bezierData.v.length > 0) {
        shape.vertices = bezierData.v;
        if (bezierData.i) shape.inTangents = bezierData.i;
        if (bezierData.o) shape.outTangents = bezierData.o;
        shape.closed = bezierData.c || false;
    }
    return shape;
}

// === INTELLIGENT ASSET IMPORT WITH CONTEXT-AWARE DIALOGS ===
function importAssetWithDialog(comp, assetId, originalPath, assetType, assetName, dimensions, description) {
    // Try original path first
    if (originalPath && originalPath !== '') {
        var originalFile = new File(originalPath);
        if (originalFile.exists) {
            return importAssetFile(comp, assetId, originalFile, assetName);
        }
    }
    
    // Build hyper-intelligent dialog with detailed context
    var message = '🚨 MISSING ASSET REQUIRED\n';
    message += '════════════════════════════════════════\n\n';
    
    // Asset identification section
    message += '📋 WHAT FILE IS NEEDED:\n';
    message += '• Asset Name: "' + (assetName || assetId) + '"\n';
    message += '• File Type: ' + assetType.toUpperCase() + '\n';
    message += '• Asset ID: ' + assetId + '\n';
    
    if (dimensions && dimensions !== '') {
        message += '• Expected Size: ' + dimensions + ' pixels\n';
    }
    
    message += '• Original Path: ' + (originalPath || 'Not specified') + '\n';
    
    // Context and purpose section
    if (description && description !== '') {
        message += '\n🎯 PURPOSE & CONTEXT:\n';
        message += '• ' + description + '\n';
    }
    
    // Smart guidance based on asset type and name
    message += '\n💡 WHAT TO LOOK FOR:\n';
    if (assetType === 'image') {
        message += '• Look for PNG, JPG, or other image files\n';
        if (assetName && assetName.toLowerCase().indexOf('logo') !== -1) {
            message += '• This should be your company/brand logo\n';
            message += '• Usually a PNG with transparent background\n';
        } else if (assetName && assetName.toLowerCase().indexOf('background') !== -1) {
            message += '• This should be a background image\n';
            message += '• Usually covers the full composition size\n';
        } else if (assetName && assetName.toLowerCase().indexOf('placeholder') !== -1) {
            message += '• This is a placeholder - replace with your image\n';
            message += '• Can be any image that fits your design\n';
        }
    } else if (assetType === 'audio') {
        message += '• Look for MP3, WAV, or other audio files\n';
        if (assetName && assetName.toLowerCase().indexOf('music') !== -1) {
            message += '• This should be background music\n';
            message += '• Usually a longer audio track\n';
        } else {
            message += '• This should be a sound effect or audio clip\n';
            message += '• Usually a shorter audio file\n';
        }
    } else if (assetType === 'video') {
        message += '• Look for MP4, MOV, or other video files\n';
        message += '• Should match the expected dimensions if specified\n';
    }
    
    message += '\n📂 NEXT STEPS:\n';
    message += '• Click OK to open file browser and locate the file\n';
    message += '• Click Cancel to create a placeholder instead\n';
    message += '\n⚠️  If you can\'t find the exact file, choose a similar one or cancel for placeholder.';
    
    var userChoice = confirm(message);
    if (!userChoice) {
        return createPlaceholderLayer(comp, assetId, assetName, assetType, dimensions);
    }
    
    // Show file dialog with smart filters and detailed title
    var fileFilter = getSmartFileFilter(assetType);
    var dialogTitle = 'SELECT ' + assetType.toUpperCase() + ': ' + (assetName || assetId);
    if (description) {
        dialogTitle += ' (' + description + ')';
    }
    
    var selectedFile = File.openDialog(dialogTitle, fileFilter);
    
    if (selectedFile && selectedFile.exists) {
        return importAssetFile(comp, assetId, selectedFile, assetName);
    } else {
        alert('❌ No file selected. Creating placeholder layer instead.');
        return createPlaceholderLayer(comp, assetId, assetName, assetType, dimensions);
    }
}

function importAssetFile(comp, assetId, file, assetName) {
    try {
        var importOptions = new ImportOptions(file);
        var footage = app.project.importFile(importOptions);
        var layer = comp.layers.add(footage);
        layer.name = assetName || assetId;
        return layer;
    } catch (e) {
        alert('Error importing file: ' + file.fsName + '\nError: ' + e.toString() + '\nCreating placeholder layer.');
        return createNullLayer(comp, (assetName || assetId) + ' (Import Error)');
    }
}

function createPlaceholderLayer(comp, assetId, assetName, assetType, dimensions) {
    var placeholderName = (assetName || assetId) + ' [MISSING ' + assetType.toUpperCase() + ']';
    
    // Create different placeholder types based on asset type
    if (assetType === 'image') {
        // Create a colored solid as image placeholder
        var width = 500, height = 500;
        if (dimensions) {
            var parts = dimensions.split('x');
            if (parts.length === 2) {
                width = parseInt(parts[0]) || 500;
                height = parseInt(parts[1]) || 500;
            }
        }
        var placeholder = comp.layers.addSolid([0.8, 0.2, 0.2], placeholderName, width, height, 1.0);
        return placeholder;
    } else if (assetType === 'audio') {
        // Create null layer for audio placeholder
        var placeholder = comp.layers.addNull();
        placeholder.name = placeholderName;
        return placeholder;
    } else if (assetType === 'video') {
        // Create solid for video placeholder
        var placeholder = comp.layers.addSolid([0.2, 0.2, 0.8], placeholderName, 1920, 1080, 1.0);
        return placeholder;
    } else {
        // Generic null layer placeholder
        var placeholder = comp.layers.addNull();
        placeholder.name = placeholderName;
        return placeholder;
    }
}

function getSmartFileFilter(assetType) {
    switch (assetType.toLowerCase()) {
        case 'image':
            return 'Image Files:*.png;*.jpg;*.jpeg;*.gif;*.bmp;*.tiff;*.tga;*.psd;*.ai;*.eps';
        case 'video':
            return 'Video Files:*.mp4;*.mov;*.avi;*.wmv;*.flv;*.webm;*.mkv;*.m4v;*.3gp';
        case 'audio':
            return 'Audio Files:*.mp3;*.wav;*.aac;*.ogg;*.flac;*.m4a;*.wma;*.aiff';
        default:
            return 'All Files:*.*';
    }
}

function getFileTypesForAsset(assetType) {
    switch (assetType.toLowerCase()) {
        case 'image':
            return 'Images:*.png;*.jpg;*.jpeg;*.gif;*.bmp;*.tiff;*.psd';
        case 'video':
            return 'Videos:*.mp4;*.mov;*.avi;*.wmv;*.flv;*.webm;*.mkv';
        case 'audio':
            return 'Audio:*.mp3;*.wav;*.aac;*.ogg;*.flac;*.m4a';
        default:
            return 'All Files:*.*';
    }
}

// Create main composition: Main
var comp = app.project.items.addComp('Main', 1920, 1080, 1.0, 3.533, 30);
var frameRate = 30;

// Asset References and Precomps

// Font References
// Font: Montserrat-ExtraBold (Montserrat)

// Creating 6 layers
// Preserving original JSON layer order for correct visual stacking
// Shape Layer 1 (index 1)
// M (index 2)
// A (index 3)
// E (index 4)
// R (index 5)
// D (index 6)
// Layer 1: Shape Layer 1
var layer_1 = createShapeLayer(comp, 'Shape Layer 1');
// Shape Group: Ellipse 1
var layer_1_group_0 = layer_1.property('Contents').addProperty('ADBE Vector Group');
if (layer_1_group_0) {
    layer_1_group_0.name = 'Ellipse 1';
    var layer_1_group_0_contents = layer_1_group_0.property('ADBE Vectors Group');
} else {
    alert('Error: Failed to create vector group Ellipse 1');
    var layer_1_group_0_contents = null;
}
if (layer_1_group_0_contents) {
    var layer_1_group_0_contents_ellipse_0 = layer_1_group_0_contents.addProperty('ADBE Vector Shape - Ellipse');
    if (layer_1_group_0_contents_ellipse_0) {
        layer_1_group_0_contents_ellipse_0.property('Size').setValue([46, 46]);
        layer_1_group_0_contents_ellipse_0.property('Position').setValue([0, 0]);
    } else {
        // Failed to create ellipse shape
    }
} else {
    // Skipping el item due to null parent
}
if (layer_1_group_0_contents) {
    var layer_1_group_0_contents_stroke_1 = layer_1_group_0_contents.addProperty('ADBE Vector Graphic - Stroke');
    if (layer_1_group_0_contents_stroke_1) {
        layer_1_group_0_contents_stroke_1.property('Color').setValue([1.000000, 1.000000, 1.000000]);
// WARNING: Stroke Width value 0 clamped to minimum 0.1
        layer_1_group_0_contents_stroke_1.property('Stroke Width').setValue(0.1);
        layer_1_group_0_contents_stroke_1.property('Opacity').setValue(100);
    } else {
        // Failed to create stroke
    }
} else {
    // Skipping st item due to null parent
}
if (layer_1_group_0_contents) {
    var layer_1_group_0_contents_fill_2 = layer_1_group_0_contents.addProperty('ADBE Vector Graphic - Fill');
    if (layer_1_group_0_contents_fill_2) {
        layer_1_group_0_contents_fill_2.property('Color').setValue([1.000000, 0.000000, 0.000000]);
        layer_1_group_0_contents_fill_2.property('Opacity').setValue(100);
    } else {
        // Failed to create fill
    }
} else {
    // Skipping fl item due to null parent
}
if (layer_1_group_0_contents) {
var layer_1_group_0_contents_transform = layer_1_group_0_contents.property('Transform');
layer_1_group_0_contents_transform.property('Position').setValue([347, 35.5]);
layer_1_group_0_contents_transform.property('Scale').setValue([100, 100]);
layer_1_group_0_contents_transform.property('Rotation').setValue(0);
layer_1_group_0_contents_transform.property('Opacity').setValue(100);
} else {
    // Skipping tr item due to null parent
}
// Effect: Echo (type 5, match: ADBE Echo)
var layer_1_grid_0 = layer_1.property('Effects').addProperty('ADBE Grid');
layer_1_grid_0.property('Echo Time (seconds)').setValue(-0.001);
layer_1_grid_0.property('Number Of Echoes').setValue(60);
layer_1_grid_0.property('Starting Intensity').setValue(1);
layer_1_grid_0.property('Decay').setValue(1);
layer_1_grid_0.property('Echo Operator').setValue(1);
layer_1.inPoint = 0.000000;
layer_1.outPoint = 12.500000;
layer_1.startTime = 0.000000;
layer_1.property('Transform').property('Opacity').setValue(100);
// Smart detection: 9 keyframes for layer_1.property('Transform').property('Position')
layer_1.property('Transform').property('Position').setValueAtTime(0/frameRate, [960, 540, 0]);
layer_1.property('Transform').property('Position').setValueAtTime(4/frameRate, [972, 566, 0]);
layer_1.property('Transform').property('Position').setValueAtTime(16/frameRate, [836, 417, 0]);
layer_1.property('Transform').property('Position').setValueAtTime(22/frameRate, [731, 583.5, 0]);
layer_1.property('Transform').property('Position').setValueAtTime(27/frameRate, [1121, 418.5, 0]);
layer_1.property('Transform').property('Position').setValueAtTime(31/frameRate, [951, 687.5, 0]);
layer_1.property('Transform').property('Position').setValueAtTime(39/frameRate, [1083, 541.5, 0]);
layer_1.property('Transform').property('Position').setValueAtTime(45/frameRate, [1176, 662.5, 0]);
layer_1.property('Transform').property('Position').setValueAtTime(62/frameRate, [1295.5, 585, 0]);
// Smart detection: 2 keyframes for layer_1.property('Transform').property('Scale')
layer_1.property('Transform').property('Scale').setValueAtTime(52/frameRate, [100, 100, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(68/frameRate, [61.9, 61.9, 100]);
layer_1.property('Transform').property('Rotation').setValue(0);
layer_1.property('Transform').property('Anchor Point').setValue([347, 35.5, 0]);
// Effect: Echo (type 5, match: ADBE Echo)
var layer_1_grid_0 = layer_1.property('Effects').addProperty('ADBE Grid');
layer_1_grid_0.property('Echo Time (seconds)').setValue(-0.001);
layer_1_grid_0.property('Number Of Echoes').setValue(60);
layer_1_grid_0.property('Starting Intensity').setValue(1);
layer_1_grid_0.property('Decay').setValue(1);
layer_1_grid_0.property('Echo Operator').setValue(1);
layer_1.blendingMode = BlendingMode.NORMAL;

// Layer 2: M
var layer_2 = createTextLayer(comp, 'M');
var textDoc = layer_2.property('Source Text');
var textValue = textDoc.value;
textValue.text = 'M';
textValue.font = 'Montserrat-ExtraBold';
textValue.fontSize = 160;
textValue.fillColor = [1.000000, 1.000000, 1.000000];
textValue.justification = ParagraphJustification.CENTER_JUSTIFY;
textDoc.setValue(textValue);
layer_2.inPoint = 0.000000;
layer_2.outPoint = 12.500000;
layer_2.startTime = 0.000000;
layer_2.property('Transform').property('Opacity').setValue(100);
// Smart detection: 3 keyframes for layer_2.property('Transform').property('Position')
layer_2.property('Transform').property('Position').setValueAtTime(45/frameRate, [1211, 728, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(53/frameRate, [1235, 884, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(62/frameRate, [1201, 540, 0]);
layer_2.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 3 keyframes for layer_2.property('Transform').property('Rotation')
layer_2.property('Transform').property('Rotation').setValueAtTime(45/frameRate, 46.5);
layer_2.property('Transform').property('Rotation').setValueAtTime(53/frameRate, 102.5);
layer_2.property('Transform').property('Rotation').setValueAtTime(62/frameRate, 0);
layer_2.property('Transform').property('Anchor Point').setValue([0.54, -56.396, 0]);
layer_2.blendingMode = BlendingMode.NORMAL;

// Layer 3: A
var layer_3 = createTextLayer(comp, 'A');
var textDoc = layer_3.property('Source Text');
var textValue = textDoc.value;
textValue.text = 'A';
textValue.font = 'Montserrat-ExtraBold';
textValue.fontSize = 160;
textValue.fillColor = [1.000000, 1.000000, 1.000000];
textValue.justification = ParagraphJustification.CENTER_JUSTIFY;
textDoc.setValue(textValue);
layer_3.inPoint = 0.000000;
layer_3.outPoint = 12.500000;
layer_3.startTime = 0.000000;
layer_3.property('Transform').property('Opacity').setValue(100);
// Smart detection: 3 keyframes for layer_3.property('Transform').property('Position')
layer_3.property('Transform').property('Position').setValueAtTime(30/frameRate, [956, 762, 0]);
layer_3.property('Transform').property('Position').setValueAtTime(42/frameRate, [866, 944, 0]);
layer_3.property('Transform').property('Position').setValueAtTime(56/frameRate, [1064, 540, 0]);
layer_3.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 3 keyframes for layer_3.property('Transform').property('Rotation')
layer_3.property('Transform').property('Rotation').setValueAtTime(30/frameRate, -25.6);
layer_3.property('Transform').property('Rotation').setValueAtTime(42/frameRate, -107.6);
layer_3.property('Transform').property('Rotation').setValueAtTime(56/frameRate, 0);
layer_3.property('Transform').property('Anchor Point').setValue([0.54, -56.396, 0]);
layer_3.blendingMode = BlendingMode.NORMAL;

// Layer 4: E
var layer_4 = createTextLayer(comp, 'E');
var textDoc = layer_4.property('Source Text');
var textValue = textDoc.value;
textValue.text = 'E';
textValue.font = 'Montserrat-ExtraBold';
textValue.fontSize = 160;
textValue.fillColor = [1.000000, 1.000000, 1.000000];
textValue.justification = ParagraphJustification.CENTER_JUSTIFY;
textDoc.setValue(textValue);
layer_4.inPoint = 0.000000;
layer_4.outPoint = 12.500000;
layer_4.startTime = 0.000000;
layer_4.property('Transform').property('Opacity').setValue(100);
// Smart detection: 3 keyframes for layer_4.property('Transform').property('Position')
layer_4.property('Transform').property('Position').setValueAtTime(26/frameRate, [1154, 356, 0]);
layer_4.property('Transform').property('Position').setValueAtTime(38/frameRate, [1320, 200, 0]);
layer_4.property('Transform').property('Position').setValueAtTime(52/frameRate, [946, 540, 0]);
layer_4.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 3 keyframes for layer_4.property('Transform').property('Rotation')
layer_4.property('Transform').property('Rotation').setValueAtTime(26/frameRate, 32.6);
layer_4.property('Transform').property('Rotation').setValueAtTime(38/frameRate, 85.3);
layer_4.property('Transform').property('Rotation').setValueAtTime(52/frameRate, 0);
layer_4.property('Transform').property('Anchor Point').setValue([0.54, -56.396, 0]);
layer_4.blendingMode = BlendingMode.NORMAL;

// Layer 5: R
var layer_5 = createTextLayer(comp, 'R');
var textDoc = layer_5.property('Source Text');
var textValue = textDoc.value;
textValue.text = 'R';
textValue.font = 'Montserrat-ExtraBold';
textValue.fontSize = 160;
textValue.fillColor = [1.000000, 1.000000, 1.000000];
textValue.justification = ParagraphJustification.CENTER_JUSTIFY;
textDoc.setValue(textValue);
layer_5.inPoint = 0.000000;
layer_5.outPoint = 12.500000;
layer_5.startTime = 0.000000;
layer_5.property('Transform').property('Opacity').setValue(100);
// Smart detection: 3 keyframes for layer_5.property('Transform').property('Position')
layer_5.property('Transform').property('Position').setValueAtTime(11/frameRate, [822, 360, 0]);
layer_5.property('Transform').property('Position').setValueAtTime(23/frameRate, [794, 201, 0]);
layer_5.property('Transform').property('Position').setValueAtTime(44/frameRate, [834, 540, 0]);
layer_5.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 3 keyframes for layer_5.property('Transform').property('Rotation')
layer_5.property('Transform').property('Rotation').setValueAtTime(11/frameRate, 27.3);
layer_5.property('Transform').property('Rotation').setValueAtTime(23/frameRate, 77.3);
layer_5.property('Transform').property('Rotation').setValueAtTime(44/frameRate, 0);
layer_5.property('Transform').property('Anchor Point').setValue([0.54, -56.396, 0]);
layer_5.blendingMode = BlendingMode.NORMAL;

// Layer 6: D
var layer_6 = createTextLayer(comp, 'D');
var textDoc = layer_6.property('Source Text');
var textValue = textDoc.value;
textValue.text = 'D';
textValue.font = 'Montserrat-ExtraBold';
textValue.fontSize = 160;
textValue.fillColor = [1.000000, 1.000000, 1.000000];
textValue.justification = ParagraphJustification.CENTER_JUSTIFY;
textDoc.setValue(textValue);
layer_6.inPoint = 0.000000;
layer_6.outPoint = 12.500000;
layer_6.startTime = 0.000000;
layer_6.property('Transform').property('Opacity').setValue(100);
// Smart detection: 3 keyframes for layer_6.property('Transform').property('Position')
layer_6.property('Transform').property('Position').setValueAtTime(22/frameRate, [684, 652, 0]);
layer_6.property('Transform').property('Position').setValueAtTime(34/frameRate, [612, 820, 0]);
layer_6.property('Transform').property('Position').setValueAtTime(49/frameRate, [708, 540, 0]);
layer_6.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 3 keyframes for layer_6.property('Transform').property('Rotation')
layer_6.property('Transform').property('Rotation').setValueAtTime(22/frameRate, -33.5);
layer_6.property('Transform').property('Rotation').setValueAtTime(34/frameRate, -112.5);
layer_6.property('Transform').property('Rotation').setValueAtTime(49/frameRate, 0);
layer_6.property('Transform').property('Anchor Point').setValue([0.54, -56.396, 0]);
layer_6.blendingMode = BlendingMode.NORMAL;


// Set up track matte relationships
// No track matte relationships found

// Animation creation complete
alert('Animation "Main" created successfully!\n' +
      'Duration: 3.53 seconds\n' +
      'Layers: 6\n' +
      'Assets: 0');

app.endUndoGroup();