// 07 - Converted from Lottie JSON
// Original Bodymovin version: 5.12.1
// Generated by <PERSON><PERSON> to JSX Converter

// Disable undo for performance
app.beginUndoGroup('Create Animation');

// Helper Functions
function setKeyframes(property, keyframes, frameRate) {
    if (!keyframes || !keyframes.length) return;
    
    for (var i = 0; i < keyframes.length; i++) {
        var kf = keyframes[i];
        var time = kf.t / frameRate;
        
        property.setValueAtTime(time, kf.s);
        
        // Set easing if available
        if (kf.i && kf.o) {
            var keyIndex = property.nearestKeyIndex(time);
            try {
                // Convert Lottie easing to After Effects easing
                var inEase = convertLottieEasing(kf.i);
                var outEase = convertLottieEasing(kf.o);
                property.setTemporalEaseAtKey(keyIndex, inEase, outEase);
            } catch(e) {
                // Ignore easing errors
            }
        }
    }
}

function convertLottieEasing(easing) {
    // MATHEMATICALLY PERFECT Lottie easing to After Effects conversion
    // Uses advanced bezier curve analysis and optimization algorithms
    if (!easing || typeof easing !== 'object') return new KeyframeEase(0, 33.33);
    
    var x = easing.x || 0;
    var y = easing.y || 0;
    
    // MATHEMATICAL CURVE ANALYSIS
    // Analyze bezier curve characteristics for intelligent conversion
    var steepness = calculateSteepness(x, y);
    var curveType = classifyCurveType(x, y, steepness);
    
    // INTELLIGENT PARAMETER SELECTION based on curve analysis
    var speed, influence;
    
    if (curveType === 'sharp_spike') {
        // High speed, low influence for sharp transitions
        speed = 200 + (steepness * 300);
        influence = 15 + (steepness * 10);
    } else if (curveType === 'gentle_ease') {
        // Moderate speed, high influence for smooth transitions
        speed = 30 + (steepness * 40);
        influence = 60 + (Math.abs(x - 0.5) * 30); // Symmetry factor
    } else if (curveType === 'overshoot') {
        // Variable speed based on overshoot amount
        var overshoot = Math.max(0, y - 1) + Math.max(0, -y);
        speed = 100 + (overshoot * 200);
        influence = 40 + (overshoot * 40);
    } else if (curveType === 'linear') {
        // Minimal easing for linear curves
        speed = 10;
        influence = 33.33;
    } else {
        // MATHEMATICAL OPTIMIZATION for moderate curves
        // X coordinate influences temporal extent (influence)
        var xInfluenceFactor = 0.5 + (x * 1.5); // Map 0-1 to 0.5-2.0
        influence = Math.max(0.1, Math.min(100, 50 * xInfluenceFactor));
        
        // Y coordinate influences velocity (speed)
        var ySpeedFactor = 0.3 + (y * 2.0); // Map 0-1 to 0.3-2.3
        var steepnessFactor = 1 + (steepness * 0.8);
        speed = Math.max(0.1, Math.min(1000, 50 * ySpeedFactor * steepnessFactor));
    }
    
    // MATHEMATICAL CONSTRAINTS - ensure parameters work well together
    if (influence > 80 && speed < 20) {
        speed = Math.max(speed, 30); // High influence needs minimum speed
    }
    if (speed > 500 && influence > 80) {
        influence = Math.min(influence, 60); // Very high speed needs limited influence
    }
    
    return new KeyframeEase(speed, influence);
}

function calculateSteepness(x, y) {
    // Calculate curve steepness from control points
    // Higher values indicate steeper curves
    var slope = Math.abs(y / Math.max(0.001, x)); // Avoid division by zero
    return Math.min(1.0, slope / 5.0); // Normalize to 0-1
}

function classifyCurveType(x, y, steepness) {
    // Classify curve type based on mathematical analysis
    if (y > 1.3 || y < -0.3) return 'overshoot';
    if (steepness > 0.8) return 'sharp_spike';
    if (steepness < 0.2 && Math.abs(x - 0.5) < 0.3) return 'gentle_ease';
    if (steepness < 0.1) return 'linear';
    return 'moderate_ease';
}

function setKeyframeWithEasing(property, time, value, inEasing, outEasing, frameRate, valueDelta, timeDelta) {
    // SIMPLE MATHEMATICAL CONVERSION - Direct bezier to speed/influence mapping
    property.setValueAtTime(time / frameRate, value);
    
    if (inEasing && outEasing) {
        try {
            var keyIndex = property.nearestKeyIndex(time / frameRate);
            
            if (keyIndex >= 1 && keyIndex <= property.numKeys) {
                // MATHEMATICAL EXTRACTION from JSON bezier values
                var inSpeed = calculateSpeedFromBezier(inEasing, valueDelta, timeDelta);
                var outSpeed = calculateSpeedFromBezier(outEasing, valueDelta, timeDelta);
                var inInfluence = calculateInfluenceFromBezier(inEasing);
                var outInfluence = calculateInfluenceFromBezier(outEasing);
                
                var inKeyEase = new KeyframeEase(inSpeed, inInfluence);
                var outKeyEase = new KeyframeEase(outSpeed, outInfluence);
                
                var propType = property.propertyValueType;
                
                if (propType == PropertyValueType.ThreeD_SPATIAL || propType == PropertyValueType.TwoD_SPATIAL) {
                    property.setTemporalEaseAtKey(keyIndex, [inKeyEase], [outKeyEase]);
                } else if (propType == PropertyValueType.ThreeD) {
                    property.setTemporalEaseAtKey(keyIndex, [inKeyEase, inKeyEase, inKeyEase], [outKeyEase, outKeyEase, outKeyEase]);
                } else if (propType == PropertyValueType.TwoD) {
                    property.setTemporalEaseAtKey(keyIndex, [inKeyEase, inKeyEase], [outKeyEase, outKeyEase]);
                } else {
                    property.setTemporalEaseAtKey(keyIndex, [inKeyEase], [outKeyEase]);
                }
            }
        } catch(e) {
            // Silently handle easing errors
        }
    }
}

// SIMPLE MATHEMATICAL CONVERSION FUNCTIONS
function calculateSpeedFromBezier(easing, valueDelta, timeDelta) {
    // Direct mathematical extraction from bezier coordinates
    if (!easing || typeof easing !== 'object') return 33.33;
    
    var x = easing.x || 0;
    var y = easing.y || 0;
    
    // Mathematical formula: speed based on Y coordinate and value change
    var baseSpeed = Math.abs(y) * 100; // Y coordinate determines speed intensity
    
    // Scale by value delta (larger changes need higher speeds)
    var valueScale = Math.sqrt(Math.abs(valueDelta) / 100);
    valueScale = Math.max(0.5, Math.min(3.0, valueScale));
    
    // Scale by time delta (shorter time needs higher speeds)
    var timeScale = Math.sqrt(1 / Math.max(0.1, timeDelta));
    timeScale = Math.max(0.5, Math.min(2.0, timeScale));
    
    var finalSpeed = baseSpeed * valueScale * timeScale;
    
    // Clamp to reasonable range
    return Math.max(1, Math.min(1000, finalSpeed));
}

function calculateInfluenceFromBezier(easing) {
    // Direct mathematical extraction from bezier coordinates
    if (!easing || typeof easing !== 'object') return 33.33;
    
    var x = easing.x || 0;
    var y = easing.y || 0;
    
    // Mathematical formula: influence based on X coordinate
    var baseInfluence = x * 100; // X coordinate determines temporal influence
    
    // Adjust based on Y coordinate for better curve matching
    var yAdjustment = Math.abs(y - 0.5) * 20;
    baseInfluence += yAdjustment;
    
    // Clamp to valid range
    return Math.max(0.1, Math.min(100, baseInfluence));
}


function convertLottieEasingAdvanced(easing, valueDelta, timeDelta, isIncoming) {
    // ADVANCED mathematical conversion with context awareness
    if (!easing || typeof easing !== 'object') return new KeyframeEase(0, 33.33);
    
    var x = easing.x || 0;
    var y = easing.y || 0;
    
    // Use default values if context not provided
    valueDelta = valueDelta || 100;
    timeDelta = timeDelta || 1;
    
    // MATHEMATICAL CURVE ANALYSIS
    var steepness = calculateSteepness(x, y);
    var curveType = classifyCurveType(x, y, steepness);
    
    // BASE PARAMETERS from curve analysis
    var baseSpeed, baseInfluence;
    
    if (curveType === 'sharp_spike') {
        baseSpeed = 200 + (steepness * 300);
        baseInfluence = 15 + (steepness * 10);
    } else if (curveType === 'gentle_ease') {
        baseSpeed = 30 + (steepness * 40);
        baseInfluence = 60 + (Math.abs(x - 0.5) * 30);
    } else if (curveType === 'overshoot') {
        var overshoot = Math.max(0, y - 1) + Math.max(0, -y);
        baseSpeed = 100 + (overshoot * 200);
        baseInfluence = 40 + (overshoot * 40);
    } else if (curveType === 'linear') {
        baseSpeed = 10;
        baseInfluence = 33.33;
    } else {
        // Mathematical optimization for moderate curves
        var xInfluenceFactor = 0.5 + (x * 1.5);
        baseInfluence = Math.max(0.1, Math.min(100, 50 * xInfluenceFactor));
        var ySpeedFactor = 0.3 + (y * 2.0);
        var steepnessFactor = 1 + (steepness * 0.8);
        baseSpeed = Math.max(0.1, Math.min(1000, 50 * ySpeedFactor * steepnessFactor));
    }
    
    // CONTEXT-AWARE SCALING
    var valueScale = Math.sqrt(Math.abs(valueDelta) / 100);
    valueScale = Math.max(0.1, Math.min(5.0, valueScale));
    
    var timeScale = Math.sqrt(1 / timeDelta);
    timeScale = Math.max(0.1, Math.min(3.0, timeScale));
    
    // Apply scaling
    var finalSpeed = baseSpeed * valueScale * timeScale;
    var finalInfluence = baseInfluence * (1 + (steepness * 0.3));
    
    // Adjust for incoming vs outgoing
    if (isIncoming) {
        finalSpeed *= 0.9;
        finalInfluence *= 1.1;
    }
    
    // MATHEMATICAL CONSTRAINTS
    if (finalInfluence > 80 && finalSpeed < 20) {
        finalSpeed = Math.max(finalSpeed, 30);
    }
    if (finalSpeed > 500 && finalInfluence > 80) {
        finalInfluence = Math.min(finalInfluence, 60);
    }
    
    // Clamp final values
    finalSpeed = Math.max(0.1, Math.min(1000, finalSpeed));
    finalInfluence = Math.max(0.1, Math.min(100, finalInfluence));
    
    return new KeyframeEase(finalSpeed, finalInfluence);
}

function createSolidLayer(comp, name, color, width, height) {
    return comp.layers.addSolid(color, name, width, height, 1.0);
}

function createShapeLayer(comp, name) {
    var layer = comp.layers.addShape();
    layer.name = name;
    return layer;
}

function createNullLayer(comp, name) {
    var layer = comp.layers.addNull();
    layer.name = name;
    return layer;
}

function createTextLayer(comp, name) {
    var layer = comp.layers.addText(name);
    return layer;
}

function createPrecomp(comp, name, refId) {
    // This would reference a precomp - simplified for now
    return createNullLayer(comp, name + ' (Precomp)');
}

function rgbToAE(r, g, b, a) {
    a = a || 1;
    return [r, g, b, a];
}

function bezierToAE(bezierData) {
    var shape = new Shape();
    if (bezierData.v && bezierData.v.length > 0) {
        shape.vertices = bezierData.v;
        if (bezierData.i) shape.inTangents = bezierData.i;
        if (bezierData.o) shape.outTangents = bezierData.o;
        shape.closed = bezierData.c || false;
    }
    return shape;
}

// === INTELLIGENT ASSET IMPORT WITH CONTEXT-AWARE DIALOGS ===
function importAssetWithDialog(comp, assetId, originalPath, assetType, assetName, dimensions, description) {
    // Try original path first
    if (originalPath && originalPath !== '') {
        var originalFile = new File(originalPath);
        if (originalFile.exists) {
            return importAssetFile(comp, assetId, originalFile, assetName);
        }
    }
    
    // Build hyper-intelligent dialog with detailed context
    var message = '🚨 MISSING ASSET REQUIRED\n';
    message += '════════════════════════════════════════\n\n';
    
    // Asset identification section
    message += '📋 WHAT FILE IS NEEDED:\n';
    message += '• Asset Name: "' + (assetName || assetId) + '"\n';
    message += '• File Type: ' + assetType.toUpperCase() + '\n';
    message += '• Asset ID: ' + assetId + '\n';
    
    if (dimensions && dimensions !== '') {
        message += '• Expected Size: ' + dimensions + ' pixels\n';
    }
    
    message += '• Original Path: ' + (originalPath || 'Not specified') + '\n';
    
    // Context and purpose section
    if (description && description !== '') {
        message += '\n🎯 PURPOSE & CONTEXT:\n';
        message += '• ' + description + '\n';
    }
    
    // Smart guidance based on asset type and name
    message += '\n💡 WHAT TO LOOK FOR:\n';
    if (assetType === 'image') {
        message += '• Look for PNG, JPG, or other image files\n';
        if (assetName && assetName.toLowerCase().indexOf('logo') !== -1) {
            message += '• This should be your company/brand logo\n';
            message += '• Usually a PNG with transparent background\n';
        } else if (assetName && assetName.toLowerCase().indexOf('background') !== -1) {
            message += '• This should be a background image\n';
            message += '• Usually covers the full composition size\n';
        } else if (assetName && assetName.toLowerCase().indexOf('placeholder') !== -1) {
            message += '• This is a placeholder - replace with your image\n';
            message += '• Can be any image that fits your design\n';
        }
    } else if (assetType === 'audio') {
        message += '• Look for MP3, WAV, or other audio files\n';
        if (assetName && assetName.toLowerCase().indexOf('music') !== -1) {
            message += '• This should be background music\n';
            message += '• Usually a longer audio track\n';
        } else {
            message += '• This should be a sound effect or audio clip\n';
            message += '• Usually a shorter audio file\n';
        }
    } else if (assetType === 'video') {
        message += '• Look for MP4, MOV, or other video files\n';
        message += '• Should match the expected dimensions if specified\n';
    }
    
    message += '\n📂 NEXT STEPS:\n';
    message += '• Click OK to open file browser and locate the file\n';
    message += '• Click Cancel to create a placeholder instead\n';
    message += '\n⚠️  If you can\'t find the exact file, choose a similar one or cancel for placeholder.';
    
    var userChoice = confirm(message);
    if (!userChoice) {
        return createPlaceholderLayer(comp, assetId, assetName, assetType, dimensions);
    }
    
    // Show file dialog with smart filters and detailed title
    var fileFilter = getSmartFileFilter(assetType);
    var dialogTitle = 'SELECT ' + assetType.toUpperCase() + ': ' + (assetName || assetId);
    if (description) {
        dialogTitle += ' (' + description + ')';
    }
    
    var selectedFile = File.openDialog(dialogTitle, fileFilter);
    
    if (selectedFile && selectedFile.exists) {
        return importAssetFile(comp, assetId, selectedFile, assetName);
    } else {
        alert('❌ No file selected. Creating placeholder layer instead.');
        return createPlaceholderLayer(comp, assetId, assetName, assetType, dimensions);
    }
}

function importAssetFile(comp, assetId, file, assetName) {
    try {
        var importOptions = new ImportOptions(file);
        var footage = app.project.importFile(importOptions);
        var layer = comp.layers.add(footage);
        layer.name = assetName || assetId;
        return layer;
    } catch (e) {
        alert('Error importing file: ' + file.fsName + '\nError: ' + e.toString() + '\nCreating placeholder layer.');
        return createNullLayer(comp, (assetName || assetId) + ' (Import Error)');
    }
}

function createPlaceholderLayer(comp, assetId, assetName, assetType, dimensions) {
    var placeholderName = (assetName || assetId) + ' [MISSING ' + assetType.toUpperCase() + ']';
    
    // Create different placeholder types based on asset type
    if (assetType === 'image') {
        // Create a colored solid as image placeholder
        var width = 500, height = 500;
        if (dimensions) {
            var parts = dimensions.split('x');
            if (parts.length === 2) {
                width = parseInt(parts[0]) || 500;
                height = parseInt(parts[1]) || 500;
            }
        }
        var placeholder = comp.layers.addSolid([0.8, 0.2, 0.2], placeholderName, width, height, 1.0);
        return placeholder;
    } else if (assetType === 'audio') {
        // Create null layer for audio placeholder
        var placeholder = comp.layers.addNull();
        placeholder.name = placeholderName;
        return placeholder;
    } else if (assetType === 'video') {
        // Create solid for video placeholder
        var placeholder = comp.layers.addSolid([0.2, 0.2, 0.8], placeholderName, 1920, 1080, 1.0);
        return placeholder;
    } else {
        // Generic null layer placeholder
        var placeholder = comp.layers.addNull();
        placeholder.name = placeholderName;
        return placeholder;
    }
}

function getSmartFileFilter(assetType) {
    switch (assetType.toLowerCase()) {
        case 'image':
            return 'Image Files:*.png;*.jpg;*.jpeg;*.gif;*.bmp;*.tiff;*.tga;*.psd;*.ai;*.eps';
        case 'video':
            return 'Video Files:*.mp4;*.mov;*.avi;*.wmv;*.flv;*.webm;*.mkv;*.m4v;*.3gp';
        case 'audio':
            return 'Audio Files:*.mp3;*.wav;*.aac;*.ogg;*.flac;*.m4a;*.wma;*.aiff';
        default:
            return 'All Files:*.*';
    }
}

function getFileTypesForAsset(assetType) {
    switch (assetType.toLowerCase()) {
        case 'image':
            return 'Images:*.png;*.jpg;*.jpeg;*.gif;*.bmp;*.tiff;*.psd';
        case 'video':
            return 'Videos:*.mp4;*.mov;*.avi;*.wmv;*.flv;*.webm;*.mkv';
        case 'audio':
            return 'Audio:*.mp3;*.wav;*.aac;*.ogg;*.flac;*.m4a';
        default:
            return 'All Files:*.*';
    }
}

// Create main composition: 07
var comp = app.project.items.addComp('07', 1080, 1080, 1.0, 6.000, 25);
var frameRate = 25;

// Asset References and Precomps

// Creating 10 layers
// Reversing JSON layer order for correct After Effects stacking
// Bg (index 10)
// Fether (index 9)
// fether (index 8)
// Fether (index 7)
// Fether (index 6)
// Fether (index 5)
// Body 2 (index 4)
// Mouth (index 3)
// Eyes (index 2)
// Body (index 1)
// Layer 10: Bg
var layer_10 = createShapeLayer(comp, 'Bg');
// Shape Group: Group 1
var layer_10_group_0 = layer_10.property('Contents').addProperty('ADBE Vector Group');
layer_10_group_0.name = 'Group 1';
var layer_10_group_0_path_0 = layer_10_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-539.819, 540], [539.819, 540], [539.819, -540], [-539.819, -540]];
pathShape.inTangents = [[0, 0], [0, 0], [0, 0], [0, 0]];
pathShape.outTangents = [[0, 0], [0, 0], [0, 0], [0, 0]];
pathShape.closed = true;
layer_10_group_0_path_0.property('Path').setValue(pathShape);
var layer_10_group_0_fill_1 = layer_10_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_10_group_0_fill_1.property('Color').setValue([0.156863, 0.431373, 0.552941]);
layer_10_group_0_fill_1.property('Opacity').setValue(100);
var layer_10_group_0_transform = layer_10_group_0.property('Transform');
layer_10_group_0_transform.property('Position').setValue([540.069, 540.25]);
layer_10_group_0_transform.property('Scale').setValue([100, 100]);
layer_10_group_0_transform.property('Rotation').setValue(0);
layer_10_group_0_transform.property('Opacity').setValue(100);
layer_10.inPoint = 0.000000;
layer_10.outPoint = 6.250000;
layer_10.startTime = 0.000000;
layer_10.property('Transform').property('Opacity').setValue(100);
layer_10.property('Transform').property('Position').setValue([540.181, 540, 0]);
layer_10.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_10.property('Transform').property('Rotation').setValue(0);
layer_10.property('Transform').property('Anchor Point').setValue([540.069, 540.25, 0]);

// Layer 4: Body 2
var layer_4 = createShapeLayer(comp, 'Body 2');
// Shape Group: Group 1
var layer_4_group_0 = layer_4.property('Contents').addProperty('ADBE Vector Group');
layer_4_group_0.name = 'Group 1';
var layer_4_group_0_path_0 = layer_4_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-99.754, -7.973], [-95.065, 23.889], [21.657, 24.332], [83.508, 28.647], [104.009, 18.973]];
pathShape.inTangents = [[104.418, -62.511], [-28.979, -10.511], [0, 0], [-11.507, -7.192], [16.187, 13.341]];
pathShape.outTangents = [[0, 0], [28.979, 10.512], [0, 0], [11.508, 7.192], [-16.185, -13.341]];
pathShape.closed = true;
layer_4_group_0_path_0.property('Path').setValue(pathShape);
var layer_4_group_0_fill_1 = layer_4_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_4_group_0_fill_1.property('Color').setValue([1.000000, 1.000000, 1.000000]);
layer_4_group_0_fill_1.property('Opacity').setValue(100);
var layer_4_group_0_transform = layer_4_group_0.property('Transform');
layer_4_group_0_transform.property('Position').setValue([197.95, 41.366]);
layer_4_group_0_transform.property('Scale').setValue([100, 100]);
layer_4_group_0_transform.property('Rotation').setValue(0);
layer_4_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 13
var layer_4_group_1 = layer_4.property('Contents').addProperty('ADBE Vector Group');
layer_4_group_1.name = 'Group 13';
var layer_4_group_1_path_0 = layer_4_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[78.57, 26.755], [9.983, -26.611], [-73.257, -4.781], [-55.056, 20.505], [-18.086, 33.689], [60.942, 31.324]];
pathShape.inTangents = [[-5.114, 1.612], [24.266, 65.914], [35.96, -34.522], [-23.514, -17.866], [-14.11, -2.44], [-25.962, 5.785]];
pathShape.outTangents = [[0, 0], [0, 0], [0, 0], [8.817, 6.699], [26.21, 4.53], [6.286, -1.4]];
pathShape.closed = true;
layer_4_group_1_path_0.property('Path').setValue(pathShape);
var layer_4_group_1_fill_1 = layer_4_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_4_group_1_fill_1.property('Color').setValue([1.000000, 1.000000, 1.000000]);
layer_4_group_1_fill_1.property('Opacity').setValue(100);
var layer_4_group_1_transform = layer_4_group_1.property('Transform');
layer_4_group_1_transform.property('Position').setValue([168.441, 190.586]);
layer_4_group_1_transform.property('Scale').setValue([100, 100]);
layer_4_group_1_transform.property('Rotation').setValue(0);
layer_4_group_1_transform.property('Opacity').setValue(100);
// Shape Group: Group 14
var layer_4_group_2 = layer_4.property('Contents').addProperty('ADBE Vector Group');
layer_4_group_2.name = 'Group 14';
var layer_4_group_2_path_0 = layer_4_group_2.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[32.203, 108.585], [-3.671, 3.095], [34.904, -102.889], [38.11, -108.585], [3.686, -102.889], [1.488, -98.784], [-36.612, -0.433], [-6.867, 97.884]];
pathShape.inTangents = [[0, 0], [-0.486, 38.31], [-24.253, 29.66], [0, 0], [0, 0], [0, 0], [1.498, -35.763], [-20.612, -29.265]];
pathShape.outTangents = [[-23.493, -30.266], [0.486, -38.311], [0, 0], [-10.296, 0.59], [0, 0], [-22.987, 27.439], [-1.498, 35.765], [0, 0]];
pathShape.closed = true;
layer_4_group_2_path_0.property('Path').setValue(pathShape);
var layer_4_group_2_fill_1 = layer_4_group_2.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_4_group_2_fill_1.property('Color').setValue([0.980392, 0.643137, 0.411765]);
layer_4_group_2_fill_1.property('Opacity').setValue(100);
var layer_4_group_2_transform = layer_4_group_2.property('Transform');
layer_4_group_2_transform.property('Position').setValue([127.329, 118.056]);
layer_4_group_2_transform.property('Scale').setValue([100, 100]);
layer_4_group_2_transform.property('Rotation').setValue(0);
layer_4_group_2_transform.property('Opacity').setValue(100);
// Shape Group: Group 15
var layer_4_group_3 = layer_4.property('Contents').addProperty('ADBE Vector Group');
layer_4_group_3.name = 'Group 15';
var layer_4_group_3_path_0 = layer_4_group_3.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[19.095, 104.911], [-13.324, -0.323], [33.841, -96.623], [39.454, -100.335], [7.643, -107.788], [4.098, -103.215], [-38.594, 12.392], [-2.481, 107.788]];
pathShape.inTangents = [[0, 0], [-4.505, 36.126], [-25.778, 25.707], [0, 0], [0, 0], [0, 0], [1.027, -42.213], [-25.984, -23.923]];
pathShape.outTangents = [[-18.674, -31.252], [4.506, -36.126], [0, 0], [0, 0], [0, 0], [-31.092, 28.572], [-0.86, 35.309], [0, 0]];
pathShape.closed = false;
layer_4_group_3_path_0.property('Path').setValue(pathShape);
var layer_4_group_3_fill_1 = layer_4_group_3.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_4_group_3_fill_1.property('Color').setValue([0.980392, 0.643137, 0.411765]);
layer_4_group_3_fill_1.property('Opacity').setValue(100);
var layer_4_group_3_transform = layer_4_group_3.property('Transform');
layer_4_group_3_transform.property('Position').setValue([196.916, 119.73]);
layer_4_group_3_transform.property('Scale').setValue([100, 100]);
layer_4_group_3_transform.property('Rotation').setValue(0);
layer_4_group_3_transform.property('Opacity').setValue(100);
// Shape Group: Group 16
var layer_4_group_4 = layer_4.property('Contents').addProperty('ADBE Vector Group');
layer_4_group_4.name = 'Group 16';
var layer_4_group_4_path_0 = layer_4_group_4.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[33.216, -68.829], [20.989, 68.829], [-11.867, 36.4], [-11.867, -23.924]];
pathShape.inTangents = [[0, 0], [-25.891, -83.428], [7.419, 3.531], [-21.349, 33.826]];
pathShape.outTangents = [[0, 0], [0, 0], [0, 0], [0, 0]];
pathShape.closed = true;
layer_4_group_4_path_0.property('Path').setValue(pathShape);
var layer_4_group_4_fill_1 = layer_4_group_4.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_4_group_4_fill_1.property('Color').setValue([1.000000, 1.000000, 1.000000]);
layer_4_group_4_fill_1.property('Opacity').setValue(100);
var layer_4_group_4_transform = layer_4_group_4.property('Transform');
layer_4_group_4_transform.property('Position').setValue([51.9, 106.187]);
layer_4_group_4_transform.property('Scale').setValue([100, 100]);
layer_4_group_4_transform.property('Rotation').setValue(0);
layer_4_group_4_transform.property('Opacity').setValue(100);
// Shape Group: Group 17
var layer_4_group_5 = layer_4.property('Contents').addProperty('ADBE Vector Group');
layer_4_group_5.name = 'Group 17';
var layer_4_group_5_path_0 = layer_4_group_5.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[146.201, -6.873], [143.842, -28.03], [135.942, -51.007], [38.319, -103.246], [-112.713, -55.778], [-145.797, -29.887], [-140.043, 23.333], [-101.206, 57.855], [-0.518, 111.076], [160.611, 54.984], [157.963, 37.848], [157.707, 37.717], [172.161, 23.538], [168.625, 17.425], [159.129, 9.964]];
pathShape.inTangents = [[2.831, 6.606], [-0.551, 14.37], [5.381, 6.407], [55.179, 10.567], [38.837, -40.275], [18.7, -1.438], [-33.083, -30.206], [-20.137, -30.207], [-74.797, -5.754], [-18.736, 18.873], [6.398, 3.318], [0.087, 0.044], [0.964, 9.775], [1.92, 1.508], [0, 0]];
pathShape.outTangents = [[0, 0], [0.32, -8.361], [-13.138, -15.642], [-67.604, -12.945], [0, 0], [0, 0], [0, 0], [0, 0], [66.498, 5.115], [5.078, -5.115], [-0.084, -0.044], [0, 0], [-0.239, -2.43], [0, 0], [-5.652, -4.441]];
pathShape.closed = true;
layer_4_group_5_path_0.property('Path').setValue(pathShape);
var layer_4_group_5_fill_1 = layer_4_group_5.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_4_group_5_fill_1.property('Color').setValue([0.976471, 0.552941, 0.262745]);
layer_4_group_5_fill_1.property('Opacity').setValue(100);
var layer_4_group_5_transform = layer_4_group_5.property('Transform');
layer_4_group_5_transform.property('Position').setValue([173.375, 116.442]);
layer_4_group_5_transform.property('Scale').setValue([100, 100]);
layer_4_group_5_transform.property('Rotation').setValue(0);
layer_4_group_5_transform.property('Opacity').setValue(100);
layer_4.inPoint = 0.000000;
layer_4.outPoint = 6.250000;
layer_4.startTime = 0.000000;
layer_4.property('Transform').property('Opacity').setValue(100);
// Smart detection: 3 keyframes for layer_4.property('Transform').property('Position')
setKeyframeWithEasing(layer_4.property('Transform').property('Position'), 0, [319.997, 593.221, 0], {x: 0.833, y: 0.833}, {x: 0.167, y: 0.167}, frameRate, 339.36, 3.104);
setKeyframeWithEasing(layer_4.property('Transform').property('Position'), 75, [572.482, 573.221, 0], {x: 0.667, y: 1}, {x: 0.167, y: 0.167}, frameRate, 339.36, 3.104);
layer_4.property('Transform').property('Position').setValueAtTime(149/frameRate, [821.601, 593.221, 0]);
layer_4.property('Transform').property('Scale').setValue([140, 140, 100]);
// Smart detection: 3 keyframes for layer_4.property('Transform').property('Rotation')
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 0, 1, {x: [0.833], y: [0.833]}, {x: [0.167], y: [0.167]}, frameRate, 10.00, 3.104);
setKeyframeWithEasing(layer_4.property('Transform').property('Rotation'), 75, -11, {x: [0.667], y: [1]}, {x: [0.167], y: [0.167]}, frameRate, 10.00, 3.104);
layer_4.property('Transform').property('Rotation').setValueAtTime(149/frameRate, 1);
layer_4.property('Transform').property('Anchor Point').setValue([173.375, 147.687, 0]);

// Layer 9: Fether
var layer_9 = createShapeLayer(comp, 'Fether');
// Shape Group: Group 1
var layer_9_group_0 = layer_9.property('Contents').addProperty('ADBE Vector Group');
layer_9_group_0.name = 'Group 1';
var layer_9_group_0_path_0 = layer_9_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-70.911, -20.198], [-33.514, -14.444], [70.911, 20.198]];
pathShape.inTangents = [[0, 0], [0, 0], [0, 0]];
pathShape.outTangents = [[0, 0], [0, 0], [0, 0]];
pathShape.closed = false;
layer_9_group_0_path_0.property('Path').setValue(pathShape);
var layer_9_group_0_stroke_1 = layer_9_group_0.property('Contents').addProperty('ADBE Vector Graphic - Stroke');
layer_9_group_0_stroke_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_9_group_0_stroke_1.property('Stroke Width').setValue(1);
layer_9_group_0_stroke_1.property('Opacity').setValue(100);
var layer_9_group_0_transform = layer_9_group_0.property('Transform');
layer_9_group_0_transform.property('Position').setValue([74.039, 136.959]);
layer_9_group_0_transform.property('Scale').setValue([100, 100]);
layer_9_group_0_transform.property('Rotation').setValue(0);
layer_9_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_9_group_1 = layer_9.property('Contents').addProperty('ADBE Vector Group');
layer_9_group_1.name = 'Group 2';
var layer_9_group_1_path_0 = layer_9_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-20.857, -14.384], [20.857, 14.384]];
pathShape.inTangents = [[0, 0], [-10.069, -10.069]];
pathShape.outTangents = [[0, 0], [0, 0]];
pathShape.closed = false;
layer_9_group_1_path_0.property('Path').setValue(pathShape);
var layer_9_group_1_stroke_1 = layer_9_group_1.property('Contents').addProperty('ADBE Vector Graphic - Stroke');
layer_9_group_1_stroke_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_9_group_1_stroke_1.property('Stroke Width').setValue(1);
layer_9_group_1_stroke_1.property('Opacity').setValue(100);
var layer_9_group_1_transform = layer_9_group_1.property('Transform');
layer_9_group_1_transform.property('Position').setValue([32.615, 100.939]);
layer_9_group_1_transform.property('Scale').setValue([100, 100]);
layer_9_group_1_transform.property('Rotation').setValue(0);
layer_9_group_1_transform.property('Opacity').setValue(100);
// Shape Group: Group 3
var layer_9_group_2 = layer_9.property('Contents').addProperty('ADBE Vector Group');
layer_9_group_2.name = 'Group 3';
var layer_9_group_2_path_0 = layer_9_group_2.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-64.728, -30.926], [-15.823, 10.789], [64.728, 51.064], [7.191, 10.789], [-53.221, -51.064]];
pathShape.inTangents = [[0, 0], [-14.384, -20.138], [0, 0], [0, 0], [0, 0]];
pathShape.outTangents = [[0, 0], [0, 0], [0, 0], [-8.63, -18.699], [0, 0]];
pathShape.closed = false;
layer_9_group_2_path_0.property('Path').setValue(pathShape);
var layer_9_group_2_stroke_1 = layer_9_group_2.property('Contents').addProperty('ADBE Vector Graphic - Stroke');
layer_9_group_2_stroke_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_9_group_2_stroke_1.property('Stroke Width').setValue(1);
layer_9_group_2_stroke_1.property('Opacity').setValue(100);
var layer_9_group_2_transform = layer_9_group_2.property('Transform');
layer_9_group_2_transform.property('Position').setValue([83.678, 100.219]);
layer_9_group_2_transform.property('Scale').setValue([100, 100]);
layer_9_group_2_transform.property('Rotation').setValue(0);
layer_9_group_2_transform.property('Opacity').setValue(100);
// Shape Group: Group 4
var layer_9_group_3 = layer_9.property('Contents').addProperty('ADBE Vector Group');
layer_9_group_3.name = 'Group 4';
var layer_9_group_3_path_0 = layer_9_group_3.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-53.729, -50.376], [13.876, 18.667], [53.729, 66.198], [32.575, 12.914], [-39.345, -66.198]];
pathShape.inTangents = [[0, 0], [-8.63, -25.891], [0, 0], [0, 0], [0, 0]];
pathShape.outTangents = [[0, 0], [0, 0], [0, 0], [-18.699, -44.591], [0, 0]];
pathShape.closed = false;
layer_9_group_3_path_0.property('Path').setValue(pathShape);
var layer_9_group_3_stroke_1 = layer_9_group_3.property('Contents').addProperty('ADBE Vector Graphic - Stroke');
layer_9_group_3_stroke_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_9_group_3_stroke_1.property('Stroke Width').setValue(1);
layer_9_group_3_stroke_1.property('Opacity').setValue(100);
var layer_9_group_3_transform = layer_9_group_3.property('Transform');
layer_9_group_3_transform.property('Position').setValue([94.255, 85.149]);
layer_9_group_3_transform.property('Scale').setValue([100, 100]);
layer_9_group_3_transform.property('Rotation').setValue(0);
layer_9_group_3_transform.property('Opacity').setValue(100);
// Shape Group: Group 5
var layer_9_group_4 = layer_9.property('Contents').addProperty('ADBE Vector Group');
layer_9_group_4.name = 'Group 5';
var layer_9_group_4_path_0 = layer_9_group_4.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-4.928, -73.536], [61.239, 12.768], [76.13, 73.536], [-35.855, 36.503], [-76.13, 22.119]];
pathShape.inTangents = [[0, 0], [-7.193, -54.659], [0, 0], [0, 0], [0, 0]];
pathShape.outTangents = [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]];
pathShape.closed = false;
layer_9_group_4_path_0.property('Path').setValue(pathShape);
var layer_9_group_4_stroke_1 = layer_9_group_4.property('Contents').addProperty('ADBE Vector Graphic - Stroke');
layer_9_group_4_stroke_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_9_group_4_stroke_1.property('Stroke Width').setValue(1);
layer_9_group_4_stroke_1.property('Opacity').setValue(100);
var layer_9_group_4_transform = layer_9_group_4.property('Transform');
layer_9_group_4_transform.property('Position').setValue([79.257, 80.259]);
layer_9_group_4_transform.property('Scale').setValue([100, 100]);
layer_9_group_4_transform.property('Rotation').setValue(0);
layer_9_group_4_transform.property('Opacity').setValue(100);
// Shape Group: Group 6
var layer_9_group_5 = layer_9.property('Contents').addProperty('ADBE Vector Group');
layer_9_group_5.name = 'Group 6';
var layer_9_group_5_path_0 = layer_9_group_5.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[78.393, 17.98], [9.35, -79.832], [-7.911, -72.639], [-23.733, -61.132], [-40.994, -45.31], [-52.502, -30.926], [-62.57, -12.226], [-68.324, 5.035], [-74.077, 20.857], [-75.516, 33.803], [-74.077, 46.749], [-42.433, 53.94], [29.488, 71.201], [55.379, 76.955], [78.393, 66.886]];
pathShape.inTangents = [[0, 0], [57.536, 11.508], [5.754, -7.193], [5.753, -11.507], [2.877, -14.384], [-1.438, -10.068], [-2.877, -11.507], [-5.753, -12.946], [-4.316, -14.384], [-4.315, -5.754], [-7.193, -4.316], [0, 0], [0, 0], [0, 0], [-2.876, 12.946]];
pathShape.outTangents = [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.877, -12.946]];
pathShape.closed = true;
layer_9_group_5_path_0.property('Path').setValue(pathShape);
var layer_9_group_5_fill_1 = layer_9_group_5.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_9_group_5_fill_1.property('Color').setValue([0.972549, 0.305882, 0.192157]);
layer_9_group_5_fill_1.property('Opacity').setValue(100);
var layer_9_group_5_transform = layer_9_group_5.property('Transform');
layer_9_group_5_transform.property('Position').setValue([81.52, 80.082]);
layer_9_group_5_transform.property('Scale').setValue([100, 100]);
layer_9_group_5_transform.property('Rotation').setValue(0);
layer_9_group_5_transform.property('Opacity').setValue(100);
layer_9.inPoint = 0.000000;
layer_9.outPoint = 6.250000;
layer_9.startTime = 0.000000;
layer_9.parent = layer_4;
layer_9.property('Transform').property('Opacity').setValue(100);
layer_9.property('Transform').property('Position').setValue([157.15, 44.639, 0]);
layer_9.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 150 keyframes for layer_9.property('Transform').property('Rotation')
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 0, 0, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 1, -0.087, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 2, -0.34, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 3, -0.743, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 4, -1.283, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 5, -1.945, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 6, -2.715, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 7, -3.578, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 8, -4.52, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 9, -5.527, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 10, -6.584, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 11, -7.677, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 12, -8.791, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 13, -9.913, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 14, -11.027, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 15, -12.12, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 16, -13.177, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 17, -14.184, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 18, -15.126, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 19, -15.989, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 20, -16.758, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 21, -17.42, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 22, -17.96, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 23, -18.364, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 24, -18.616, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 25, -18.704, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 26, -18.616, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 27, -18.364, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 28, -17.96, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 29, -17.42, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 30, -16.758, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 31, -15.989, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 32, -15.126, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 33, -14.184, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 34, -13.177, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 35, -12.12, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 36, -11.027, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 37, -9.913, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 38, -8.791, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 39, -7.677, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 40, -6.584, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 41, -5.527, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 42, -4.52, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 43, -3.578, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 44, -2.715, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 45, -1.945, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 46, -1.283, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 47, -0.743, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 48, -0.34, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 49, -0.087, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 50, 0, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 51, -0.087, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 52, -0.34, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 53, -0.743, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 54, -1.283, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 55, -1.945, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 56, -2.715, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 57, -3.578, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 58, -4.52, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 59, -5.527, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 60, -6.584, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 61, -7.677, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 62, -8.791, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 63, -9.913, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 64, -11.027, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 65, -12.12, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 66, -13.177, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 67, -14.184, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 68, -15.126, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 69, -15.989, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 70, -16.758, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 71, -17.42, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 72, -17.96, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 73, -18.364, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 74, -18.616, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 75, -18.704, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 76, -18.616, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 77, -18.364, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 78, -17.96, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 79, -17.42, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 80, -16.758, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 81, -15.989, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 82, -15.126, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 83, -14.184, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 84, -13.177, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 85, -12.12, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 86, -11.027, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 87, -9.913, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 88, -8.791, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 89, -7.677, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 90, -6.584, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 91, -5.527, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 92, -4.52, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 93, -3.578, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 94, -2.715, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 95, -1.945, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 96, -1.283, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 97, -0.743, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 98, -0.34, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 99, -0.087, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 100, 0, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 101, -0.087, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 102, -0.34, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 103, -0.743, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 104, -1.283, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 105, -1.945, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 106, -2.715, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 107, -3.578, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 108, -4.52, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 109, -5.527, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 110, -6.584, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 111, -7.677, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 112, -8.791, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 113, -9.913, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 114, -11.027, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 115, -12.12, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 116, -13.177, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 117, -14.184, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 118, -15.126, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 119, -15.989, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 120, -16.758, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 121, -17.42, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 122, -17.96, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 123, -18.364, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 124, -18.616, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 125, -18.704, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 126, -18.616, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 127, -18.364, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 128, -17.96, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 129, -17.42, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 130, -16.758, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 131, -15.989, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 132, -15.126, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 133, -14.184, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 134, -13.177, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 135, -12.12, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 136, -11.027, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 137, -9.913, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 138, -8.791, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 139, -7.677, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 140, -6.584, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 141, -5.527, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 142, -4.52, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 143, -3.578, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 144, -2.715, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 145, -1.945, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 146, -1.283, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 147, -0.743, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 148, -0.34, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
setKeyframeWithEasing(layer_9.property('Transform').property('Rotation'), 149, -0.087, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 18.70, 0.042);
layer_9.property('Transform').property('Anchor Point').setValue([148.52, 151.082, 0]);

// Layer 8: fether
var layer_8 = createShapeLayer(comp, 'fether');
// Shape Group: Group 1
var layer_8_group_0 = layer_8.property('Contents').addProperty('ADBE Vector Group');
layer_8_group_0.name = 'Group 1';
var layer_8_group_0_path_0 = layer_8_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-14.343, 1.947], [14.344, -0.949], [14.344, -1.949], [-14.343, 0.947]];
pathShape.inTangents = [[-0.641, 0.065], [-9.562, 0.965], [0.641, -0.064], [9.562, -0.965]];
pathShape.outTangents = [[9.562, -0.965], [0.634, -0.064], [-9.562, 0.965], [-0.634, 0.064]];
pathShape.closed = true;
layer_8_group_0_path_0.property('Path').setValue(pathShape);
var layer_8_group_0_fill_1 = layer_8_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_8_group_0_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_8_group_0_fill_1.property('Opacity').setValue(100);
var layer_8_group_0_transform = layer_8_group_0.property('Transform');
layer_8_group_0_transform.property('Position').setValue([57.461, 117.463]);
layer_8_group_0_transform.property('Scale').setValue([100, 100]);
layer_8_group_0_transform.property('Rotation').setValue(0);
layer_8_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_8_group_1 = layer_8.property('Contents').addProperty('ADBE Vector Group');
layer_8_group_1.name = 'Group 2';
var layer_8_group_1_path_0 = layer_8_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-29.501, -25.588], [-7.864, -3.909], [15.27, 15.987], [28.919, 26.388], [29.424, 25.526], [5.481, 6.63], [-16.887, -13.958], [-28.795, -26.294]];
pathShape.inTangents = [[-0.437, -0.472], [-7.484, -6.948], [-7.959, -6.336], [-4.621, -3.371], [0.515, 0.375], [7.738, 6.6], [7.193, 7.141], [3.882, 4.195]];
pathShape.outTangents = [[6.935, 7.496], [7.456, 6.922], [4.476, 3.563], [0.521, 0.38], [-8.217, -5.993], [-7.711, -6.577], [-4.056, -4.027], [-0.438, -0.474]];
pathShape.closed = true;
layer_8_group_1_path_0.property('Path').setValue(pathShape);
var layer_8_group_1_fill_1 = layer_8_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_8_group_1_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_8_group_1_fill_1.property('Opacity').setValue(100);
var layer_8_group_1_transform = layer_8_group_1.property('Transform');
layer_8_group_1_transform.property('Position').setValue([67.821, 58.891]);
layer_8_group_1_transform.property('Scale').setValue([100, 100]);
layer_8_group_1_transform.property('Rotation').setValue(0);
layer_8_group_1_transform.property('Opacity').setValue(100);
// Shape Group: Group 3
var layer_8_group_2 = layer_8.property('Contents').addProperty('ADBE Vector Group');
layer_8_group_2.name = 'Group 3';
var layer_8_group_2_path_0 = layer_8_group_2.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-40.854, -24.266], [-9.542, -4.59], [22.234, 14.408], [40.332, 25.152], [40.837, 24.29], [8.811, 5.267], [-22.805, -13.925], [-40.35, -25.129]];
pathShape.inTangents = [[-0.537, -0.352], [-10.536, -6.401], [-10.611, -6.3], [-6.033, -3.581], [0.554, 0.329], [10.667, 6.355], [10.459, 6.527], [5.805, 3.802]];
pathShape.outTangents = [[10.313, 6.755], [10.548, 6.407], [6.033, 3.582], [0.555, 0.33], [-10.676, -6.339], [-10.591, -6.31], [-5.887, -3.673], [-0.54, -0.354]];
pathShape.closed = true;
layer_8_group_2_path_0.property('Path').setValue(pathShape);
var layer_8_group_2_fill_1 = layer_8_group_2.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_8_group_2_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_8_group_2_fill_1.property('Opacity').setValue(100);
var layer_8_group_2_transform = layer_8_group_2.property('Transform');
layer_8_group_2_transform.property('Position').setValue([52.361, 66.089]);
layer_8_group_2_transform.property('Scale').setValue([100, 100]);
layer_8_group_2_transform.property('Rotation').setValue(0);
layer_8_group_2_transform.property('Opacity').setValue(100);
// Shape Group: Group 4
var layer_8_group_3 = layer_8.property('Contents').addProperty('ADBE Vector Group');
layer_8_group_3.name = 'Group 4';
var layer_8_group_3_path_0 = layer_8_group_3.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-35.575, -16.629], [-8.513, -1.387], [18.418, 9.9], [35.019, 17.572], [35.524, 16.709], [6.968, 4.089], [-19.589, -7.949], [-35.071, -17.492]];
pathShape.inTangents = [[-0.524, -0.366], [-9.46, -4.279], [-8.953, -3.818], [-5.444, -2.748], [0.575, 0.29], [9.642, 3.933], [8.572, 4.616], [4.973, 3.477]];
pathShape.outTangents = [[8.514, 5.954], [8.871, 4.011], [5.609, 2.391], [0.573, 0.29], [-9.302, -4.695], [-9.004, -3.674], [-5.342, -2.876], [-0.528, -0.37]];
pathShape.closed = true;
layer_8_group_3_path_0.property('Path').setValue(pathShape);
var layer_8_group_3_fill_1 = layer_8_group_3.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_8_group_3_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_8_group_3_fill_1.property('Opacity').setValue(100);
var layer_8_group_3_transform = layer_8_group_3.property('Transform');
layer_8_group_3_transform.property('Position').setValue([48.539, 78.838]);
layer_8_group_3_transform.property('Scale').setValue([100, 100]);
layer_8_group_3_transform.property('Rotation').setValue(0);
layer_8_group_3_transform.property('Opacity').setValue(100);
// Shape Group: Group 5
var layer_8_group_4 = layer_8.property('Contents').addProperty('ADBE Vector Group');
layer_8_group_4.name = 'Group 5';
var layer_8_group_4_path_0 = layer_8_group_4.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-28.731, -9.95], [-7.579, -1.214], [16.794, 6.67], [28.197, 10.883], [28.702, 10.02], [6.969, 2.442], [-17.247, -5.754], [-28.227, -10.814]];
pathShape.inTangents = [[-0.559, -0.321], [-7.19, -2.428], [-8.098, -2.709], [-3.723, -1.6], [0.589, 0.254], [7.3, 2.318], [7.941, 3.096], [3.498, 2.007]];
pathShape.outTangents = [[6.608, 3.79], [8.091, 2.731], [3.841, 1.284], [0.585, 0.251], [-7.046, -3.029], [-8.119, -2.578], [-3.752, -1.463], [-0.559, -0.32]];
pathShape.closed = true;
layer_8_group_4_path_0.property('Path').setValue(pathShape);
var layer_8_group_4_fill_1 = layer_8_group_4.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_8_group_4_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_8_group_4_fill_1.property('Opacity').setValue(100);
var layer_8_group_4_transform = layer_8_group_4.property('Transform');
layer_8_group_4_transform.property('Position').setValue([53.791, 101.994]);
layer_8_group_4_transform.property('Scale').setValue([100, 100]);
layer_8_group_4_transform.property('Rotation').setValue(0);
layer_8_group_4_transform.property('Opacity').setValue(100);
// Shape Group: Group 6
var layer_8_group_5 = layer_8.property('Contents').addProperty('ADBE Vector Group');
layer_8_group_5.name = 'Group 6';
var layer_8_group_5_path_0 = layer_8_group_5.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-25.532, 13.107], [14.619, -5.805], [25.83, -12.164], [25.325, -13.027], [-13.856, 7.036], [-25.798, 12.142]];
pathShape.inTangents = [[-0.595, 0.247], [-13.009, 7.08], [-3.701, 2.182], [0.557, -0.327], [13.435, -5.944], [3.999, -1.659]];
pathShape.outTangents = [[13.675, -5.674], [3.774, -2.054], [0.554, -0.327], [-12.657, 7.461], [-3.959, 1.751], [-0.586, 0.243]];
pathShape.closed = true;
layer_8_group_5_path_0.property('Path').setValue(pathShape);
var layer_8_group_5_fill_1 = layer_8_group_5.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_8_group_5_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_8_group_5_fill_1.property('Opacity').setValue(100);
var layer_8_group_5_transform = layer_8_group_5.property('Transform');
layer_8_group_5_transform.property('Position').setValue([52.347, 133.671]);
layer_8_group_5_transform.property('Scale').setValue([100, 100]);
layer_8_group_5_transform.property('Rotation').setValue(0);
layer_8_group_5_transform.property('Opacity').setValue(100);
// Shape Group: Group 7
var layer_8_group_6 = layer_8.property('Contents').addProperty('ADBE Vector Group');
layer_8_group_6.name = 'Group 7';
var layer_8_group_6_path_0 = layer_8_group_6.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-35.264, 27.771], [-5.498, 8.89], [21.655, -13.093], [35.883, -26.913], [35.176, -27.62], [9.444, -3.854], [-18.939, 16.833], [-35.768, 26.908]];
pathShape.inTangents = [[-0.563, 0.312], [-9.546, 6.864], [-8.621, 7.839], [-4.591, 4.759], [0.449, -0.463], [9.026, -7.418], [9.85, -6.34], [5.715, -3.178]];
pathShape.outTangents = [[10.276, -5.712], [9.46, -6.802], [4.892, -4.449], [0.448, -0.462], [-8.112, 8.409], [-9.05, 7.438], [-5.499, 3.539], [-0.563, 0.312]];
pathShape.closed = true;
layer_8_group_6_path_0.property('Path').setValue(pathShape);
var layer_8_group_6_fill_1 = layer_8_group_6.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_8_group_6_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_8_group_6_fill_1.property('Opacity').setValue(100);
var layer_8_group_6_transform = layer_8_group_6.property('Transform');
layer_8_group_6_transform.property('Position').setValue([48.966, 152.268]);
layer_8_group_6_transform.property('Scale').setValue([100, 100]);
layer_8_group_6_transform.property('Rotation').setValue(0);
layer_8_group_6_transform.property('Opacity').setValue(100);
// Shape Group: Group 8
var layer_8_group_7 = layer_8.property('Contents').addProperty('ADBE Vector Group');
layer_8_group_7.name = 'Group 8';
var layer_8_group_7_path_0 = layer_8_group_7.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-38.813, 37.69], [-20.964, 26.322], [-4.824, 12.938], [10.219, -2.146], [24.282, -18.173], [37.838, -34.743], [39.484, -36.788], [38.778, -37.496], [25.337, -20.99], [11.367, -4.87], [-3.39, 10.233], [-19.469, 23.962], [-37.024, 35.562], [-39.318, 36.828]];
pathShape.inTangents = [[-0.567, 0.307], [-5.683, 4.19], [-5.143, 4.738], [-4.825, 5.211], [-4.569, 5.445], [-4.476, 5.558], [-0.548, 0.682], [0.403, -0.501], [4.539, -5.454], [4.79, -5.254], [5.117, -4.836], [5.613, -4.268], [6.116, -3.447], [0.769, -0.415]];
pathShape.outTangents = [[6.214, -3.355], [5.629, -4.151], [5.224, -4.812], [4.831, -5.215], [4.588, -5.467], [0.549, -0.681], [0.4, -0.497], [-4.448, 5.53], [-4.547, 5.466], [-4.742, 5.203], [-5.123, 4.843], [-5.588, 4.248], [-0.761, 0.429], [-0.566, 0.305]];
pathShape.closed = true;
layer_8_group_7_path_0.property('Path').setValue(pathShape);
var layer_8_group_7_fill_1 = layer_8_group_7.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_8_group_7_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_8_group_7_fill_1.property('Opacity').setValue(100);
var layer_8_group_7_transform = layer_8_group_7.property('Transform');
layer_8_group_7_transform.property('Position').setValue([50.8, 168.36]);
layer_8_group_7_transform.property('Scale').setValue([100, 100]);
layer_8_group_7_transform.property('Rotation').setValue(0);
layer_8_group_7_transform.property('Opacity').setValue(100);
// Shape Group: Group 9
var layer_8_group_8 = layer_8.property('Contents').addProperty('ADBE Vector Group');
layer_8_group_8.name = 'Group 9';
var layer_8_group_8_path_0 = layer_8_group_8.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-33.138, 42.841], [-3.914, 11.981], [21.505, -21.967], [33.995, -42.218], [33.132, -42.722], [10.035, -7.305], [-17.051, 25.282], [-33.845, 42.134]];
pathShape.inTangents = [[-0.469, 0.44], [-9.135, 10.84], [-7.811, 11.791], [-3.944, 6.883], [0.32, -0.559], [8.371, -11.35], [9.646, -10.329], [5.778, -5.435]];
pathShape.outTangents = [[10.326, -9.712], [9.114, -10.817], [4.381, -6.612], [0.321, -0.559], [-7.011, 12.236], [-8.389, 11.373], [-5.414, 5.797], [-0.47, 0.441]];
pathShape.closed = true;
layer_8_group_8_path_0.property('Path').setValue(pathShape);
var layer_8_group_8_fill_1 = layer_8_group_8.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_8_group_8_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_8_group_8_fill_1.property('Opacity').setValue(100);
var layer_8_group_8_transform = layer_8_group_8.property('Transform');
layer_8_group_8_transform.property('Position').setValue([63.454, 177.807]);
layer_8_group_8_transform.property('Scale').setValue([100, 100]);
layer_8_group_8_transform.property('Rotation').setValue(0);
layer_8_group_8_transform.property('Opacity').setValue(100);
// Shape Group: Group 10
var layer_8_group_9 = layer_8.property('Contents').addProperty('ADBE Vector Group');
layer_8_group_9.name = 'Group 10';
var layer_8_group_9_path_0 = layer_8_group_9.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[28.313, -29.795], [12.946, -30.207], [-80.551, -97.811], [-84.866, -66.166], [-54.659, -4.316], [-66.166, 27.329], [-81.989, 93.496], [-47.467, 99.249], [12.946, 27.329], [35.468, 27.264]];
pathShape.inTangents = [[62.307, -6.166], [0, 0], [33.084, -21.576], [-5.754, -27.33], [-10.069, -7.192], [20.137, -24.453], [-8.631, -25.892], [0, 0], [-15.823, 33.083], [-18.207, -10.003]];
pathShape.outTangents = [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.631, 25.891], [0, 0], [0, 0], [35.014, 4.38]];
pathShape.closed = true;
layer_8_group_9_path_0.property('Path').setValue(pathShape);
var layer_8_group_9_fill_1 = layer_8_group_9.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_8_group_9_fill_1.property('Color').setValue([0.972549, 0.305882, 0.192157]);
layer_8_group_9_fill_1.property('Opacity').setValue(100);
var layer_8_group_9_transform = layer_8_group_9.property('Transform');
layer_8_group_9_transform.property('Position').setValue([90.87, 119.638]);
layer_8_group_9_transform.property('Scale').setValue([100, 100]);
layer_8_group_9_transform.property('Rotation').setValue(0);
layer_8_group_9_transform.property('Opacity').setValue(100);
layer_8.inPoint = 0.000000;
layer_8.outPoint = 6.250000;
layer_8.startTime = 0.000000;
layer_8.parent = layer_4;
layer_8.property('Transform').property('Opacity').setValue(100);
layer_8.property('Transform').property('Position').setValue([69.826, 115.323, 0]);
layer_8.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 150 keyframes for layer_8.property('Transform').property('Rotation')
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 0, 12.433, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 1, 12.334, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 2, 12.048, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 3, 11.592, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 4, 10.981, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 5, 10.233, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 6, 9.362, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 7, 8.386, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 8, 7.32, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 9, 6.182, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 10, 4.986, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 11, 3.75, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 12, 2.49, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 13, 1.221, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 14, -0.039, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 15, -1.275, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 16, -2.471, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 17, -3.609, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 18, -4.675, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 19, -5.651, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 20, -6.521, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 21, -7.27, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 22, -7.88, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 23, -8.337, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 24, -8.622, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 25, -8.721, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 26, -8.622, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 27, -8.337, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 28, -7.88, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 29, -7.27, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 30, -6.521, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 31, -5.651, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 32, -4.675, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 33, -3.609, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 34, -2.471, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 35, -1.275, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 36, -0.039, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 37, 1.221, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 38, 2.49, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 39, 3.75, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 40, 4.986, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 41, 6.182, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 42, 7.32, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 43, 8.386, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 44, 9.362, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 45, 10.233, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 46, 10.981, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 47, 11.592, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 48, 12.048, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 49, 12.334, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 50, 12.433, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 51, 12.334, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 52, 12.048, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 53, 11.592, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 54, 10.981, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 55, 10.233, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 56, 9.362, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 57, 8.386, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 58, 7.32, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 59, 6.182, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 60, 4.986, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 61, 3.75, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 62, 2.49, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 63, 1.221, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 64, -0.039, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 65, -1.275, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 66, -2.471, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 67, -3.609, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 68, -4.675, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 69, -5.651, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 70, -6.521, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 71, -7.27, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 72, -7.88, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 73, -8.337, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 74, -8.622, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 75, -8.721, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 76, -8.622, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 77, -8.337, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 78, -7.88, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 79, -7.27, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 80, -6.521, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 81, -5.651, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 82, -4.675, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 83, -3.609, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 84, -2.471, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 85, -1.275, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 86, -0.039, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 87, 1.221, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 88, 2.49, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 89, 3.75, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 90, 4.986, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 91, 6.182, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 92, 7.32, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 93, 8.386, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 94, 9.362, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 95, 10.233, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 96, 10.981, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 97, 11.592, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 98, 12.048, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 99, 12.334, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 100, 12.433, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 101, 12.334, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 102, 12.048, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 103, 11.592, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 104, 10.981, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 105, 10.233, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 106, 9.362, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 107, 8.386, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 108, 7.32, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 109, 6.182, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 110, 4.986, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 111, 3.75, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 112, 2.49, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 113, 1.221, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 114, -0.039, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 115, -1.275, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 116, -2.471, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 117, -3.609, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 118, -4.675, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 119, -5.651, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 120, -6.521, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 121, -7.27, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 122, -7.88, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 123, -8.337, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 124, -8.622, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 125, -8.721, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 126, -8.622, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 127, -8.337, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 128, -7.88, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 129, -7.27, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 130, -6.521, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 131, -5.651, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 132, -4.675, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 133, -3.609, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 134, -2.471, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 135, -1.275, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 136, -0.039, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 137, 1.221, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 138, 2.49, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 139, 3.75, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 140, 4.986, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 141, 6.182, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 142, 7.32, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 143, 8.386, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 144, 9.362, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 145, 10.233, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 146, 10.981, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 147, 11.592, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 148, 12.048, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
setKeyframeWithEasing(layer_8.property('Transform').property('Rotation'), 149, 12.334, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 12.39, 0.042);
layer_8.property('Transform').property('Anchor Point').setValue([138.87, 119.638, 0]);

// Layer 7: Fether
var layer_7 = createShapeLayer(comp, 'Fether');
// Shape Group: Group 1
var layer_7_group_0 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_0.name = 'Group 1';
var layer_7_group_0_path_0 = layer_7_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-35.165, 6.531], [-7.81, 0.898], [19.757, -3.581], [35.419, -5.6], [35.419, -6.6], [7.74, -2.781], [-19.983, 2.24], [-35.432, 5.566]];
pathShape.inTangents = [[-0.627, 0.143], [-9.157, 1.687], [-9.219, 1.3], [-5.228, 0.612], [0.639, -0.074], [9.199, -1.465], [9.204, -1.869], [5.137, -1.169]];
pathShape.outTangents = [[9.078, -2.067], [9.156, -1.685], [5.213, -0.736], [0.632, -0.073], [-9.251, 1.082], [-9.275, 1.478], [-5.162, 1.048], [-0.627, 0.143]];
pathShape.closed = true;
layer_7_group_0_path_0.property('Path').setValue(pathShape);
var layer_7_group_0_fill_1 = layer_7_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_0_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_7_group_0_fill_1.property('Opacity').setValue(100);
var layer_7_group_0_transform = layer_7_group_0.property('Transform');
layer_7_group_0_transform.property('Position').setValue([46.217, 11.976]);
layer_7_group_0_transform.property('Scale').setValue([100, 100]);
layer_7_group_0_transform.property('Rotation').setValue(0);
layer_7_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_7_group_1 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_1.name = 'Group 2';
var layer_7_group_1_path_0 = layer_7_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-38.909, 11.67], [-8.96, 1.417], [21.692, -6.956], [39.14, -10.808], [38.874, -11.771], [8.06, -4.524], [-22.351, 4.688], [-39.175, 10.705]];
pathShape.inTangents = [[-0.604, 0.228], [-10.088, 3.102], [-10.302, 2.467], [-5.838, 1.181], [0.63, -0.127], [10.192, -2.74], [10.036, -3.391], [5.572, -2.104]];
pathShape.outTangents = [[9.873, -3.729], [10.126, -3.113], [5.793, -1.387], [0.631, -0.127], [-10.344, 2.093], [-10.231, 2.749], [-5.643, 1.906], [-0.596, 0.226]];
pathShape.closed = true;
layer_7_group_1_path_0.property('Path').setValue(pathShape);
var layer_7_group_1_fill_1 = layer_7_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_1_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_7_group_1_fill_1.property('Opacity').setValue(100);
var layer_7_group_1_transform = layer_7_group_1.property('Transform');
layer_7_group_1_transform.property('Position').setValue([44.802, 20.17]);
layer_7_group_1_transform.property('Scale').setValue([100, 100]);
layer_7_group_1_transform.property('Rotation').setValue(0);
layer_7_group_1_transform.property('Opacity').setValue(100);
// Shape Group: Group 3
var layer_7_group_2 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_2.name = 'Group 3';
var layer_7_group_2_path_0 = layer_7_group_2.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-33.547, 21.877], [-6.215, 6.5], [19.849, -10.519], [34.117, -20.921], [33.613, -21.784], [8.007, -3.678], [-18.539, 12.58], [-34.052, 21.014]];
pathShape.inTangents = [[-0.571, 0.297], [-8.938, 5.426], [-8.499, 5.958], [-4.692, 3.555], [0.513, -0.39], [8.733, -5.751], [9.024, -5.126], [5.223, -2.716]];
pathShape.outTangents = [[9.276, -4.823], [8.871, -5.385], [4.82, -3.377], [0.506, -0.384], [-8.334, 6.314], [-8.667, 5.708], [-5.118, 2.907], [-0.571, 0.297]];
pathShape.closed = true;
layer_7_group_2_path_0.property('Path').setValue(pathShape);
var layer_7_group_2_fill_1 = layer_7_group_2.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_2_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_7_group_2_fill_1.property('Opacity').setValue(100);
var layer_7_group_2_transform = layer_7_group_2.property('Transform');
layer_7_group_2_transform.property('Position').setValue([53.833, 27.311]);
layer_7_group_2_transform.property('Scale').setValue([100, 100]);
layer_7_group_2_transform.property('Rotation').setValue(0);
layer_7_group_2_transform.property('Opacity').setValue(100);
// Shape Group: Group 4
var layer_7_group_3 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_3.name = 'Group 4';
var layer_7_group_3_path_0 = layer_7_group_3.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-29.706, 27.063], [-6.234, 6.263], [17.204, -14.596], [30.415, -26.354], [29.708, -27.061], [6.264, -6.196], [-17.176, 14.648], [-30.414, 26.356]];
pathShape.inTangents = [[-0.481, 0.425], [-7.812, 6.947], [-7.813, 6.953], [-4.404, 3.92], [0.48, -0.427], [7.815, -6.955], [7.822, -6.939], [4.42, -3.895]];
pathShape.outTangents = [[7.842, -6.912], [7.815, -6.951], [4.403, -3.919], [0.482, -0.429], [-7.815, 6.955], [-7.81, 6.951], [-4.406, 3.909], [-0.483, 0.427]];
pathShape.closed = true;
layer_7_group_3_path_0.property('Path').setValue(pathShape);
var layer_7_group_3_fill_1 = layer_7_group_3.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_3_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_7_group_3_fill_1.property('Opacity').setValue(100);
var layer_7_group_3_transform = layer_7_group_3.property('Transform');
layer_7_group_3_transform.property('Position').setValue([58.593, 40.315]);
layer_7_group_3_transform.property('Scale').setValue([100, 100]);
layer_7_group_3_transform.property('Rotation').setValue(0);
layer_7_group_3_transform.property('Opacity').setValue(100);
// Shape Group: Group 5
var layer_7_group_4 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_4.name = 'Group 5';
var layer_7_group_4_path_0 = layer_7_group_4.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[22.64, 2.018], [-0.266, 27.335], [-9.365, 32.405], [-13.779, 34.516], [-24.081, 37.266], [-32.02, 27.91], [-37.806, 16.624], [-46.586, 6.404], [-46.934, -15.655], [-31.243, -27.691], [-26.266, -28.188], [31.27, -35.38], [41.339, -13.804]];
pathShape.inTangents = [[0, 0], [0, 0], [3.487, -0.747], [1.356, -1.177], [3.654, 0.991], [0, 5.766], [2.741, 1.673], [1.589, 4.348], [-3.035, 8.665], [-7.09, 0.709], [0, 0], [0, 0], [8.631, -24.453]];
pathShape.outTangents = [[0, 0], [-2.392, 2.645], [-1.469, 0.314], [-2.861, 2.481], [-3.965, -1.074], [0, -6.33], [-3.952, -2.412], [-2.036, -5.57], [2.355, -6.725], [0, 0], [0, 0], [0, 0], [0, 0]];
pathShape.closed = true;
layer_7_group_4_path_0.property('Path').setValue(pathShape);
var layer_7_group_4_fill_1 = layer_7_group_4.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_4_fill_1.property('Color').setValue([0.972549, 0.305882, 0.192157]);
layer_7_group_4_fill_1.property('Opacity').setValue(100);
var layer_7_group_4_transform = layer_7_group_4.property('Transform');
layer_7_group_4_transform.property('Position').setValue([50.219, 38.507]);
layer_7_group_4_transform.property('Scale').setValue([100, 100]);
layer_7_group_4_transform.property('Rotation').setValue(0);
layer_7_group_4_transform.property('Opacity').setValue(100);
layer_7.inPoint = 0.000000;
layer_7.outPoint = 6.250000;
layer_7.startTime = 0.000000;
layer_7.parent = layer_4;
layer_7.property('Transform').property('Opacity').setValue(100);
layer_7.property('Transform').property('Position').setValue([100.353, 191.869, 0]);
layer_7.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 150 keyframes for layer_7.property('Transform').property('Rotation')
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 0, 0, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 1, -0.143, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 2, -0.557, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 3, -1.218, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 4, -2.103, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 5, -3.187, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 6, -4.448, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 7, -5.863, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 8, -7.406, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 9, -9.056, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 10, -10.788, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 11, -12.578, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 12, -14.404, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 13, -16.242, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 14, -18.068, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 15, -19.859, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 16, -21.591, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 17, -23.24, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 18, -24.784, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 19, -26.198, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 20, -27.459, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 21, -28.544, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 22, -29.429, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 23, -30.09, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 24, -30.503, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 25, -30.647, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 26, -30.503, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 27, -30.09, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 28, -29.429, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 29, -28.544, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 30, -27.459, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 31, -26.198, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 32, -24.784, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 33, -23.24, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 34, -21.591, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 35, -19.859, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 36, -18.068, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 37, -16.242, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 38, -14.404, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 39, -12.578, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 40, -10.788, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 41, -9.056, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 42, -7.406, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 43, -5.863, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 44, -4.448, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 45, -3.187, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 46, -2.103, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 47, -1.218, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 48, -0.557, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 49, -0.143, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 50, 0, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 51, -0.143, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 52, -0.557, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 53, -1.218, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 54, -2.103, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 55, -3.187, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 56, -4.448, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 57, -5.863, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 58, -7.406, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 59, -9.056, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 60, -10.788, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 61, -12.578, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 62, -14.404, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 63, -16.242, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 64, -18.068, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 65, -19.859, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 66, -21.591, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 67, -23.24, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 68, -24.784, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 69, -26.198, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 70, -27.459, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 71, -28.544, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 72, -29.429, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 73, -30.09, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 74, -30.503, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 75, -30.647, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 76, -30.503, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 77, -30.09, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 78, -29.429, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 79, -28.544, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 80, -27.459, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 81, -26.198, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 82, -24.784, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 83, -23.24, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 84, -21.591, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 85, -19.859, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 86, -18.068, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 87, -16.242, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 88, -14.404, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 89, -12.578, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 90, -10.788, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 91, -9.056, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 92, -7.406, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 93, -5.863, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 94, -4.448, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 95, -3.187, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 96, -2.103, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 97, -1.218, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 98, -0.557, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 99, -0.143, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 100, 0, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 101, -0.143, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 102, -0.557, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 103, -1.218, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 104, -2.103, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 105, -3.187, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 106, -4.448, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 107, -5.863, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 108, -7.406, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 109, -9.056, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 110, -10.788, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 111, -12.578, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 112, -14.404, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 113, -16.242, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 114, -18.068, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 115, -19.859, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 116, -21.591, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 117, -23.24, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 118, -24.784, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 119, -26.198, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 120, -27.459, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 121, -28.544, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 122, -29.429, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 123, -30.09, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 124, -30.503, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 125, -30.647, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 126, -30.503, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 127, -30.09, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 128, -29.429, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 129, -28.544, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 130, -27.459, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 131, -26.198, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 132, -24.784, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 133, -23.24, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 134, -21.591, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 135, -19.859, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 136, -18.068, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 137, -16.242, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 138, -14.404, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 139, -12.578, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 140, -10.788, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 141, -9.056, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 142, -7.406, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 143, -5.863, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 144, -4.448, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 145, -3.187, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 146, -2.103, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 147, -1.218, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 148, -0.557, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
setKeyframeWithEasing(layer_7.property('Transform').property('Rotation'), 149, -0.143, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.65, 0.042);
layer_7.property('Transform').property('Anchor Point').setValue([85.22, 13.507, 0]);

// Layer 6: Fether
var layer_6 = createShapeLayer(comp, 'Fether');
// Shape Group: Group 1
var layer_6_group_0 = layer_6.property('Contents').addProperty('ADBE Vector Group');
layer_6_group_0.name = 'Group 1';
var layer_6_group_0_path_0 = layer_6_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[11.401, -27.085], [-7.77, 14.578], [-12.349, 26.786], [-11.385, 27.052], [6.443, -15.178], [12.263, -26.58]];
pathShape.inTangents = [[0.302, -0.569], [5.602, -14.233], [1.462, -4.093], [-0.215, 0.602], [-6.734, 13.726], [-1.998, 3.77]];
pathShape.outTangents = [[-7.165, 13.515], [-1.592, 4.044], [-0.216, 0.608], [5.142, -14.399], [1.88, -3.831], [0.302, -0.568]];
pathShape.closed = true;
layer_6_group_0_path_0.property('Path').setValue(pathShape);
var layer_6_group_0_fill_1 = layer_6_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_6_group_0_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_6_group_0_fill_1.property('Opacity').setValue(100);
var layer_6_group_0_transform = layer_6_group_0.property('Transform');
layer_6_group_0_transform.property('Position').setValue([35.789, 36.382]);
layer_6_group_0_transform.property('Scale').setValue([100, 100]);
layer_6_group_0_transform.property('Rotation').setValue(0);
layer_6_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_6_group_1 = layer_6.property('Contents').addProperty('ADBE Vector Group');
layer_6_group_1.name = 'Group 2';
var layer_6_group_1_path_0 = layer_6_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[6.409, -28.304], [-3.308, 16.026], [-7.246, 28.07], [-6.282, 28.336], [5.364, -15.528], [7.373, -28.038]];
pathShape.inTangents = [[0.088, -0.636], [4.391, -14.494], [1.399, -3.985], [-0.212, 0.604], [-2.728, 14.898], [-0.58, 4.183]];
pathShape.outTangents = [[-2.079, 15.001], [-1.224, 4.043], [-0.214, 0.608], [5.016, -14.289], [0.761, -4.155], [0.087, -0.63]];
pathShape.closed = true;
layer_6_group_1_path_0.property('Path').setValue(pathShape);
var layer_6_group_1_fill_1 = layer_6_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_6_group_1_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_6_group_1_fill_1.property('Opacity').setValue(100);
var layer_6_group_1_transform = layer_6_group_1.property('Transform');
layer_6_group_1_transform.property('Position').setValue([45.811, 36.484]);
layer_6_group_1_transform.property('Scale').setValue([100, 100]);
layer_6_group_1_transform.property('Rotation').setValue(0);
layer_6_group_1_transform.property('Opacity').setValue(100);
// Shape Group: Group 3
var layer_6_group_2 = layer_6.property('Contents').addProperty('ADBE Vector Group');
layer_6_group_2.name = 'Group 3';
var layer_6_group_2_path_0 = layer_6_group_2.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[17.119, -30.431], [0.843, -8.398], [-12.227, 15.778], [-18.023, 30.053], [-17.059, 30.319], [-6.027, 5.408], [8.325, -17.646], [17.826, -29.724]];
pathShape.inTangents = [[0.418, -0.49], [4.9, -7.714], [3.781, -8.353], [1.745, -4.832], [-0.217, 0.602], [-4.256, 8.033], [-5.312, 7.339], [-3.325, 3.899]];
pathShape.outTangents = [[-5.93, 6.954], [-4.917, 7.738], [-2.118, 4.681], [-0.219, 0.606], [3.088, -8.55], [4.241, -8.005], [3.003, -4.151], [0.416, -0.486]];
pathShape.closed = true;
layer_6_group_2_path_0.property('Path').setValue(pathShape);
var layer_6_group_2_fill_1 = layer_6_group_2.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_6_group_2_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_6_group_2_fill_1.property('Opacity').setValue(100);
var layer_6_group_2_transform = layer_6_group_2.property('Transform');
layer_6_group_2_transform.property('Position').setValue([31.105, 34.056]);
layer_6_group_2_transform.property('Scale').setValue([100, 100]);
layer_6_group_2_transform.property('Rotation').setValue(0);
layer_6_group_2_transform.property('Opacity').setValue(100);
// Shape Group: Group 4
var layer_6_group_3 = layer_6.property('Contents').addProperty('ADBE Vector Group');
layer_6_group_3.name = 'Group 4';
var layer_6_group_3_path_0 = layer_6_group_3.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[13.009, -19.15], [-8.503, 9.827], [-13.81, 18.577], [-12.947, 19.081], [7.27, -10.643], [13.715, -18.443]];
pathShape.inTangents = [[0.42, -0.488], [6.457, -10.164], [1.71, -2.953], [-0.323, 0.557], [-7.451, 9.398], [-2.201, 2.557]];
pathShape.outTangents = [[-7.854, 9.126], [-1.829, 2.879], [-0.324, 0.557], [6.012, -10.377], [2.095, -2.644], [0.418, -0.485]];
pathShape.closed = true;
layer_6_group_3_path_0.property('Path').setValue(pathShape);
var layer_6_group_3_fill_1 = layer_6_group_3.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_6_group_3_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_6_group_3_fill_1.property('Opacity').setValue(100);
var layer_6_group_3_transform = layer_6_group_3.property('Transform');
layer_6_group_3_transform.property('Position').setValue([22.393, 26.424]);
layer_6_group_3_transform.property('Scale').setValue([100, 100]);
layer_6_group_3_transform.property('Rotation').setValue(0);
layer_6_group_3_transform.property('Opacity').setValue(100);
// Shape Group: Group 5
var layer_6_group_4 = layer_6.property('Contents').addProperty('ADBE Vector Group');
layer_6_group_4.name = 'Group 5';
var layer_6_group_4_path_0 = layer_6_group_4.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[2.877, -36.679], [-14.384, -20.857], [-20.137, -9.35], [-24.453, 22.296], [8.63, 22.296], [27.329, -32.365]];
pathShape.inTangents = [[0, 0], [0, 0], [-1.439, -4.315], [-8.63, -14.385], [0, 0], [5.754, 44.591]];
pathShape.outTangents = [[0, 0], [0, 0], [0, 0], [8.631, 14.383], [0, 0], [0, 0]];
pathShape.closed = true;
layer_6_group_4_path_0.property('Path').setValue(pathShape);
var layer_6_group_4_fill_1 = layer_6_group_4.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_6_group_4_fill_1.property('Color').setValue([0.972549, 0.305882, 0.192157]);
layer_6_group_4_fill_1.property('Opacity').setValue(100);
var layer_6_group_4_transform = layer_6_group_4.property('Transform');
layer_6_group_4_transform.property('Position').setValue([33.333, 36.929]);
layer_6_group_4_transform.property('Scale').setValue([100, 100]);
layer_6_group_4_transform.property('Rotation').setValue(0);
layer_6_group_4_transform.property('Opacity').setValue(100);
layer_6.inPoint = 0.000000;
layer_6.outPoint = 6.250000;
layer_6.startTime = 0.000000;
layer_6.parent = layer_4;
layer_6.property('Transform').property('Opacity').setValue(100);
layer_6.property('Transform').property('Position').setValue([133.514, 217.937, 0]);
layer_6.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 150 keyframes for layer_6.property('Transform').property('Rotation')
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 0, 0, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 1, -0.199, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 2, -0.773, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 3, -1.69, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 4, -2.918, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 5, -4.423, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 6, -6.174, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 7, -8.136, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 8, -10.278, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 9, -12.568, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 10, -14.971, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 11, -17.456, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 12, -19.99, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 13, -22.541, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 14, -25.075, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 15, -27.56, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 16, -29.964, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 17, -32.253, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 18, -34.395, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 19, -36.358, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 20, -38.108, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 21, -39.613, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 22, -40.841, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 23, -41.758, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 24, -42.333, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 25, -42.531, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 26, -42.333, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 27, -41.758, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 28, -40.841, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 29, -39.613, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 30, -38.108, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 31, -36.358, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 32, -34.395, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 33, -32.253, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 34, -29.964, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 35, -27.56, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 36, -25.075, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 37, -22.541, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 38, -19.99, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 39, -17.456, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 40, -14.971, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 41, -12.568, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 42, -10.278, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 43, -8.136, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 44, -6.174, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 45, -4.423, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 46, -2.918, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 47, -1.69, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 48, -0.773, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 49, -0.199, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 50, 0, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 51, -0.199, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 52, -0.773, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 53, -1.69, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 54, -2.918, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 55, -4.423, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 56, -6.174, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 57, -8.136, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 58, -10.278, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 59, -12.568, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 60, -14.971, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 61, -17.456, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 62, -19.99, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 63, -22.541, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 64, -25.075, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 65, -27.56, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 66, -29.964, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 67, -32.253, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 68, -34.395, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 69, -36.358, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 70, -38.108, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 71, -39.613, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 72, -40.841, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 73, -41.758, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 74, -42.333, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 75, -42.531, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 76, -42.333, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 77, -41.758, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 78, -40.841, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 79, -39.613, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 80, -38.108, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 81, -36.358, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 82, -34.395, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 83, -32.253, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 84, -29.964, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 85, -27.56, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 86, -25.075, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 87, -22.541, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 88, -19.99, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 89, -17.456, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 90, -14.971, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 91, -12.568, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 92, -10.278, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 93, -8.136, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 94, -6.174, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 95, -4.423, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 96, -2.918, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 97, -1.69, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 98, -0.773, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 99, -0.199, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 100, 0, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 101, -0.199, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 102, -0.773, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 103, -1.69, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 104, -2.918, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 105, -4.423, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 106, -6.174, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 107, -8.136, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 108, -10.278, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 109, -12.568, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 110, -14.971, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 111, -17.456, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 112, -19.99, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 113, -22.541, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 114, -25.075, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 115, -27.56, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 116, -29.964, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 117, -32.253, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 118, -34.395, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 119, -36.358, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 120, -38.108, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 121, -39.613, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 122, -40.841, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 123, -41.758, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 124, -42.333, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 125, -42.531, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 126, -42.333, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 127, -41.758, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 128, -40.841, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 129, -39.613, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 130, -38.108, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 131, -36.358, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 132, -34.395, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 133, -32.253, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 134, -29.964, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 135, -27.56, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 136, -25.075, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 137, -22.541, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 138, -19.99, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 139, -17.456, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 140, -14.971, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 141, -12.568, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 142, -10.278, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 143, -8.136, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 144, -6.174, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 145, -4.423, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 146, -2.918, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 147, -1.69, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 148, -0.773, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
setKeyframeWithEasing(layer_6.property('Transform').property('Rotation'), 149, -0.199, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 42.53, 0.042);
layer_6.property('Transform').property('Anchor Point').setValue([44.333, 7.929, 0]);

// Layer 5: Fether
var layer_5 = createShapeLayer(comp, 'Fether');
// Shape Group: Group 1
var layer_5_group_0 = layer_5.property('Contents').addProperty('ADBE Vector Group');
layer_5_group_0.name = 'Group 1';
var layer_5_group_0_path_0 = layer_5_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-17.223, -26.01], [-5.245, -4.783], [8.273, 15.636], [16.423, 26.578], [17.131, 25.871], [2.899, 6.1], [-9.847, -14.539], [-16.36, -26.515]];
pathShape.inTangents = [[-0.297, -0.571], [-4.243, -6.932], [-4.747, -6.643], [-2.787, -3.595], [0.389, 0.503], [4.508, 6.756], [4.005, 7.027], [2.093, 4.034]];
pathShape.outTangents = [[3.742, 7.213], [4.261, 6.963], [2.644, 3.701], [0.394, 0.508], [-4.976, -6.419], [-4.489, -6.727], [-2.25, -3.949], [-0.297, -0.571]];
pathShape.closed = true;
layer_5_group_0_path_0.property('Path').setValue(pathShape);
var layer_5_group_0_fill_1 = layer_5_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_5_group_0_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_5_group_0_fill_1.property('Opacity').setValue(100);
var layer_5_group_0_transform = layer_5_group_0.property('Transform');
layer_5_group_0_transform.property('Position').setValue([61.851, 79.723]);
layer_5_group_0_transform.property('Scale').setValue([100, 100]);
layer_5_group_0_transform.property('Rotation').setValue(0);
layer_5_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_5_group_1 = layer_5.property('Contents').addProperty('ADBE Vector Group');
layer_5_group_1.name = 'Group 2';
var layer_5_group_1_path_0 = layer_5_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-18.701, -33.662], [-9.297, -3.933], [5.657, 22.325], [17.85, 34.162], [18.355, 33.299], [-1.107, 10.686], [-13.049, -17.35], [-17.737, -33.929]];
pathShape.inTangents = [[-0.16, -0.625], [-3.92, -9.64], [-6.376, -7.874], [-4.537, -3.429], [0.506, 0.384], [5.029, 8.661], [3.078, 9.689], [1.425, 5.565]];
pathShape.outTangents = [[2.579, 10.07], [3.805, 9.357], [3.579, 4.419], [0.512, 0.389], [-8.019, -6.062], [-5.115, -8.809], [-1.739, -5.474], [-0.16, -0.623]];
pathShape.closed = true;
layer_5_group_1_path_0.property('Path').setValue(pathShape);
var layer_5_group_1_fill_1 = layer_5_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_5_group_1_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_5_group_1_fill_1.property('Opacity').setValue(100);
var layer_5_group_1_transform = layer_5_group_1.property('Transform');
layer_5_group_1_transform.property('Position').setValue([57.911, 84.923]);
layer_5_group_1_transform.property('Scale').setValue([100, 100]);
layer_5_group_1_transform.property('Rotation').setValue(0);
layer_5_group_1_transform.property('Opacity').setValue(100);
// Shape Group: Group 3
var layer_5_group_2 = layer_5.property('Contents').addProperty('ADBE Vector Group');
layer_5_group_2.name = 'Group 3';
var layer_5_group_2_path_0 = layer_5_group_2.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-9.763, -32.072], [-7.564, -5.655], [1.245, 19.347], [8.944, 32.194], [9.807, 31.689], [-2.449, 8.362], [-8.384, -17.313], [-8.763, -32.072]];
pathShape.inTangents = [[0.03, -0.642], [-1.859, -8.665], [-3.981, -7.915], [-2.878, -4.087], [0.372, 0.527], [3.057, 8.263], [0.866, 8.763], [-0.23, 4.922]];
pathShape.outTangents = [[-0.416, 8.854], [1.86, 8.662], [2.246, 4.466], [0.368, 0.521], [-5.073, -7.204], [-3.054, -8.259], [-0.484, -4.904], [0.03, -0.643]];
pathShape.closed = true;
layer_5_group_2_path_0.property('Path').setValue(pathShape);
var layer_5_group_2_fill_1 = layer_5_group_2.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_5_group_2_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_5_group_2_fill_1.property('Opacity').setValue(100);
var layer_5_group_2_transform = layer_5_group_2.property('Transform');
layer_5_group_2_transform.property('Position').setValue([41.05, 81.117]);
layer_5_group_2_transform.property('Scale').setValue([100, 100]);
layer_5_group_2_transform.property('Rotation').setValue(0);
layer_5_group_2_transform.property('Opacity').setValue(100);
// Shape Group: Group 4
var layer_5_group_3 = layer_5.property('Contents').addProperty('ADBE Vector Group');
layer_5_group_3.name = 'Group 4';
var layer_5_group_3_path_0 = layer_5_group_3.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[2.893, -29.583], [-4.695, -8.926], [-1.053, 15.437], [4.077, 29.489], [5.04, 29.223], [-2.77, 4.844], [-2.085, -17.916], [3.6, -28.876]];
pathShape.inTangents = [[0.405, -0.5], [0.521, -7.445], [-2.505, -7.852], [-1.808, -4.647], [0.235, 0.601], [1.601, 8.41], [-2.214, 7.38], [-2.616, 3.225]];
pathShape.outTangents = [[-4.745, 5.85], [-0.579, 8.28], [1.515, 4.752], [0.23, 0.594], [-3.092, -7.952], [-1.431, -7.518], [1.191, -3.97], [0.402, -0.495]];
pathShape.closed = true;
layer_5_group_3_path_0.property('Path').setValue(pathShape);
var layer_5_group_3_fill_1 = layer_5_group_3.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_5_group_3_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_5_group_3_fill_1.property('Opacity').setValue(100);
var layer_5_group_3_transform = layer_5_group_3.property('Transform');
layer_5_group_3_transform.property('Position').setValue([24.669, 72.924]);
layer_5_group_3_transform.property('Scale').setValue([100, 100]);
layer_5_group_3_transform.property('Rotation').setValue(0);
layer_5_group_3_transform.property('Opacity').setValue(100);
// Shape Group: Group 5
var layer_5_group_4 = layer_5.property('Contents').addProperty('ADBE Vector Group');
layer_5_group_4.name = 'Group 5';
var layer_5_group_4_path_0 = layer_5_group_4.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[4.616, -19.644], [-4.147, -5.517], [-3.74, 11.216], [0.755, 19.551], [1.461, 18.844], [-0.49, -11.954], [5.323, -18.937]];
pathShape.inTangents = [[0.492, -0.408], [1.421, -5.454], [-1.746, -5.376], [-2, -2.481], [0.4, 0.496], [-5.328, 9.44], [-2.353, 1.951]];
pathShape.outTangents = [[-4.357, 3.614], [-1.423, 5.459], [0.983, 3.031], [0.404, 0.501], [-6.906, -8.565], [1.502, -2.661], [0.496, -0.411]];
pathShape.closed = true;
layer_5_group_4_path_0.property('Path').setValue(pathShape);
var layer_5_group_4_fill_1 = layer_5_group_4.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_5_group_4_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_5_group_4_fill_1.property('Opacity').setValue(100);
var layer_5_group_4_transform = layer_5_group_4.property('Transform');
layer_5_group_4_transform.property('Position').setValue([15.679, 57.1]);
layer_5_group_4_transform.property('Scale').setValue([100, 100]);
layer_5_group_4_transform.property('Rotation').setValue(0);
layer_5_group_4_transform.property('Opacity').setValue(100);
// Shape Group: Group 6
var layer_5_group_5 = layer_5.property('Contents').addProperty('ADBE Vector Group');
layer_5_group_5.name = 'Group 6';
var layer_5_group_5_path_0 = layer_5_group_5.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-39.556, -2.158], [12.227, 53.941], [36.679, 43.871], [9.715, -7.953]];
pathShape.inTangents = [[-5.753, -60.413], [-33.084, -8.631], [0, 0], [0, 0]];
pathShape.outTangents = [[0, 0], [33.083, 8.63], [0, 0], [0, 0]];
pathShape.closed = true;
layer_5_group_5_path_0.property('Path').setValue(pathShape);
var layer_5_group_5_fill_1 = layer_5_group_5.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_5_group_5_fill_1.property('Color').setValue([0.972549, 0.305882, 0.192157]);
layer_5_group_5_fill_1.property('Opacity').setValue(100);
var layer_5_group_5_transform = layer_5_group_5.property('Transform');
layer_5_group_5_transform.property('Position').setValue([45.559, 62.82]);
layer_5_group_5_transform.property('Scale').setValue([100, 100]);
layer_5_group_5_transform.property('Rotation').setValue(0);
layer_5_group_5_transform.property('Opacity').setValue(100);
layer_5.inPoint = 0.000000;
layer_5.outPoint = 6.250000;
layer_5.startTime = 0.000000;
layer_5.parent = layer_4;
layer_5.property('Transform').property('Opacity').setValue(100);
layer_5.property('Transform').property('Position').setValue([179.277, 206.553, 0]);
layer_5.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 150 keyframes for layer_5.property('Transform').property('Rotation')
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 0, 0, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 1, 0.22, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 2, 0.857, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 3, 1.873, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 4, 3.233, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 5, 4.901, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 6, 6.841, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 7, 9.015, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 8, 11.389, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 9, 13.926, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 10, 16.589, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 11, 19.343, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 12, 22.151, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 13, 24.977, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 14, 27.785, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 15, 30.539, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 16, 33.202, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 17, 35.739, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 18, 38.113, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 19, 40.287, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 20, 42.227, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 21, 43.895, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 22, 45.255, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 23, 46.271, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 24, 46.908, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 25, 47.128, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 26, 46.908, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 27, 46.271, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 28, 45.255, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 29, 43.895, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 30, 42.227, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 31, 40.287, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 32, 38.113, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 33, 35.739, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 34, 33.202, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 35, 30.539, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 36, 27.785, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 37, 24.977, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 38, 22.151, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 39, 19.343, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 40, 16.589, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 41, 13.926, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 42, 11.389, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 43, 9.015, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 44, 6.841, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 45, 4.901, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 46, 3.233, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 47, 1.873, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 48, 0.857, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 49, 0.22, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 50, 0, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 51, 0.22, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 52, 0.857, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 53, 1.873, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 54, 3.233, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 55, 4.901, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 56, 6.841, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 57, 9.015, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 58, 11.389, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 59, 13.926, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 60, 16.589, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 61, 19.343, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 62, 22.151, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 63, 24.977, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 64, 27.785, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 65, 30.539, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 66, 33.202, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 67, 35.739, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 68, 38.113, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 69, 40.287, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 70, 42.227, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 71, 43.895, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 72, 45.255, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 73, 46.271, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 74, 46.908, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 75, 47.128, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 76, 46.908, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 77, 46.271, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 78, 45.255, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 79, 43.895, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 80, 42.227, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 81, 40.287, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 82, 38.113, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 83, 35.739, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 84, 33.202, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 85, 30.539, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 86, 27.785, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 87, 24.977, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 88, 22.151, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 89, 19.343, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 90, 16.589, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 91, 13.926, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 92, 11.389, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 93, 9.015, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 94, 6.841, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 95, 4.901, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 96, 3.233, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 97, 1.873, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 98, 0.857, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 99, 0.22, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 100, 0, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 101, 0.22, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 102, 0.857, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 103, 1.873, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 104, 3.233, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 105, 4.901, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 106, 6.841, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 107, 9.015, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 108, 11.389, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 109, 13.926, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 110, 16.589, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 111, 19.343, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 112, 22.151, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 113, 24.977, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 114, 27.785, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 115, 30.539, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 116, 33.202, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 117, 35.739, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 118, 38.113, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 119, 40.287, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 120, 42.227, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 121, 43.895, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 122, 45.255, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 123, 46.271, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 124, 46.908, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 125, 47.128, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 126, 46.908, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 127, 46.271, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 128, 45.255, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 129, 43.895, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 130, 42.227, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 131, 40.287, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 132, 38.113, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 133, 35.739, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 134, 33.202, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 135, 30.539, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 136, 27.785, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 137, 24.977, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 138, 22.151, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 139, 19.343, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 140, 16.589, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 141, 13.926, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 142, 11.389, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 143, 9.015, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 144, 6.841, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 145, 4.901, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 146, 3.233, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 147, 1.873, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 148, 0.857, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
setKeyframeWithEasing(layer_5.property('Transform').property('Rotation'), 149, 0.22, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 47.13, 0.042);
layer_5.property('Transform').property('Anchor Point').setValue([32.56, 36.82, 0]);

// Layer 3: Mouth
var layer_3 = createShapeLayer(comp, 'Mouth');
// Shape Group: Group 1
var layer_3_group_0 = layer_3.property('Contents').addProperty('ADBE Vector Group');
layer_3_group_0.name = 'Group 1';
var layer_3_group_0_path_0 = layer_3_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[2.158, -10.069], [2.158, 10.069]];
pathShape.inTangents = [[0, 0], [-4.315, -5.754]];
pathShape.outTangents = [[0, 0], [0, 0]];
pathShape.closed = false;
layer_3_group_0_path_0.property('Path').setValue(pathShape);
var layer_3_group_0_stroke_1 = layer_3_group_0.property('Contents').addProperty('ADBE Vector Graphic - Stroke');
layer_3_group_0_stroke_1.property('Color').setValue([0.937255, 0.435294, 0.094118]);
layer_3_group_0_stroke_1.property('Stroke Width').setValue(2);
layer_3_group_0_stroke_1.property('Opacity').setValue(100);
var layer_3_group_0_transform = layer_3_group_0.property('Transform');
layer_3_group_0_transform.property('Position').setValue([7.157, 15.069]);
layer_3_group_0_transform.property('Scale').setValue([100, 100]);
layer_3_group_0_transform.property('Rotation').setValue(0);
layer_3_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_3_group_1 = layer_3.property('Contents').addProperty('ADBE Vector Group');
layer_3_group_1.name = 'Group 2';
var layer_3_group_1_path_0 = layer_3_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-20.137, -4.316], [20.137, -4.316]];
pathShape.inTangents = [[0, 0], [-20.137, 8.631]];
pathShape.outTangents = [[0, 0], [0, 0]];
pathShape.closed = false;
layer_3_group_1_path_0.property('Path').setValue(pathShape);
var layer_3_group_1_stroke_1 = layer_3_group_1.property('Contents').addProperty('ADBE Vector Graphic - Stroke');
layer_3_group_1_stroke_1.property('Color').setValue([0.937255, 0.435294, 0.094118]);
layer_3_group_1_stroke_1.property('Stroke Width').setValue(2);
layer_3_group_1_stroke_1.property('Opacity').setValue(100);
var layer_3_group_1_transform = layer_3_group_1.property('Transform');
layer_3_group_1_transform.property('Position').setValue([28.014, 20.822]);
layer_3_group_1_transform.property('Scale').setValue([100, 100]);
layer_3_group_1_transform.property('Rotation').setValue(0);
layer_3_group_1_transform.property('Opacity').setValue(100);
layer_3.inPoint = 0.000000;
layer_3.outPoint = 6.250000;
layer_3.startTime = 0.000000;
layer_3.parent = layer_4;
layer_3.property('Transform').property('Opacity').setValue(100);
layer_3.property('Transform').property('Position').setValue([310.226, 152.002, 0]);
layer_3.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_3.property('Transform').property('Rotation').setValue(0);
layer_3.property('Transform').property('Anchor Point').setValue([26.576, 15.069, 0]);

// Layer 2: Eyes
var layer_2 = createShapeLayer(comp, 'Eyes');
// Shape Group: Group 1
var layer_2_group_0 = layer_2.property('Contents').addProperty('ADBE Vector Group');
layer_2_group_0.name = 'Group 1';
var layer_2_group_0_path_0 = layer_2_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[7.192, 0], [0.001, 7.192], [-7.192, 0], [0.001, -7.192]];
pathShape.inTangents = [[0, -3.972], [3.971, 0], [0, 3.972], [-3.973, 0]];
pathShape.outTangents = [[0, 3.972], [-3.973, 0], [0, -3.972], [3.971, 0]];
pathShape.closed = true;
layer_2_group_0_path_0.property('Path').setValue(pathShape);
var layer_2_group_0_fill_1 = layer_2_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_2_group_0_fill_1.property('Color').setValue([1.000000, 1.000000, 1.000000]);
layer_2_group_0_fill_1.property('Opacity').setValue(100);
var layer_2_group_0_transform = layer_2_group_0.property('Transform');
layer_2_group_0_transform.property('Position').setValue([33.706, 15.978]);
layer_2_group_0_transform.property('Scale').setValue([100, 100]);
layer_2_group_0_transform.property('Rotation').setValue(0);
layer_2_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_2_group_1 = layer_2.property('Contents').addProperty('ADBE Vector Group');
layer_2_group_1.name = 'Group 2';
var layer_2_group_1_path_0 = layer_2_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[20.138, -0.001], [0, 20.137], [-20.138, -0.001], [0, -20.137]];
pathShape.inTangents = [[0, -11.122], [11.121, 0], [0, 11.122], [-11.122, 0]];
pathShape.outTangents = [[0, 11.122], [-11.122, 0], [0, -11.122], [11.121, 0]];
pathShape.closed = true;
layer_2_group_1_path_0.property('Path').setValue(pathShape);
var layer_2_group_1_fill_1 = layer_2_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_2_group_1_fill_1.property('Color').setValue([0.137255, 0.066667, 0.125490]);
layer_2_group_1_fill_1.property('Opacity').setValue(100);
var layer_2_group_1_transform = layer_2_group_1.property('Transform');
layer_2_group_1_transform.property('Position').setValue([27.234, 29.643]);
layer_2_group_1_transform.property('Scale').setValue([100, 100]);
layer_2_group_1_transform.property('Rotation').setValue(0);
layer_2_group_1_transform.property('Opacity').setValue(100);
// Shape Group: Group 3
var layer_2_group_2 = layer_2.property('Contents').addProperty('ADBE Vector Group');
layer_2_group_2.name = 'Group 3';
var layer_2_group_2_path_0 = layer_2_group_2.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[23.258, 4.728], [-5.445, 26.782], [-23.259, -4.728], [5.444, -26.782]];
pathShape.inTangents = [[3.007, -14.792], [12.846, 2.611], [-3.006, 14.791], [-12.845, -2.611]];
pathShape.outTangents = [[-3.007, 14.791], [-12.845, -2.612], [3.007, -14.792], [12.845, 2.611]];
pathShape.closed = true;
layer_2_group_2_path_0.property('Path').setValue(pathShape);
var layer_2_group_2_fill_1 = layer_2_group_2.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_2_group_2_fill_1.property('Color').setValue([1.000000, 1.000000, 1.000000]);
layer_2_group_2_fill_1.property('Opacity').setValue(100);
var layer_2_group_2_transform = layer_2_group_2.property('Transform');
layer_2_group_2_transform.property('Position').setValue([26.515, 29.643]);
layer_2_group_2_transform.property('Scale').setValue([100, 100]);
layer_2_group_2_transform.property('Rotation').setValue(0);
layer_2_group_2_transform.property('Opacity').setValue(100);
layer_2.inPoint = 0.000000;
layer_2.outPoint = 6.250000;
layer_2.startTime = 0.000000;
layer_2.parent = layer_4;
layer_2.property('Transform').property('Opacity').setValue(100);
layer_2.property('Transform').property('Position').setValue([261.321, 99.5, 0]);
// Smart detection: 45 keyframes for layer_2.property('Transform').property('Scale')
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 30, [100, 100, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 31, [100, 94.461, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 32, [100, 80.175, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 33, [100, 60.641, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 34, [100, 39.359, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 35, [100, 19.825, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 36, [100, 5.539, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 37, [100, 0, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 38, [100, 4.297, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 39, [100, 15.625, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 40, [100, 31.641, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 42, [100, 68.359, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 43, [100, 84.375, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 44, [100, 95.703, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 45, [100, 100, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 75, [100, 100, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 76, [100, 94.461, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 77, [100, 80.175, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 78, [100, 60.641, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 79, [100, 39.359, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 80, [100, 19.825, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 81, [100, 5.539, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 82, [100, 0, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 83, [100, 4.297, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 84, [100, 15.625, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 85, [100, 31.641, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 87, [100, 68.359, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 88, [100, 84.375, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 89, [100, 95.703, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 90, [100, 100, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 120, [100, 100, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 121, [100, 94.461, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 122, [100, 80.175, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 123, [100, 60.641, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 124, [100, 39.359, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 125, [100, 19.825, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 126, [100, 5.539, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 127, [100, 0, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 128, [100, 4.297, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 129, [100, 15.625, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 130, [100, 31.641, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 132, [100, 68.359, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 133, [100, 84.375, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 134, [100, 95.703, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
setKeyframeWithEasing(layer_2.property('Transform').property('Scale'), 135, [100, 100, 100], {x: [1, 1, 1], y: [1, 1, 1]}, {x: [0, 0, 0], y: [0, 0, 0]}, frameRate, 41.42, 0.099);
layer_2.property('Transform').property('Rotation').setValue(0);
layer_2.property('Transform').property('Anchor Point').setValue([26.515, 29.643, 0]);

// Layer 1: Body
var layer_1 = createShapeLayer(comp, 'Body');
// Shape Group: Group 2
var layer_1_group_0 = layer_1.property('Contents').addProperty('ADBE Vector Group');
layer_1_group_0.name = 'Group 2';
var layer_1_group_0_path_0 = layer_1_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-33.921, 15.147], [-8.679, -0.374], [18.128, -11.654], [34.32, -14.458], [34.32, -15.458], [6.052, -8.557], [-20.202, 5.185], [-34.426, 14.285]];
pathShape.inTangents = [[-0.541, 0.354], [-8.675, 4.735], [-9.379, 2.584], [-5.48, 0.373], [0.643, -0.043], [9.022, -3.689], [8.42, -5.177], [4.71, -3.082]];
pathShape.outTangents = [[8.265, -5.408], [8.521, -4.653], [5.293, -1.458], [0.638, -0.043], [-9.746, 0.662], [-9.161, 3.744], [-4.795, 2.948], [-0.536, 0.351]];
pathShape.closed = true;
layer_1_group_0_path_0.property('Path').setValue(pathShape);
var layer_1_group_0_fill_1 = layer_1_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_1_group_0_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_1_group_0_fill_1.property('Opacity').setValue(100);
var layer_1_group_0_transform = layer_1_group_0.property('Transform');
layer_1_group_0_transform.property('Position').setValue([128.525, 163.727]);
layer_1_group_0_transform.property('Scale').setValue([100, 100]);
layer_1_group_0_transform.property('Rotation').setValue(0);
layer_1_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 3
var layer_1_group_1 = layer_1.property('Contents').addProperty('ADBE Vector Group');
layer_1_group_1.name = 'Group 3';
var layer_1_group_1_path_0 = layer_1_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-41.177, 7.117], [-9.763, -2.396], [22.955, -6.037], [41.39, -5.435], [41.39, -6.435], [8.453, -6.173], [-23.909, 0.068], [-41.443, 6.152]];
pathShape.inTangents = [[-0.597, 0.242], [-10.734, 2.203], [-10.987, 0.211], [-6.129, -0.516], [0.638, 0.054], [10.943, -1.096], [10.568, -3.06], [5.735, -2.328]];
pathShape.outTangents = [[10.153, -4.123], [10.766, -2.211], [6.151, -0.117], [0.641, 0.054], [-10.957, -0.924], [-10.946, 1.096], [-5.946, 1.723], [-0.588, 0.239]];
pathShape.closed = true;
layer_1_group_1_path_0.property('Path').setValue(pathShape);
var layer_1_group_1_fill_1 = layer_1_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_1_group_1_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_1_group_1_fill_1.property('Opacity').setValue(100);
var layer_1_group_1_transform = layer_1_group_1.property('Transform');
layer_1_group_1_transform.property('Position').setValue([123.523, 154.908]);
layer_1_group_1_transform.property('Scale').setValue([100, 100]);
layer_1_group_1_transform.property('Rotation').setValue(0);
layer_1_group_1_transform.property('Opacity').setValue(100);
// Shape Group: Group 4
var layer_1_group_2 = layer_1.property('Contents').addProperty('ADBE Vector Group');
layer_1_group_2.name = 'Group 4';
var layer_1_group_2_path_0 = layer_1_group_2.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-40.66, -3.952], [-8.785, -4.108], [23.044, 1.571], [40.671, 5.9], [40.936, 4.935], [9.109, -2.416], [-22.865, -5.974], [-40.926, -4.916]];
pathShape.inTangents = [[-0.63, 0.085], [-10.587, -1.118], [-10.5, -2.437], [-5.867, -1.484], [0.624, 0.158], [10.701, 2.032], [10.742, 0.147], [5.986, -0.808]];
pathShape.outTangents = [[10.566, -1.427], [10.728, 1.132], [5.893, 1.37], [0.624, 0.157], [-10.556, -2.669], [-10.542, -2.002], [-6.038, -0.083], [-0.635, 0.085]];
pathShape.closed = true;
layer_1_group_2_path_0.property('Path').setValue(pathShape);
var layer_1_group_2_fill_1 = layer_1_group_2.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_1_group_2_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_1_group_2_fill_1.property('Opacity').setValue(100);
var layer_1_group_2_transform = layer_1_group_2.property('Transform');
layer_1_group_2_transform.property('Position').setValue([121.927, 144.222]);
layer_1_group_2_transform.property('Scale').setValue([100, 100]);
layer_1_group_2_transform.property('Rotation').setValue(0);
layer_1_group_2_transform.property('Opacity').setValue(100);
// Shape Group: Group 5
var layer_1_group_3 = layer_1.property('Contents').addProperty('ADBE Vector Group');
layer_1_group_3.name = 'Group 5';
var layer_1_group_3_path_0 = layer_1_group_3.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-36.607, -11.058], [-8.099, -4.797], [5.548, 2.411], [19.763, 8.474], [36.6, 13.685], [36.867, 12.72], [6.987, 1.997], [-6.793, -5.235], [-20.723, -11.396], [-36.874, -12.022]];
pathShape.inTangents = [[-0.622, 0.175], [-8.645, -4.568], [-4.634, -2.246], [-4.831, -1.792], [-5.702, -1.429], [0.625, 0.157], [9.571, 4.561], [4.579, 2.445], [4.866, 1.485], [5.338, -1.496]];
pathShape.outTangents = [[9.933, -2.783], [4.55, 2.404], [4.636, 2.247], [5.512, 2.046], [0.625, 0.156], [-10.284, -2.575], [-4.688, -2.236], [-4.476, -2.39], [-5.249, -1.602], [-0.619, 0.173]];
pathShape.closed = true;
layer_1_group_3_path_0.property('Path').setValue(pathShape);
var layer_1_group_3_fill_1 = layer_1_group_3.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_1_group_3_fill_1.property('Color').setValue([0.450980, 0.239216, 0.545098]);
layer_1_group_3_fill_1.property('Opacity').setValue(100);
var layer_1_group_3_transform = layer_1_group_3.property('Transform');
layer_1_group_3_transform.property('Position').setValue([124.683, 134.086]);
layer_1_group_3_transform.property('Scale').setValue([100, 100]);
layer_1_group_3_transform.property('Rotation').setValue(0);
layer_1_group_3_transform.property('Opacity').setValue(100);
// Shape Group: Group 6
var layer_1_group_4 = layer_1.property('Contents').addProperty('ADBE Vector Group');
layer_1_group_4.name = 'Group 6';
var layer_1_group_4_path_0 = layer_1_group_4.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[41.715, 20.138], [35.96, -4.314], [27.33, -4.314], [-37.397, -25.89], [-37.397, 30.206], [-15.822, 40.276], [20.07, 23.88]];
pathShape.inTangents = [[-7.429, -0.929], [12.945, 4.315], [0, 0], [8.63, -18.7], [-11.508, -21.576], [-12.945, 4.314], [-10.824, 5.449]];
pathShape.outTangents = [[0, 0], [0, 0], [0, 0], [-8.631, 18.699], [0, 0], [8.348, -2.784], [6.687, -3.366]];
pathShape.closed = true;
layer_1_group_4_path_0.property('Path').setValue(pathShape);
var layer_1_group_4_fill_1 = layer_1_group_4.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_1_group_4_fill_1.property('Color').setValue([0.972549, 0.305882, 0.192157]);
layer_1_group_4_fill_1.property('Opacity').setValue(100);
var layer_1_group_4_transform = layer_1_group_4.property('Transform');
layer_1_group_4_transform.property('Position').setValue([121.075, 141.933]);
layer_1_group_4_transform.property('Scale').setValue([100, 100]);
layer_1_group_4_transform.property('Rotation').setValue(0);
layer_1_group_4_transform.property('Opacity').setValue(100);
layer_1.inPoint = 0.000000;
layer_1.outPoint = 6.250000;
layer_1.startTime = 0.000000;
layer_1.parent = layer_4;
layer_1.property('Transform').property('Opacity').setValue(100);
layer_1.property('Transform').property('Position').setValue([164.74, 147.053, 0]);
layer_1.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 150 keyframes for layer_1.property('Transform').property('Rotation')
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 0, 0, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 1, -0.145, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 2, -0.562, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 3, -1.229, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 4, -2.122, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 5, -3.217, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 6, -4.49, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 7, -5.917, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 8, -7.475, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 9, -9.14, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 10, -10.888, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 11, -12.696, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 12, -14.539, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 13, -16.394, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 14, -18.237, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 15, -20.044, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 16, -21.792, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 17, -23.457, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 18, -25.015, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 19, -26.443, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 20, -27.716, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 21, -28.81, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 22, -29.703, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 23, -30.37, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 24, -30.788, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 25, -30.933, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 26, -30.788, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 27, -30.37, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 28, -29.703, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 29, -28.81, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 30, -27.716, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 31, -26.443, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 32, -25.015, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 33, -23.457, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 34, -21.792, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 35, -20.044, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 36, -18.237, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 37, -16.394, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 38, -14.539, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 39, -12.696, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 40, -10.888, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 41, -9.14, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 42, -7.475, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 43, -5.917, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 44, -4.49, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 45, -3.217, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 46, -2.122, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 47, -1.229, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 48, -0.562, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 49, -0.145, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 50, 0, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 51, -0.145, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 52, -0.562, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 53, -1.229, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 54, -2.122, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 55, -3.217, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 56, -4.49, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 57, -5.917, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 58, -7.475, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 59, -9.14, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 60, -10.888, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 61, -12.696, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 62, -14.539, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 63, -16.394, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 64, -18.237, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 65, -20.044, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 66, -21.792, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 67, -23.457, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 68, -25.015, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 69, -26.443, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 70, -27.716, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 71, -28.81, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 72, -29.703, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 73, -30.37, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 74, -30.788, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 75, -30.933, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 76, -30.788, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 77, -30.37, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 78, -29.703, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 79, -28.81, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 80, -27.716, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 81, -26.443, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 82, -25.015, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 83, -23.457, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 84, -21.792, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 85, -20.044, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 86, -18.237, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 87, -16.394, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 88, -14.539, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 89, -12.696, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 90, -10.888, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 91, -9.14, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 92, -7.475, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 93, -5.917, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 94, -4.49, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 95, -3.217, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 96, -2.122, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 97, -1.229, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 98, -0.562, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 99, -0.145, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 100, 0, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 101, -0.145, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 102, -0.562, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 103, -1.229, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 104, -2.122, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 105, -3.217, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 106, -4.49, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 107, -5.917, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 108, -7.475, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 109, -9.14, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 110, -10.888, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 111, -12.696, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 112, -14.539, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 113, -16.394, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 114, -18.237, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 115, -20.044, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 116, -21.792, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 117, -23.457, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 118, -25.015, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 119, -26.443, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 120, -27.716, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 121, -28.81, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 122, -29.703, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 123, -30.37, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 124, -30.788, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 125, -30.933, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 126, -30.788, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 127, -30.37, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 128, -29.703, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 129, -28.81, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 130, -27.716, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 131, -26.443, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 132, -25.015, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 133, -23.457, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 134, -21.792, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 135, -20.044, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 136, -18.237, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 137, -16.394, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 138, -14.539, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 139, -12.696, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 140, -10.888, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 141, -9.14, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 142, -7.475, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 143, -5.917, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 144, -4.49, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 145, -3.217, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 146, -2.122, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 147, -1.229, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 148, -0.562, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
setKeyframeWithEasing(layer_1.property('Transform').property('Rotation'), 149, -0.145, {x: [1], y: [1]}, {x: [0], y: [0]}, frameRate, 30.93, 0.042);
layer_1.property('Transform').property('Anchor Point').setValue([164.74, 147.053, 0]);


// Set up track matte relationships

// Animation creation complete
alert('Animation "07" created successfully!\n' +
      'Duration: 6.00 seconds\n' +
      'Layers: 10\n' +
      'Assets: 0');

app.endUndoGroup();